apiVersion: k8s.cni.cncf.io/v1
kind: NetworkAttachmentDefinition
metadata:
  annotations:
    network.harvesterhci.io/route: >-
      {"mode":"auto","cidr":"10.134.26.0/27","gateway":"10.134.26.1","connectivity":"false"}
  creationTimestamp: '2025-02-26T21:38:32Z'
  finalizers:
    - wrangler.cattle.io/harvester-network-nad-controller
    - wrangler.cattle.io/harvester-network-manager-nad-controller
  generation: 1
  labels:
    network.harvesterhci.io/clusternetwork: mgmt
    network.harvesterhci.io/ready: 'true'
    network.harvesterhci.io/type: L2VlanNetwork
    network.harvesterhci.io/vlan-id: '3584'
  managedFields:
    - apiVersion: k8s.cni.cncf.io/v1
      fieldsType: FieldsV1
      fieldsV1:
        f:metadata:
          f:annotations: {}
        f:spec:
          .: {}
          f:config: {}
      manager: harvester
      operation: Update
      time: '2025-02-26T21:38:32Z'
    - apiVersion: k8s.cni.cncf.io/v1
      fieldsType: FieldsV1
      fieldsV1:
        f:metadata:
          f:annotations:
            f:network.harvesterhci.io/route: {}
          f:finalizers:
            .: {}
            v:"wrangler.cattle.io/harvester-network-manager-nad-controller": {}
            v:"wrangler.cattle.io/harvester-network-nad-controller": {}
          f:labels:
            .: {}
            f:network.harvesterhci.io/clusternetwork: {}
            f:network.harvesterhci.io/ready: {}
            f:network.harvesterhci.io/type: {}
            f:network.harvesterhci.io/vlan-id: {}
      manager: harvester-network-controller
      operation: Update
      time: '2025-02-26T21:38:46Z'
  name: medp-proj-cde-net
  namespace: medp-proj-cde-ns
  resourceVersion: '602229832'
  uid: f0e8376c-5439-418d-b661-33692df8b893
spec:
  config: >-
    {"cniVersion":"0.3.1","name":"medp-proj-cde-net","type":"bridge","bridge":"mgmt-br","promiscMode":true,"vlan":3584,"ipam":{}}
