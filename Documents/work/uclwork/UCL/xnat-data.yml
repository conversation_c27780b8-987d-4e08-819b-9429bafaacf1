apiVersion: kubevirt.io/v1
kind: VirtualMachineInstance
metadata:
  annotations:
    harvesterhci.io/sshNames: '[]'
    harvesterhci.io/waitForLeaseInterfaceNames: '["nic-1"]'
    kubevirt.io/latest-observed-api-version: v1
    kubevirt.io/storage-observed-api-version: v1
    kubevirt.io/vm-generation: "2"
  creationTimestamp: "2025-03-17T14:38:07Z"
  finalizers:
  - kubevirt.io/virtualMachineControllerFinalize
  - foregroundDeleteVirtualMachine
  - wrangler.cattle.io/harvester-lb-vmi-controller
  - wrangler.cattle.io/VMIController.UnsetOwnerOfPVCs
  generation: 69
  labels:
    harvesterhci.io/creator: terraform-provider-harvester
    harvesterhci.io/vmName: xnat-data
    kubevirt.io/nodeName: harvester-mh9pg
    tag.harvesterhci.io/os: linux
  name: xnat-data
  namespace: medp-proj-cde-ns
  ownerReferences:
  - apiVersion: kubevirt.io/v1
    blockOwnerDeletion: true
    controller: true
    kind: VirtualMachine
    name: xnat-data
    uid: b45522af-ad45-4435-84d5-9b3755623acf
  resourceVersion: "665301582"
  uid: b8aca496-11fa-41da-8c28-fb6dd432eca1
spec:
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - key: network.harvesterhci.io/mgmt
            operator: In
            values:
            - "true"
    podAntiAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
      - podAffinityTerm:
          labelSelector:
            matchExpressions:
            - key: harvesterhci.io/creator
              operator: Exists
          topologyKey: kubernetes.io/hostname
        weight: 100
  architecture: amd64
  domain:
    cpu:
      cores: 2
      model: host-model
    devices:
      disks:
      - bootOrder: 1
        disk:
          bus: virtio
        name: disk-0
      - disk:
          bus: virtio
        name: cloudinitdisk
      interfaces:
      - bridge: {}
        model: virtio
        name: nic-1
    features:
      acpi:
        enabled: true
      smm:
        enabled: true
    firmware:
      bootloader:
        efi:
          secureBoot: true
      uuid: ab428584-342f-528b-8978-72b7945d1368
    machine:
      type: q35
    memory:
      guest: 10140Mi
    resources:
      limits:
        cpu: "2"
        memory: 10Gi
      requests:
        cpu: 125m
        memory: 6826Mi
  evictionStrategy: LiveMigrateIfPossible
  hostname: xnat-data
  networks:
  - multus:
      networkName: medp-proj-cde-ns/medp-proj-cde-net
    name: nic-1
  terminationGracePeriodSeconds: 120
  volumes:
  - name: disk-0
    persistentVolumeClaim:
      claimName: xnat-data-disk-0-27wnp
  - cloudInitNoCloud:
      secretRef:
        name: cloud-config-51ec4278
    name: cloudinitdisk
status:
  activePods:
    221b2a0c-afaa-4e2c-a97c-9672a473997d: harvester-mh9pg
  conditions:
  - lastProbeTime: null
    lastTransitionTime: "2025-03-17T14:38:34Z"
    status: "True"
    type: Ready
  - lastProbeTime: null
    lastTransitionTime: null
    status: "True"
    type: LiveMigratable
  - lastProbeTime: "2025-03-18T06:59:38Z"
    lastTransitionTime: null
    status: "True"
    type: AgentConnected
  currentCPUTopology:
    cores: 2
    sockets: 1
    threads: 1
  guestOSInfo:
    id: ubuntu
    kernelRelease: 6.8.0-49-generic
    kernelVersion: '#49-Ubuntu SMP PREEMPT_DYNAMIC Mon Nov  4 02:06:24 UTC 2024'
    name: Ubuntu
    prettyName: Ubuntu 24.04.1 LTS
    version: "24.04"
    versionId: "24.04"
  interfaces:
  - infoSource: domain, guest-agent, multus-status
    interfaceName: enp1s0
    ipAddress: ***********
    ipAddresses:
    - ***********
    - fe80::fcec:53ff:fe35:dfa8
    mac: fe:ec:53:35:df:a8
    name: nic-1
    queueCount: 1
  launcherContainerImageVersion: registry.suse.com/suse/sles/15.5/virt-launcher:1.1.0-150500.8.6.1
  machine:
    type: pc-q35-7.1
  memory:
    guestAtBoot: 10140Mi
    guestCurrent: 10140Mi
    guestRequested: 10140Mi
  migrationMethod: BlockMigration
  migrationTransport: Unix
  nodeName: harvester-mh9pg
  phase: Running
  phaseTransitionTimestamps:
  - phase: Pending
    phaseTransitionTimestamp: "2025-03-17T14:38:07Z"
  - phase: Scheduling
    phaseTransitionTimestamp: "2025-03-17T14:38:08Z"
  - phase: Scheduled
    phaseTransitionTimestamp: "2025-03-17T14:38:34Z"
  - phase: Running
    phaseTransitionTimestamp: "2025-03-17T14:38:38Z"
  qosClass: Burstable
  runtimeUser: 107
  selinuxContext: none
  virtualMachineRevisionName: revision-start-vm-b45522af-ad45-4435-84d5-9b3755623acf-1
  volumeStatus:
  - name: cloudinitdisk
    size: 1048576
    target: vdb
  - name: disk-0
    persistentVolumeClaimInfo:
      accessModes:
      - ReadWriteMany
      capacity:
        storage: 100Gi
      filesystemOverhead: "0.055"
      requests:
        storage: 100Gi
      volumeMode: Block
    target: vda
