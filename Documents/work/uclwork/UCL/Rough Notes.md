Articles
https://orange.hosting.lsoft.com/trk/click?ref=znwrbbrs9_6-21899x31dbdax071184&
Technion-Israel Institute of Technology researchers have developed an artificial intelligence (AI)-based technique for mapping receptors on cancer cells. The technique derives molecular data from images of breast cancer biopsies that received hematoxylin and eosin staining, extracting biological properties to help pathologists match patients with a therapy that will block receptors on the cell membrane and hinder tumor growth. Technion's <PERSON> and <PERSON> said the deep learning technique successfully identifies cancer's unique morphological signature within tissue. <PERSON><PERSON><PERSON> said to feed the AI system the necessary cancer information, "We have written software code to scan network sources and automatically download thousands of biopsy samples and the relevant medical information approved for research."

https://orange.hosting.lsoft.com/trk/click?ref=znwrbbrs9_6-21899x31dbe2x071184&
Researchers at the University of Vienna in Austria have developed a method of using artificial neural networks to accelerate the simulation of light-induced processes significantly. The approach provides new possibilities for a better understanding of biological processes, such as the first steps of carcinogenesis or the aging processes of matter. The team taught the system complex quantum-mechanical relationships by performing some calculations beforehand and passing that knowledge on to the neural network. As part of the study, the researchers completed photodynamics simulations of a test molecule called methylenimmonium cation. After two months of computing, the researchers were able to reproduce the reaction for a nanosecond; using previous methods, the simulation would have taken about 19 years.

https://www.regenstrief.org/article/ehrs-cancer-symptom-clusters/
Patients with chronic diseases such as breast cancer or colorectal cancer often experience fatigue, pain, depression and other symptoms which can lead to distress and functional impairment if left untreated. With the ultimate goal of helping clinicians manage and treat symptoms that negatively affect health and quality of life, researchers from the Regenstrief Institute and IUPUI have devised and tested novel methodologies to extract data on symptoms from electronic health records (EHRs) and have successfully investigated associations between symptom clusters and disease.

Redcap, clinical trials software.
Central database with input like - patient data, clinical data, family history, pathology reports and observations, DICOM diagnostic images, electronic health records, etc.
EHR (electronic health records) - EPIC
NCRAS - some patient data
Do business analysis on the software product to develop the central database of patients.

Gauge is a free and open source framework for writing and running acceptance tests.
https://docs.gauge.org/getting_started/create-test-project.html?os=macos&language=javascript&ide=vscode

DBpedia: Global and Unified Access to Knowledge
https://wiki.dbpedia.org/
https://github.com/dbpedia?page=2

Progranming as intended.
Programming Language, Tool Ensure Code Will Compute as Intended
Carnegie Mellon University CyLab Security and Privacy Institute
Daniel Tkacik
July 29, 2020

A multi-institutional team of researchers led by Carnegie Mellon University (CMU) published Armada, a coding language and tool for high-performance concurrent programs that ensure code is mathematically proven to compute as intended. Concurrent programming requires complex coordination of multiple simultaneous executables to avoid interference, which can be a buggy process. CMU's Bryan Parno said, "From payroll systems to hospital records to any kind of e-commerce—they are all backed by databases, and databases are always going to be backed by concurrent software. Aside from simple programs, these days almost everything has some sort of concurrency to it." Armada has the flexibility to let users write code however they desire to run as fast as possible, while still ensuring it is provably correct.

