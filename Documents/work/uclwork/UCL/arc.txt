# Configuring a VM for SSH access
Set Kubenetes config.

export KUBECONFIG=sl-p01.yaml

Get contexts.

kubectl config get-contexts
kubectl config current-context

Set namespace.

kubectl config set-context --current --namespace=medp-proj-cde-ns
kubectl config view --minify | grep namespace:

List configured VMs.
kubectl --insecure-skip-tls-verify get virtualmachineinstance.kubevirt.io --namespace medp-proj-cde-ns

Warning: Use tokens from the TokenRequest API or manually created secret-based tokens instead of auto-generated secret-based tokens.
NAME        AGE     PHASE     IP            NODENAME          READY
etl-vm-01   7h40m   Running   ***********   harvester-2f8q4   True
xnat-data   4m45s   Running                 harvester-vd4sf   True
xnat-web    4m44s   Running                 harvester-2f8q4   True

OR

kubectl --insecure-skip-tls-verify get vmi -n medp-proj-cde-ns -o wide

Warning: Use tokens from the TokenRequest API or manually created secret-based tokens instead of auto-generated secret-based tokens.
NAME        AGE     PHASE     IP            NODENAME          READY   LIVE-MIGRATABLE   PAUSED
etl-vm-01   16d     Running   ***********   harvester-2f8q4   True    True
xnat-data   6d20h   Running   ***********   harvester-n8s7z   True    True
xnat-web    6d20h   Running   ***********   harvester-bpqwj   True    True

OR

kubectl --insecure-skip-tls-verify get virtualmachineinstance.kubevirt.io -o wide
kubectl --insecure-skip-tls-verify get virtualmachineinstance.kubevirt.io etl-vm-01 -o wide
kubectl --insecure-skip-tls-verify get virtualmachineinstance.kubevirt.io -o yaml
kubectl --insecure-skip-tls-verify get virtualmachineinstance.kubevirt.io etl-vm-01 -o yaml

Get VM config.

kubectl --insecure-skip-tls-verify get vm -n medp-proj-cde-ns -o wide
kubectl --insecure-skip-tls-verify get vm -n medp-proj-cde-ns -o yaml

Connect to the VM console.

virtctl --insecure-skip-tls-verify console etl-vm-01 --namespace medp-proj-cde-ns
virtctl --insecure-skip-tls-verify console xnat-data --namespace medp-proj-cde-ns

List configured networks.

kubectl --insecure-skip-tls-verify get network-attachment-definitions

Warning: Use tokens from the TokenRequest API or manually created secret-based tokens instead of auto-generated secret-based tokens.
NAME                AGE
medp-proj-cde-net   19d

kubectl --insecure-skip-tls-verify get network-attachment-definitions medp-proj-cde-net -o yaml

Get virtual machine network configuration.

kubectl --insecure-skip-tls-verify get virtualmachinenetworkconfig -o wide
kubectl --insecure-skip-tls-verify get ippool -o wide
kubectl --insecure-skip-tls-verify get addresspool -o wide
kubectl --insecure-skip-tls-verify get ipaddresspool -o wide

Describe resources.

kubectl --insecure-skip-tls-verify get api-resources
kubectl describe <resource-type> <resource-name> -n <namespace>

Get secrets.

kubectl --insecure-skip-tls-verify get secrets

Warning: Use tokens from the TokenRequest API or manually created secret-based tokens instead of auto-generated secret-based tokens.
NAME                    TYPE     DATA   AGE
etl-vm-01-bwqva         secret   2      4d
network-test-bm-6zrdv   secret   2      5d18h

Get config.

kubectl --insecure-skip-tls-verify get configmaps

Warning: Use tokens from the TokenRequest API or manually created secret-based tokens instead of auto-generated secret-based tokens.
NAME               DATA   AGE
kube-root-ca.crt   1      7d20h

Events.

kubectl --insecure-skip-tls-verify get events -n medp-proj-cde-ns --sort-by=.metadata.creationTimestamp

Labels.

kubectl --insecure-skip-tls-verify get nodes --show-labels -n medp-proj-cde-ns

Delete virtual machine.

Using kubectl.
kubectl --insecure-skip-tls-verify delete virtualmachine xnat-compute-01 -n medp-proj-cde-ns

Using API.

Delete:
curl -X DELETE https://rancher.condenser.arc.ucl.ac.uk/apis/kubevirt.io/v1/namespaces/namespace:medp-proj-cde-ns/virtualmachines/name:xnat-compute-01

Get pods.

kubectl --insecure-skip-tls-verify get pods -n medp-proj-cde-ns

Warning: Use tokens from the TokenRequest API or manually created secret-based tokens instead of auto-generated secret-based tokens.
NAME                            READY   STATUS    RESTARTS   AGE
virt-launcher-etl-vm-01-bx6zk   2/2     Running   0          16d
virt-launcher-xnat-data-4bnc9   2/2     Running   0          6d21h
virt-launcher-xnat-web-g4ksb    2/2     Running   0          6d21h

OR

kubectl --insecure-skip-tls-verify get pods -n medp-proj-cde-ns -o wide

Warning: Use tokens from the TokenRequest API or manually created secret-based tokens instead of auto-generated secret-based tokens.
NAME                            READY   STATUS    RESTARTS   AGE     IP             NODE              NOMINATED NODE   READINESS GATES
virt-launcher-etl-vm-01-bx6zk   2/2     Running   0          16d     ***********    harvester-2f8q4   <none>           1/1
virt-launcher-xnat-data-4bnc9   2/2     Running   0          7d6h    ************   harvester-n8s7z   <none>           1/1
virt-launcher-xnat-web-qbw4h    2/2     Running   0          5h47m   ************   harvester-xzx9r   <none>           1/1

Use kubectl to execute commands inside the pod.

kubectl --insecure-skip-tls-verify exec -it virt-launcher-xnat-data-mcl2x -- curl -I https://www.google.com
kubectl --insecure-skip-tls-verify exec -it virt-launcher-xnat-data-mcl2x -- /bin/bash

kubectl --insecure-skip-tls-verify exec -it virt-launcher-xnat-web-qbw4h -- curl -I https://www.google.com
kubectl --insecure-skip-tls-verify exec -it virt-launcher-xnat-web-qbw4h -- ping ***********
kubectl --insecure-skip-tls-verify exec -it virt-launcher-xnat-web-qbw4h -- /bin/bash

Check the pod's logs for any outbound connection information.

kubectl --insecure-skip-tls-verify get pods -n medp-proj-cde-ns -o wide
kubectl --insecure-skip-tls-verify describe pod virt-launcher-xnat-web-qbw4h -n medp-proj-cde-ns
kubectl --insecure-skip-tls-verify top pod virt-launcher-xnat-web-qbw4h --containers -n medp-proj-cde-ns

NAME                            READY   STATUS    RESTARTS   AGE     IP             NODE              NOMINATED NODE   READINESS GATES
virt-launcher-etl-vm-01-bx6zk   2/2     Running   0          48d     ***********    harvester-2f8q4   <none>           1/1
virt-launcher-xnat-data-htj7k   2/2     Running   0          4h38m   ************   harvester-pv8c7   <none>           1/1
virt-launcher-xnat-web-hpzrn    2/2     Running   0          4h39m   ************   harvester-dmhnx   <none>           1/1

kubectl --insecure-skip-tls-verify logs virt-launcher-xnat-web-hpzrn -n medp-proj-cde-ns
kubectl --insecure-skip-tls-verify logs virt-launcher-xnat-web-hpzrn -c compute -n medp-proj-cde-ns
kubectl --insecure-skip-tls-verify logs virt-launcher-xnat-web-hpzrn -c guest-console-log -n medp-proj-cde-ns

Get services.

kubectl --insecure-skip-tls-verify get svc -n medp-proj-cde-ns -o wide

cat << EOF > xnat-web-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: xnat-web-service
  namespace: medp-proj-cde-ns
spec:
  selector:
    kubevirt.io/vm: xnat-web
  ports:
  - protocol: TCP
    port: 80
    targetPort: 80
  type: ClusterIP
EOF

Delete:
kubectl --insecure-skip-tls-verify delete -f xnat-web-service.yaml -n medp-proj-cde-ns
Create:
kubectl --insecure-skip-tls-verify apply -f xnat-web-service.yaml -n medp-proj-cde-ns

kubectl --insecure-skip-tls-verify get svc -n medp-proj-cde-ns -o wide
kubectl --insecure-skip-tls-verify get svc xnat-web-service -n medp-proj-cde-ns -o wide

Warning: Use tokens from the TokenRequest API or manually created secret-based tokens instead of auto-generated secret-based tokens.
NAME               TYPE        CLUSTER-IP     EXTERNAL-IP   PORT(S)   AGE
xnat-web-service   ClusterIP   ************   <none>        80/TCP    61m

kubectl --insecure-skip-tls-verify describe svc xnat-web-service -n medp-proj-cde-ns

Warning: Use tokens from the TokenRequest API or manually created secret-based tokens instead of auto-generated secret-based tokens.
Name:                     xnat-web-service
Namespace:                medp-proj-cde-ns
Labels:                   <none>
Annotations:              <none>
Selector:                 kubevirt.io/vm=xnat-web
Type:                     ClusterIP
IP Family Policy:         SingleStack
IP Families:              IPv4
IP:                       ************
IPs:                      ************
Port:                     <unset>  80/TCP
TargetPort:               80/TCP
Endpoints:
Session Affinity:         None
Internal Traffic Policy:  Cluster
Events:                   <none>

Endpoints.

kubectl --insecure-skip-tls-verify get endpoints -n medp-proj-cde-ns -o wide

cat << EOF > xnat-web-endpoint.yaml
apiVersion: v1
kind: Endpoints
metadata:
  name: xnat-web-service
  namespace: medp-proj-cde-ns
subsets:
- addresses:
  - ip: ***********
  ports:
  - port: 80
    protocol: TCP
EOF

Delete:
kubectl --insecure-skip-tls-verify delete -f xnat-web-endpoint.yaml -n medp-proj-cde-ns
Create:
kubectl --insecure-skip-tls-verify apply -f xnat-web-endpoint.yaml -n medp-proj-cde-ns

kubectl --insecure-skip-tls-verify get endpoints -n medp-proj-cde-ns -o wide
kubectl --insecure-skip-tls-verify get endpoints xnat-web-service -n medp-proj-cde-ns -o wide

Warning: Use tokens from the TokenRequest API or manually created secret-based tokens instead of auto-generated secret-based tokens.
NAME               ENDPOINTS        AGE
xnat-web-service   ***********:80   31m

kubectl --insecure-skip-tls-verify describe endpoints xnat-web-service -n medp-proj-cde-ns

Ingress.

kubectl --insecure-skip-tls-verify get pods -n medp-proj-cde-ns -o wide
kubectl --insecure-skip-tls-verify logs virt-launcher-xnat-web-hpzrn -n medp-proj-cde-ns --v=3
kubectl --insecure-skip-tls-verify sniff virt-launcher-xnat-web-hpzrn -o capture.pcap


Needs to be enabled by the admin, and configured as recommended in the tutorial - https://github.com/UCL-ARC/condenser-mkdocs/blob/main/docs/developer_guide/web_ingress.md

Once configured, you can use the external DNS address to point to the host's proxy, as given below.

host etl-vm-01.medp-proj-cde.condenser.arc.ucl.ac.uk
etl-vm-01.medp-proj-cde.condenser.arc.ucl.ac.uk has address **********
host xnat-web.medp-proj-cde.condenser.arc.ucl.ac.uk
xnat-web.medp-proj-cde.condenser.arc.ucl.ac.uk has address **********

LoadBalancer IP: **********
ClusterIP: ************

kubectl --insecure-skip-tls-verify get ingress -n medp-proj-cde-ns -o wide

cat << EOF > xnat-web-ingress.yaml
apiVersion: networking.k8s.io/v1
 AscendingNodePort: true
kind: Ingress
metadata:
  name: xnat-web-ingress
  namespace: medp-proj-cde-ns
spec:
  rules:
  - host: xnat-web.medp-proj-cde.condenser.arc.ucl.ac.uk
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: xnat-web-service
            port:
              number: 80
  # Optional: Specify Ingress class if required by your cluster
  ingressClassName: nginx
EOF

Delete:
kubectl --insecure-skip-tls-verify delete -f xnat-web-ingress.yaml -n medp-proj-cde-ns
Create:
kubectl --insecure-skip-tls-verify apply -f xnat-web-ingress.yaml -n medp-proj-cde-ns

kubectl --insecure-skip-tls-verify get ingress -n medp-proj-cde-ns -o wide
kubectl --insecure-skip-tls-verify get ingress xnat-web-ingress -n medp-proj-cde-ns -o wide

Warning: Use tokens from the TokenRequest API or manually created secret-based tokens instead of auto-generated secret-based tokens.
NAME               CLASS   HOSTS                                            ADDRESS PORTS   AGE
xnat-web-ingress   nginx   xnat-web.medp-proj-cde.condenser.arc.ucl.ac.uk   ************,************,************,************,************,************,************,************,************,************,************,************,************,************,************,************,************,************,************,************,************,************,************,************,************,************,************,************,************,************,************,************,************,************,************,************,************,************,************,************,************,************,************,************,************   80      57m

kubectl --insecure-skip-tls-verify describe ingress xnat-web-ingress -n medp-proj-cde-ns



kubectl --insecure-skip-tls-verify get pods -n medp-proj-cde-ns -o wide

kubectl --insecure-skip-tls-verify logs -n ingress-nginx virt-launcher-xnat-web-76cxp
kubectl --insecure-skip-tls-verify get deployments -n medp-proj-cde-ns
kubectl --insecure-skip-tls-verify get services -n medp-proj-cde-ns
kubectl --insecure-skip-tls-verify get networkpolicies -n medp-proj-cde-ns

Network Debugging.

kubectl --insecure-skip-tls-verify run netshoot --rm -it --image=nicolaka/netshoot -- /bin/bash

# Look at available images using Harvester CLI or API
kubectl --insecure-skip-tls-verify get VirtualMachineImages -n harvester-public

Warning: Use tokens from the TokenRequest API or manually created secret-based tokens instead of auto-generated secret-based tokens.
NAME          DISPLAY-NAME                                            SIZE         AGE
image-bp52g   almalinux-9.4-20240805                                  591724544    218d
image-brn7v   ubuntu-22.04-20241206-jammy-server-cloudimg-amd64.img   665347072    95d
image-c6rtq   rhel-9.3-base-2024.03.09.01.qcow2                       1944387584   259d
image-dkfnp   almalinux-9-genericcloud-9.5-20241120                   490995712    39d
image-fwxpx   ubuntu-24.04-20241206-noble-server-cloudimg-amd64.img   608719360    95d
image-hn5cg   almalinux-9.4-20240507                                  602603520    261d

kubectl --insecure-skip-tls-verify describe VirtualMachineImage image-c6rtq -n harvester-public






kubectl --insecure-skip-tls-verify cluster-info
kubectl --insecure-skip-tls-verify get node

kubectl --insecure-skip-tls-verify describe virtualmachineimage --selector='harvesterhci.io/imageDisplayName=almalinux-9.4-20240507' -A

kubectl --insecure-skip-tls-verify api-resources --verbs=list --namespaced -o name | xargs -n 1 kubectl get --ignore-not-found --show-kind --all-namespaces | grep service

kubectl logs traefik-internet-ingress-controller-55796c89fb-44w5h --namespace=kube-system | grep "user-management-backend"
kubectl logs traefik-internet-ingress-controller-55796c89fb-lq7bs --namespace=kube-system | grep "user-management-backend"
kubectl logs traefik-internet-ingress-controller-55796c89fb-vx5qz --namespace=kube-system | grep "user-management-backend"

kubectl create -f /home/<USER>/user-management-cron.yaml
kubectl get cronjob
kubectl get job
kubectl describe job user-management-cron-<id>
kubectl describe cronjob user-management-cron
kubectl edit cronjob user-management-cron
kubectl delete cronjobs user-management-cron

# Configuring XNAT

Create Ansible role using Ansible Molecule for deploying xnat web server and xnat data server on VMs, with details as given below.

Implement a multi-VM architecture with one virtual machine for XNAT database and storage purposes, and another virtual machine for the xnat web server also running Nginx Ingress server. The Nginx Ingress server should act as an Ingress proxy to XNAT web server running on the same VM as the Nginx Ingress server (xnat-web). The XNAT web server should connect to the XNAT data server for DB and data storage.

Configure Nginx Ingress proxy server to run on the XNAT web server. Nginx Ingress server should be configured with certificates obtained from Let’s Encrypt ACME API endpoint https://acme-v02.api.letsencrypt.org/directory, and use the Nginx Inbound DNS domain name "https://xnat-web.medp-proj-cde-ns.condenser.arc.ucl.ac.uk".

XNAT data server - xnat-data - IP address ***********
XNAT web server - xnat-web - IP address ***********, external DNS domain name xnat-web.medp-proj-cde-ns.condenser.arc.ucl.ac.uk over the SSL.

Use pipeline-based approach for installing XNAT server infrastructure, with xnat web service on xnat-web VM and xnat database and storage server running on the xnat-data VM.

Use Ansible Molecule project GitHub repository for configuring a remote server via SSH, creating XNAT deployment using XNAT pipeline engine using gradle, XNAT WAR file, XNAT directories, XNAT configuration, XNAT DB, XNAT plugins as listed below and XNAT server plugins also listed below.

The Ansible workstation uses the SSH Certificate File ~/.ssh/id_condenser.signed and Identity File ~/.ssh/id_condenser.

This Ansible deployment should be able to be run with a local unix root user, assume there is not password required to sudo as the root user. The deployment needs to be done using the my laptop connecting to the remote server over the SSH connection.

Ansible code should be able to deploy to multiple target servers each with different ssh logon options, with supported target platforms being RHEL 9.3, AlmaLinux, and Ubuntu.

XNAT plugins.

xnat_plugins:
  - name: xsync-plugin-all
    url: https://bitbucket.org/xnatdev/xsync/downloads/xsync-plugin-all-latest.jar
  - name: ldap-auth-plugin
    url: https://bitbucket.org/xnatx/ldap-auth-plugin/downloads/ldap-auth-plugin-latest.jar
  - name: ohif-viewer
    url: https://bitbucket.org/icrimaginginformatics/ohif-viewer-xnat-plugin/downloads/ohif-viewer-latest.jar
  - name: ml-schema-plugin
    url: https://bitbucket.org/xnatx/ml-schema-plugin/downloads/ml-schema-plugin-latest.jar
  - name: datasets-schema-plugin
    url: https://bitbucket.org/xnatx/datasets-schema-plugin/downloads/datasets-schema-plugin-latest.jar
  - name: ximgview-plugin
    url: https://bitbucket.org/xnatdev/xnat-image-viewer-plugin/downloads/ximgview-plugin-latest.jar
  - name: dxm-settings-plugin
    url: https://bitbucket.org/xnatx/xnatx-dxm-settings-plugin/downloads/dxm-settings-plugin-latest.jar
  - name: pipeline_engine_ui
    url: https://bitbucket.org/xnatx/pipeline_engine_plugin/downloads/pipeline_engine_ui-latest-xpl.jar

XNAT server plugins.

xnat_server_specific_plugin_urls:
  - name: container-service
    url: https://api.bitbucket.org/2.0/repositories/xnatdev/container-service/downloads/container-service-3.6.2-fat.jar
  - name: batch-launch
    url: https://api.bitbucket.org/2.0/repositories/xnatx/xnatx-batch-launch-plugin/downloads/batch-launch-0.7.0.jar
  - name: dax-plugin-genProcData
    url: https://github.com/VUIIS/dax/raw/main/misc/xnat-plugins/dax-plugin-genProcData-1.4.2.jar

Create a project README with the overall project guide, and a separate Ansible Setup, Ansible User and Ansible Vault Configuration guides in the docs folder in the project root folder.

Also create a small bash script to create empty project files (touch the files) and all the subfolders including the project's root directory.

# XNAT Deployment Repository for Rancher/Harvester

I'll create a comprehensive repository structure with Terraform code to deploy XNAT using the UCL-MIRSG/ansible-collection-infra repository on a Rancher VM. This solution will include VM creation, ingress configuration, and Ansible deployment automation.

## Repository Structure

Here's the repository structure I recommend:

```
xnat-deployment/
├── .github/                      # GitHub workflows
├── modules/
│   ├── vm/                       # VM creation module
│   └── ingress/                  # Ingress configuration module
├── components/
│   └── xnat/                     # XNAT Helm chart values
├── scripts/
│   ├── setup-ansible.sh.tpl      # Template for Ansible setup
│   └── deploy-xnat.sh            # XNAT deployment script
├── main.tf                       # Main Terraform configuration
├── variables.tf                  # Variable definitions
├── outputs.tf                    # Output definitions
├── versions.tf                   # Provider version constraints
├── providers.tf                  # Provider configuration
├── helmsman.yaml                 # Helmsman configuration
└── README.md                     # Documentation
```

## Core Terraform Files

### versions.tf
```hcl
terraform {
  required_providers {
    harvester = {
      source  = "harvester/harvester"
      version = "0.6.6"
    }
    helm = {
      source  = "hashicorp/helm"
      version = "~> 2.12.0"
    }
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "~> 2.25.0"
    }
  }

  required_version = ">= 1.8.5"
}
```

### providers.tf
```hcl
provider "harvester" {}

provider "helm" {
  kubernetes {
    config_path = var.kubeconfig_path
  }
}

provider "kubernetes" {
  config_path = var.kubeconfig_path
}
```

### variables.tf
```hcl
variable "namespace" {
  type        = string
  description = "Namespace that the VM will be deployed in"
}

variable "vm_name" {
  type        = string
  description = "Name of the virtual machine"
  default     = "xnat-server"
}

variable "network_name" {
  type        = string
  description = "Name of a network in the namespace"
}

variable "ssh_public_key_data" {
  type        = string
  description = "SSH public key data for VM access"
}

variable "xnat_hostname" {
  type        = string
  description = "Hostname for XNAT ingress"
  default     = "xnat"
}

variable "kubeconfig_path" {
  type        = string
  description = "Path to the kubeconfig file for Kubernetes access"
  default     = "~/.kube/config"
}

variable "vm_cpu_cores" {
  type        = number
  description = "Number of CPU cores for the VM"
  default     = 4
}

variable "vm_memory" {
  type        = string
  description = "Memory allocation for the VM"
  default     = "16Gi"
}

variable "vm_disk_size" {
  type        = string
  description = "Disk size for the VM"
  default     = "100Gi"
}

variable "xnat_admin_username" {
  type        = string
  description = "XNAT admin username"
  default     = "admin"
}

variable "xnat_admin_password" {
  type        = string
  description = "XNAT admin password"
  sensitive   = true
}

variable "xnat_db_password" {
  type        = string
  description = "XNAT database password"
  sensitive   = true
}
```

### main.tf
```hcl
# Get Almalinux image
data "harvester_image" "almalinux" {
  display_name = "almalinux-9.4-20240805"
  namespace    = "harvester-public"
}

# Deploy VM with SSH access
module "xnat_vm" {
  source = "./modules/vm"

  namespace           = var.namespace
  vm_name             = var.vm_name
  image_id            = data.harvester_image.almalinux.id
  network_name        = var.network_name
  ssh_public_key_data = var.ssh_public_key_data
  cpu_cores           = var.vm_cpu_cores
  memory              = var.vm_memory
  disk_size           = var.vm_disk_size
}

# Configure ingress
module "xnat_ingress" {
  source = "./modules/ingress"

  namespace     = var.namespace
  vm_name       = var.vm_name
  xnat_hostname = var.xnat_hostname
  depends_on    = [module.xnat_vm]
}

# Create cloud-init script for Ansible setup and XNAT deployment
resource "local_file" "ansible_setup" {
  content = templatefile("${path.module}/scripts/setup-ansible.sh.tpl", {
    xnat_admin_username = var.xnat_admin_username
    xnat_admin_password = var.xnat_admin_password
    xnat_db_password    = var.xnat_db_password
    xnat_hostname       = var.xnat_hostname
    namespace           = var.namespace
  })
  filename = "${path.module}/scripts/setup-ansible.sh"
  file_permission = "0755"
}
```

### outputs.tf
```hcl
output "vm_ip_address" {
  value       = module.xnat_vm.ip_address
  description = "IP address of the XNAT VM"
}

output "xnat_url" {
  value       = "https://${var.xnat_hostname}.${var.namespace}.condenser.arc.ucl.ac.uk"
  description = "URL to access XNAT web interface"
}

output "deployment_instructions" {
  value = <<-EOT
    # SSH into the VM
    ssh almalinux@${module.xnat_vm.ip_address}

    # Copy the setup script to the VM
    scp scripts/setup-ansible.sh scripts/deploy-xnat.sh almalinux@${module.xnat_vm.ip_address}:~/

    # Run the setup and deployment scripts
    ssh almalinux@${module.xnat_vm.ip_address} "chmod +x ~/setup-ansible.sh ~/deploy-xnat.sh && ~/setup-ansible.sh && ~/deploy-xnat.sh"
  EOT
}
```

## VM Module

### modules/vm/main.tf
```hcl
resource "harvester_virtualmachine" "vm" {
  name        = var.vm_name
  namespace   = var.namespace
  description = "XNAT VM"
  hostname    = var.vm_name

  cpu    = var.cpu_cores
  memory = var.memory

  restart_after_update = true
  efi                  = true
  secure_boot          = true
  run_strategy         = "RerunOnFailure"
  reserved_memory      = "512Mi"
  machine_type         = "q35"

  disk {
    name       = "rootdisk"
    type       = "disk"
    size       = var.disk_size
    bus        = "virtio"
    boot_order = 1

    image       = var.image_id
    auto_delete = true
  }

  network_interface {
    name           = "nic-1"
    wait_for_lease = true
    type           = "bridge"
    network_name   = var.network_name
  }

  cloudinit {
    user_data = <<EOF
#cloud-config
package_update: true
packages:
  - qemu-guest-agent
  - git
  - python3
  - python3-pip
  - wget
  - unzip
  - vim
runcmd:
  - - systemctl
    - enable
    - --now
    - qemu-guest-agent.service
  - dnf config-manager --set-enabled crb
  - dnf install -y epel-release
ssh_authorized_keys:
  - ${var.ssh_public_key_data}
power_state:
  mode: reboot
EOF
  }

  tags = {
    "created_by" = "terraform"
    "app"        = "xnat"
  }
}
```

### modules/vm/variables.tf
```hcl
variable "namespace" {
  type        = string
  description = "Namespace that the VM will be deployed in"
}

variable "vm_name" {
  type        = string
  description = "Name of the virtual machine"
}

variable "image_id" {
  type        = string
  description = "Image ID to use for the VM"
}

variable "network_name" {
  type        = string
  description = "Name of a network in the namespace"
}

variable "ssh_public_key_data" {
  type        = string
  description = "SSH public key data"
}

variable "cpu_cores" {
  type        = number
  description = "Number of CPU cores for the VM"
  default     = 4
}

variable "memory" {
  type        = string
  description = "Memory allocation for the VM"
  default     = "16Gi"
}

variable "disk_size" {
  type        = string
  description = "Disk size for the VM"
  default     = "100Gi"
}
```

### modules/vm/outputs.tf
```hcl
output "ip_address" {
  value = harvester_virtualmachine.vm.network_interface[0].ip_address
}

output "vm_id" {
  value = harvester_virtualmachine.vm.id
}
```

## Ingress Module

### modules/ingress/main.tf
```hcl
resource "harvester_virtualmachine" "vm_update" {
  name      = var.vm_name
  namespace = var.namespace

  # We only want to update the tags, not recreate the VM
  lifecycle {
    ignore_changes = [
      cpu,
      memory,
      disk,
      network_interface,
      cloudinit,
      efi,
      secure_boot,
      run_strategy,
      reserved_memory,
      machine_type,
    ]
  }

  # Add condenser ingress tags according to the documentation
  tags = {
    "condenser_ingress_isEnabled"    = "true"
    "condenser_ingress_xnat_hostname" = var.xnat_hostname
    "condenser_ingress_xnat_port"     = "8080"
    "condenser_ingress_xnat_protocol" = "http"
    # Configure Nginx for larger upload sizes for DICOM data
    "condenser_ingress_xnat_nginx_proxy-body-size" = "512m"
    "condenser_ingress_xnat_nginx_proxy-connect-timeout" = "300"
    "condenser_ingress_xnat_nginx_proxy-read-timeout" = "300"
    "condenser_ingress_xnat_nginx_proxy-send-timeout" = "300"
  }
}
```

### modules/ingress/variables.tf
```hcl
variable "namespace" {
  type        = string
  description = "Namespace that the VM is deployed in"
}

variable "vm_name" {
  type        = string
  description = "Name of the virtual machine"
}

variable "xnat_hostname" {
  type        = string
  description = "Hostname for XNAT ingress"
  default     = "xnat"
}
```

## Ansible Setup Scripts

### scripts/setup-ansible.sh.tpl
```bash
#!/bin/bash
set -e

echo "Setting up Ansible environment for XNAT deployment"

# Install Ansible and required packages
sudo dnf install -y ansible-core python3-pip python3-devel gcc
pip3 install ansible

# Clone the UCL-MIRSG ansible-collection-infra repository
ansible-galaxy collection install git+https://github.com/UCL-MIRSG/ansible-collection-infra.git

# Create directory for XNAT configuration
mkdir -p ~/xnat-config

# Create Ansible inventory file
cat > ~/xnat-config/inventory.ini << 'EOF'
[xnat_servers]
localhost ansible_connection=local

[all:vars]
ansible_python_interpreter=/usr/bin/python3
EOF

# Create Ansible configuration file
cat > ~/xnat-config/ansible.cfg << 'EOF'
[defaults]
inventory = inventory.ini
roles_path = ~/.ansible/roles
collections_path = ~/.ansible/collections
host_key_checking = False
EOF

# Create variables file for XNAT
cat > ~/xnat-config/xnat_vars.yml << EOF
# XNAT Configuration Variables
xnat_admin_username: "${xnat_admin_username}"
xnat_admin_password: "${xnat_admin_password}"
xnat_db_password: "${xnat_db_password}"

# XNAT Paths
xnat_home: "/data/xnat/home"
xnat_data: "/data/xnat/archive"
xnat_prearchive: "/data/xnat/prearchive"
xnat_cache: "/data/xnat/cache"

# Database Configuration
xnat_db_host: "localhost"
xnat_db_name: "xnat"
xnat_db_user: "xnat"

# Web Configuration
xnat_url: "https://${xnat_hostname}.${namespace}.condenser.arc.ucl.ac.uk"
xnat_http_port: 8080
EOF

echo "Ansible environment setup complete!"
```

### scripts/deploy-xnat.sh
```bash
#!/bin/bash
set -e

echo "Deploying XNAT using the UCL-MIRSG Ansible collection"

cd ~/xnat-config

# Create playbook for XNAT deployment
cat > deploy-xnat.yml << 'EOF'
---
- name: Deploy XNAT
  hosts: xnat_servers
  become: yes
  vars_files:
    - xnat_vars.yml
  
  pre_tasks:
    - name: Create directories for XNAT
      file:
        path: "{{ item }}"
        state: directory
        owner: root
        group: root
        mode: '0755'
      with_items:
        - "{{ xnat_home }}"
        - "{{ xnat_data }}"
        - "{{ xnat_prearchive }}"
        - "{{ xnat_cache }}"

  tasks:
    - name: Include UCL-MIRSG XNAT role
      include_role:
        name: ucl_mirsg.infra.xnat

    - name: Ensure XNAT service is running
      systemd:
        name: xnat
        state: started
        enabled: yes
      register: xnat_service_status
    
    - name: Display XNAT status
      debug:
        msg: "XNAT is deployed and running at {{ xnat_url }}"
EOF

# Run the playbook
ansible-playbook deploy-xnat.yml

echo "XNAT deployment complete!"
```

## Helmsman Configuration

### helmsman.yaml
```yaml
# Helmsman configuration for XNAT resources
context: default
settings:
  kubeContext: default

namespaces:
  xnat-system:
    protected: false

helmRepos:
  xnat: https://xnat-chart.github.io/charts

apps:
  xnat-resources:
    name: xnat-resources
    namespace: xnat-system
    enabled: true
    chart: xnat/xnat-resources
    version: 1.0.0
    valuesFiles:
      - ./components/xnat/values.yaml
    wait: true
    timeout: 900
```

### components/xnat/values.yaml
```yaml
# XNAT Resources Helm Chart Values
persistence:
  enabled: true
  storageClass: longhorn
  
  volumes:
    xnatHome:
      size: 10Gi
    xnatData:
      size: 100Gi
    xnatPrearchive:
      size: 50Gi
    xnatCache:
      size: 20Gi

resources:
  requests:
    cpu: 2
    memory: 4Gi
  limits:
    cpu: 4
    memory: 8Gi

postgres:
  enabled: true
  persistence:
    size: 20Gi
  resources:
    requests:
      cpu: 1
      memory: 2Gi
```

## Deployment Instructions

1. Set up your environment:
   ```bash
   export KUBECONFIG=/path/to/kubeconfig.yaml
   ```

2. Initialize Terraform:
   ```bash
   terraform init
   ```

3. Create a terraform.tfvars file with your specific variables:
   ```hcl
   namespace = "your-namespace"
   network_name = "your-network"
   ssh_public_key_data = "ssh-rsa AAAA..."
   xnat_hostname = "xnat"
   xnat_admin_password = "secure-password"
   xnat_db_password = "secure-db-password"
   ```

4. Apply the Terraform configuration:
   ```bash
   terraform apply
   ```

5. Follow the instructions provided in the Terraform output to SSH into the VM and deploy XNAT.

## Notes on the Implementation

1. **VM Creation**: Uses the Harvester provider to create an AlmaLinux VM with appropriate resources.

2. **Ingress Configuration**: Configures ingress using the Condenser tags documented in your guide, allowing HTTPS access to XNAT.

3. **Ansible Setup**: Prepares the VM to run Ansible and deploy XNAT using the UCL-MIRSG/ansible-collection-infra.

4. **Helm Integration**: Uses Helmsman to deploy supporting Kubernetes resources if needed.

5. **Security Considerations**: Sensitive values like passwords are marked as sensitive in Terraform variables.

This implementation provides a complete automation pipeline for deploying XNAT on Rancher/Harvester, with proper ingress configuration and Ansible-based application deployment.