RNOH

OMERO

The Human Tissue Authority (HTA): https://www.hta.gov.uk/

OMOP Common Data Model (CDM).
The OMOP Common Data Model allows for the systematic analysis of disparate observational databases.

https://www.ohdsi.org/data-standardization/#:~:text=The%20Observational%20Medical%20Outcomes%20Partnership,that%20can%20produce%20reliable%20evidence

# Common Data Model

## Data Collection

Consolidation schema is used for all data collected from applications.

Conceptual model contains ER diagram for the project at a conceptual level.

OHDSI (spelled <PERSON><PERSON><PERSON>ey) Common Data Model (CDM) schema specifies 

Data needed for research needs to be mapped to OMOP.

### Raw Data (Applcation Data)

Biobank:
  Biobank Team: Use MediLIMS to create shipment
  Researcher: Use MediLIMS to track shipment
  - Access FTDB (For clinical frozen tissue)
  - Emails (meetings and discussions)
  - FTDB
  - MediLIMS (For clinical FFPE/slides)
  - SAP
  - NAS drive / Shared folder
  - MTA?
  - Procuro (For any other samples)
  - Labware (replacement for Procuro)
  - Infoflex (consent)
  - NoteON (consent, patient infomation)
  - S drive files (consent)
  - CanonTherefore (consent)
  - ICS ("Patient status checked as follows: Are they alive?)
  - Qpulse (Empty forms/templates stored on for processed recordings)
  - Nanodrop/Qubit assay (QC?)
  - Consent tracking spreadsheet
  - Project Spreadsheet
Clinical:
  WF:
    Admin team: Physical slides and files (possibly electronic or paper or both), Noteon (a new filing system in version 1.4?)
    Pathologist: MediLIMS
    WGS:
      Genomics team:
	    - Physical samples in WGS Bloodbox
		- WGS Samples spreadsheet (findings?)
		- RTC (cut event - Ready To Cut) spreadsheet
		- Procuro to add/track samples
		- MediLIMS (laboratory notes)
		- Infoflex (details of samples and information for consultants and medical staff)
		- NoteOn (patient follow data)
      Pathologist: MediLIMS
DigPath:
  - ICS
  - MediLIMS
  - NoteOn
  - Procuro
  - Spreadsheets
Material:
  - TMA?
  - Micortome?

