apiVersion: kubevirt.io/v1
kind: VirtualMachine
metadata:
  annotations:
    field.cattle.io/description: XNAT Data Server - Live Environment
    harvesterhci.io/vmRunStrategy: RerunOnFailure
    harvesterhci.io/volumeClaimTemplates: >-
      [{"metadata":{"name":"xnat-data-disk-0-cjzml","creationTimestamp":null,"annotations":{"harvesterhci.io/imageId":"harvester-public/image-fwxpx","terraform-provider-harvester-auto-delete":"true"}},"spec":{"accessModes":["ReadWriteMany"],"resources":{"requests":{"storage":"100Gi"}},"storageClassName":"longhorn-image-fwxpx","volumeMode":"Block"},"status":{}}]
    kubevirt.io/latest-observed-api-version: v1
    kubevirt.io/storage-observed-api-version: v1
  creationTimestamp: '2025-03-18T20:27:50Z'
  finalizers:
    - kubevirt.io/virtualMachineControllerFinalize
    - harvesterhci.io/VMController.UnsetOwnerOfPVCs
  generation: 2
  labels:
    harvesterhci.io/creator: terraform-provider-harvester
    harvesterhci.io/vmName: xnat-data
    tag.harvesterhci.io/os: linux
  managedFields:
    - apiVersion: kubevirt.io/v1
      fieldsType: FieldsV1
      fieldsV1:
        f:metadata:
          f:annotations:
            f:kubevirt.io/latest-observed-api-version: {}
            f:kubevirt.io/storage-observed-api-version: {}
          f:finalizers:
            .: {}
            v:"kubevirt.io/virtualMachineControllerFinalize": {}
      manager: Go-http-client
      operation: Update
      time: '2025-03-18T20:27:50Z'
    - apiVersion: kubevirt.io/v1
      fieldsType: FieldsV1
      fieldsV1:
        f:metadata:
          f:annotations:
            .: {}
            f:field.cattle.io/description: {}
            f:harvesterhci.io/volumeClaimTemplates: {}
          f:labels:
            .: {}
            f:harvesterhci.io/creator: {}
            f:harvesterhci.io/vmName: {}
            f:tag.harvesterhci.io/os: {}
        f:spec:
          .: {}
          f:runStrategy: {}
          f:template:
            .: {}
            f:metadata:
              .: {}
              f:annotations:
                .: {}
                f:harvesterhci.io/sshNames: {}
                f:harvesterhci.io/waitForLeaseInterfaceNames: {}
              f:creationTimestamp: {}
              f:labels:
                .: {}
                f:harvesterhci.io/creator: {}
                f:harvesterhci.io/vmName: {}
                f:tag.harvesterhci.io/os: {}
            f:spec:
              .: {}
              f:affinity:
                .: {}
                f:podAntiAffinity:
                  .: {}
                  f:preferredDuringSchedulingIgnoredDuringExecution: {}
              f:domain:
                .: {}
                f:cpu:
                  .: {}
                  f:cores: {}
                f:devices:
                  .: {}
                  f:disks: {}
                f:machine:
                  .: {}
                  f:type: {}
                f:resources:
                  .: {}
                  f:limits:
                    .: {}
                    f:cpu: {}
                    f:memory: {}
              f:evictionStrategy: {}
              f:hostname: {}
              f:networks: {}
              f:volumes: {}
      manager: terraform-provider-harvester_v0.6.6
      operation: Update
      time: '2025-03-18T20:27:50Z'
    - apiVersion: kubevirt.io/v1
      fieldsType: FieldsV1
      fieldsV1:
        f:metadata:
          f:annotations:
            f:harvesterhci.io/vmRunStrategy: {}
          f:finalizers:
            v:"harvesterhci.io/VMController.UnsetOwnerOfPVCs": {}
        f:spec:
          f:template:
            f:spec:
              f:domain:
                f:devices:
                  f:interfaces: {}
      manager: harvester
      operation: Update
      time: '2025-03-18T20:28:22Z'
    - apiVersion: kubevirt.io/v1
      fieldsType: FieldsV1
      fieldsV1:
        f:status:
          .: {}
          f:conditions: {}
          f:created: {}
          f:desiredGeneration: {}
          f:observedGeneration: {}
          f:printableStatus: {}
          f:stateChangeRequests: {}
          f:volumeSnapshotStatuses: {}
      manager: Go-http-client
      operation: Update
      subresource: status
      time: '2025-03-18T20:31:55Z'
  name: xnat-data
  namespace: medp-proj-cde-ns
  resourceVersion: '666276662'
  uid: 962f94ad-bfc1-44cb-8e67-4ba54d6e8219
spec:
  runStrategy: RerunOnFailure
  template:
    metadata:
      annotations:
        harvesterhci.io/sshNames: '[]'
        harvesterhci.io/waitForLeaseInterfaceNames: '[]'
      creationTimestamp: null
      labels:
        harvesterhci.io/creator: terraform-provider-harvester
        harvesterhci.io/vmName: xnat-data
        tag.harvesterhci.io/os: linux
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: network.harvesterhci.io/mgmt
                    operator: In
                    values:
                      - 'true'
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: harvesterhci.io/creator
                      operator: Exists
                topologyKey: kubernetes.io/hostname
              weight: 100
      architecture: amd64
      domain:
        cpu:
          cores: 2
        devices:
          disks:
            - bootOrder: 1
              disk:
                bus: virtio
              name: disk-0
            - disk:
                bus: virtio
              name: cloudinitdisk
          interfaces:
            - bridge: {}
              macAddress: 96:ed:2f:e9:51:53
              model: virtio
              name: nic-0
        machine:
          type: q35
        memory:
          guest: 10140Mi
        resources:
          limits:
            cpu: '2'
            memory: 10Gi
          requests:
            cpu: 125m
            memory: 6826Mi
      evictionStrategy: LiveMigrateIfPossible
      hostname: xnat-data
      networks:
        - multus:
            networkName: medp-proj-cde-ns/medp-proj-cde-net
          name: nic-0
      terminationGracePeriodSeconds: 120
      volumes:
        - name: disk-0
          persistentVolumeClaim:
            claimName: xnat-data-disk-0-cjzml
        - cloudInitNoCloud:
            networkDataSecretRef:
              name: cloud-config-51ec4278
            secretRef:
              name: cloud-config-51ec4278
          name: cloudinitdisk
status:
  conditions:
    - lastProbeTime: '2025-03-18T20:31:52Z'
      lastTransitionTime: '2025-03-18T20:31:52Z'
      message: virt-launcher pod is terminating
      reason: PodTerminating
      status: 'False'
      type: Ready
    - lastProbeTime: null
      lastTransitionTime: null
      status: 'True'
      type: LiveMigratable
  created: true
  desiredGeneration: 2
  observedGeneration: 2
  printableStatus: Stopped
  stateChangeRequests:
    - action: Stop
      uid: b5d82a9c-bbba-4292-b291-c312604eae91
    - action: Start
  volumeSnapshotStatuses:
    - enabled: false
      name: disk-0
      reason: 2 matching VolumeSnapshotClasses for longhorn-image-fwxpx
    - enabled: false
      name: cloudinitdisk
      reason: Snapshot is not supported for this volumeSource type [cloudinitdisk]
