# OMOP CDM Pipeline

The OMOP CDM pipeline is required to extract the patient data from the original sources like patient record systems, process the data extracted so that it is compliance with the OMOP CDM principles of de-identified patient records, and finally store this transformed data into the standard OMOP CDM database records with recommended data schema for the OMOP CDM databases.

## Design

Key architectural concepts for the OMOP ETL system:

```mermaid
flowchart TB
    subgraph ConfigLayer[Configuration Layer]
        YC[YAML Config]
        MM[Mapping Manager]
    end

    subgraph DataLayer[Data Access Layer]
        SC[Source Connector]
        TC[Target Connector]
        Cache[Data Cache]
    end

    subgraph ETLCore[ETL Core]
        Extractor
        Transformer
        Loader
        QA[Quality Assurance]
    end

    subgraph Services[Common Services]
        Logger
        Monitor[Monitoring]
        Error[Error Handler]
    end

    YC --> MM
    MM --> Transformer
    SC --> Extractor
    Extractor --> Cache
    Cache --> Transformer
    Transformer --> Cache
    Cache --> Loader
    Loader --> TC
    
    QA --> Cache
    Logger --> ETLCore
    Monitor --> ETLCore
    Error --> ETLCore
```

Key Architectural Components:

1. Configuration Layer:
   - YAML-based declarative mapping definitions
   - Runtime configuration management
   - Vocabulary mapping resolution

2. Data Access Layer:
   - Abstract source/target interfaces
   - Connection pooling
   - Caching strategy

3. ETL Core:
   - Pipeline orchestration
   - Incremental processing
   - Data validation/QA

4. Common Services:
   - Logging/monitoring
   - Error handling
   - Performance metrics

The architecture emphasizes:
- Loose coupling between components
- Clear separation of concerns
- Extensibility for new data sources
- Configurability without code changes

## Implementation

```cpp
// Project structure in CMakeLists.txt
cmake_minimum_required(VERSION 3.15)
project(OMOP_ETL VERSION 1.0)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Add yaml-cpp dependency
find_package(yaml-cpp REQUIRED)

# Add source files
add_subdirectory(src)
enable_testing()
add_subdirectory(test)

// src/ETLPipeline.hpp
#pragma once
#include <string>
#include <yaml-cpp/yaml.h>
#include "DatabaseConnector.hpp"
#include "DataTransformer.hpp"
#include "Validator.hpp"

/**
 * @brief Main ETL pipeline controller class
 * @details Manages the complete ETL process from source to OMOP CDM
 */
class ETLPipeline {
public:
    /**
     * @brief Construct pipeline from YAML config
     * @param configPath Path to YAML configuration file
     */
    explicit ETLPipeline(const std::string& configPath);
    
    /**
     * @brief Execute the complete ETL pipeline
     * @return True if successful, false otherwise
     */
    bool execute();

private:
    YAML::Node config_;
    DatabaseConnector sourceDb_;
    DatabaseConnector targetDb_;
    DataTransformer transformer_;
    Validator validator_;
};

// src/DatabaseConnector.hpp
#pragma once
#include <string>

/**
 * @brief Database connection manager
 * @details Handles connections to both source and OMOP databases
 */
class DatabaseConnector {
public:
    /**
     * @brief Connect to database using connection string
     * @param connectionString Database connection parameters
     */
    void connect(const std::string& connectionString);
    
    /**
     * @brief Execute query and return results
     * @param query SQL query to execute
     * @return Query results as string
     */
    std::string executeQuery(const std::string& query);
};

// src/DataTransformer.hpp
#pragma once
#include <vector>
#include <string>

/**
 * @brief OMOP data transformation engine
 * @details Handles mapping and transformation of source data to OMOP CDM format
 */
class DataTransformer {
public:
    /**
     * @brief Load mapping rules from YAML config
     * @param mappingConfig YAML node containing mapping rules
     */
    void loadMappings(const YAML::Node& mappingConfig);
    
    /**
     * @brief Transform source data to OMOP format
     * @param sourceData Input data to transform
     * @return Transformed data in OMOP format
     */
    std::vector<std::string> transformData(const std::vector<std::string>& sourceData);
};

// src/Validator.hpp
#pragma once
#include <string>

/**
 * @brief Data validation class
 * @details Validates transformed data against OMOP CDM requirements
 */
class Validator {
public:
    /**
     * @brief Validate transformed data
     * @param data Data to validate
     * @return True if valid, false otherwise
     */
    bool validateData(const std::string& data);
    
    /**
     * @brief Get validation errors
     * @return String containing validation error messages
     */
    std::string getValidationErrors();
};

// test/TestETLPipeline.cpp
#include <gtest/gtest.h>
#include "ETLPipeline.hpp"

TEST(ETLPipelineTest, LoadConfiguration) {
    ETLPipeline pipeline("test_config.yaml");
    // Add test cases
}

// Example YAML configuration
// config/etl_config.yaml
source_db:
  type: postgresql
  host: localhost
  port: 5432
  database: source_clinical_db
  username: user
  password: pass

target_db:
  type: postgresql
  host: localhost
  port: 5432
  database: omop_cdm
  username: user
  password: pass

mappings:
  - source_table: patients
    target_table: person
    columns:
      - source: patient_id
        target: person_id
      - source: birth_date
        target: birth_datetime
      - source: gender
        target: gender_concept_id
        vocabulary: Gender

  - source_table: conditions
    target_table: condition_occurrence
    columns:
      - source: condition_id
        target: condition_occurrence_id
      - source: patient_id
        target: person_id
      - source: condition_code
        target: condition_concept_id
        vocabulary: SNOMED

```

Key features of the project structure:

1. Core components:
   - ETLPipeline: Main controller
   - DatabaseConnector: Handles DB connections
   - DataTransformer: Maps source to OMOP format
   - Validator: Ensures data validity

2. Configuration uses YAML for:
   - Database connections
   - Table mappings
   - Vocabulary mappings

3. Project organization:
   - src/: Source files
   - test/: Unit tests
   - config/: YAML configs
   - output/: Generated files

Add dependencies: yaml-cpp for YAML parsing, GTest for testing.

