<html>
    <head>
        <style>
            @font-face {
                font-family: octicons-link;
                src: url(data:font/woff;charset=utf-8;base64,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) format('woff');
            }
        
        .markdown-body {
            -ms-text-size-adjust: 100;
            -webkit-text-size-adjust: 100;
            line-height: 1.5;
            color: #24292e;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            font-size: 16px;
            line-height: 1.5;
            word-wrap: break-word;
        }
        
        .markdown-body .pl-c {
            color: #6a737d;
        }
        
        .markdown-body .pl-c1,
        .markdown-body .pl-s .pl-v {
            color: #005cc5;
        }
        
        .markdown-body .pl-e,
        .markdown-body .pl-en {
            color: #6f42c1;
        }
        
        .markdown-body .pl-smi,
        .markdown-body .pl-s .pl-s1 {
            color: #24292e;
        }
        
        .markdown-body .pl-ent {
            color: #22863a;
        }
        
        .markdown-body .pl-k {
            color: #d73a49;
        }
        
        .markdown-body .pl-s,
        .markdown-body .pl-pds,
        .markdown-body .pl-s .pl-pse .pl-s1,
        .markdown-body .pl-sr,
        .markdown-body .pl-sr .pl-cce,
        .markdown-body .pl-sr .pl-sre,
        .markdown-body .pl-sr .pl-sra {
            color: #032f62;
        }
        
        .markdown-body .pl-v,
        .markdown-body .pl-smw {
            color: #e36209;
        }
        
        .markdown-body .pl-bu {
            color: #b31d28;
        }
        
        .markdown-body .pl-ii {
            color: #fafbfc;
            background-color: #b31d28;
        }
        
        .markdown-body .pl-c2 {
            color: #fafbfc;
            background-color: #d73a49;
        }
        
        .markdown-body .pl-c2::before {
            content: "^M";
        }
        
        .markdown-body .pl-sr .pl-cce {
            font-weight: bold;
            color: #22863a;
        }
        
        .markdown-body .pl-ml {
            color: #735c0f;
        }
        
        .markdown-body .pl-mh,
        .markdown-body .pl-mh .pl-en,
        .markdown-body .pl-ms {
            font-weight: bold;
            color: #005cc5;
        }
        
        .markdown-body .pl-mi {
            font-style: italic;
            color: #24292e;
        }
        
        .markdown-body .pl-mb {
            font-weight: bold;
            color: #24292e;
        }
        
        .markdown-body .pl-md {
            color: #b31d28;
            background-color: #ffeef0;
        }
        
        .markdown-body .pl-mi1 {
            color: #22863a;
            background-color: #f0fff4;
        }
        
        .markdown-body .pl-mc {
            color: #e36209;
            background-color: #ffebda;
        }
        
        .markdown-body .pl-mi2 {
            color: #f6f8fa;
            background-color: #005cc5;
        }
        
        .markdown-body .pl-mdr {
            font-weight: bold;
            color: #6f42c1;
        }
        
        .markdown-body .pl-ba {
            color: #586069;
        }
        
        .markdown-body .pl-sg {
            color: #959da5;
        }
        
        .markdown-body .pl-corl {
            text-decoration: underline;
            color: #032f62;
        }
        
        .markdown-body .octicon {
            display: inline-block;
            vertical-align: text-top;
            fill: currentColor;
        }
        
        .markdown-body a {
            background-color: transparent;
        }
        
        .markdown-body a:active,
        .markdown-body a:hover {
            outline-width: 0;
        }
        
        .markdown-body strong {
            font-weight: inherit;
        }
        
        .markdown-body strong {
            font-weight: bolder;
        }
        
        .markdown-body h1 {
            font-size: 2em;
            margin: 0.67em 0;
        }
        
        .markdown-body img {
            border-style: none;
        }
        
        .markdown-body code,
        .markdown-body kbd,
        .markdown-body pre {
            font-family: monospace, monospace;
            font-size: 1em;
        }
        
        .markdown-body hr {
            box-sizing: content-box;
            height: 0;
            overflow: visible;
        }
        
        .markdown-body input {
            font: inherit;
            margin: 0;
        }
        
        .markdown-body input {
            overflow: visible;
        }
        
        .markdown-body [type="checkbox"] {
            box-sizing: border-box;
            padding: 0;
        }
        
        .markdown-body * {
            box-sizing: border-box;
        }
        
        .markdown-body input {
            font-family: inherit;
            font-size: inherit;
            line-height: inherit;
        }
        
        .markdown-body a {
            color: #0366d6;
            text-decoration: none;
        }
        
        .markdown-body a:hover {
            text-decoration: underline;
        }
        
        .markdown-body strong {
            font-weight: 600;
        }
        
        .markdown-body hr {
            height: 0;
            margin: 15px 0;
            overflow: hidden;
            background: transparent;
            border: 0;
            border-bottom: 1px solid #dfe2e5;
        }
        
        .markdown-body hr::before {
            display: table;
            content: "";
        }
        
        .markdown-body hr::after {
            display: table;
            clear: both;
            content: "";
        }
        
        .markdown-body table {
            border-spacing: 0;
            border-collapse: collapse;
        }
        
        .markdown-body td,
        .markdown-body th {
            padding: 0;
        }
        
        .markdown-body h1,
        .markdown-body h2,
        .markdown-body h3,
        .markdown-body h4,
        .markdown-body h5,
        .markdown-body h6 {
            margin-top: 0;
            margin-bottom: 0;
        }
        
        .markdown-body h1 {
            font-size: 32px;
            font-weight: 600;
        }
        
        .markdown-body h2 {
            font-size: 24px;
            font-weight: 600;
        }
        
        .markdown-body h3 {
            font-size: 20px;
            font-weight: 600;
        }
        
        .markdown-body h4 {
            font-size: 16px;
            font-weight: 600;
        }
        
        .markdown-body h5 {
            font-size: 14px;
            font-weight: 600;
        }
        
        .markdown-body h6 {
            font-size: 12px;
            font-weight: 600;
        }
        
        .markdown-body p {
            margin-top: 0;
            margin-bottom: 10px;
        }
        
        .markdown-body blockquote {
            margin: 0;
        }
        
        .markdown-body ul,
        .markdown-body ol {
            padding-left: 0;
            margin-top: 0;
            margin-bottom: 0;
        }
        
        .markdown-body ol ol,
        .markdown-body ul ol {
            list-style-type: lower-roman;
        }
        
        .markdown-body ul ul ol,
        .markdown-body ul ol ol,
        .markdown-body ol ul ol,
        .markdown-body ol ol ol {
            list-style-type: lower-alpha;
        }
        
        .markdown-body dd {
            margin-left: 0;
        }
        
        .markdown-body code {
            font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
            font-size: 12px;
        }
        
        .markdown-body pre {
            margin-top: 0;
            margin-bottom: 0;
            font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
            font-size: 12px;
        }
        
        .markdown-body .octicon {
            vertical-align: text-bottom;
        }
        
        .markdown-body .pl-0 {
            padding-left: 0 !important;
        }
        
        .markdown-body .pl-1 {
            padding-left: 4px !important;
        }
        
        .markdown-body .pl-2 {
            padding-left: 8px !important;
        }
        
        .markdown-body .pl-3 {
            padding-left: 16px !important;
        }
        
        .markdown-body .pl-4 {
            padding-left: 24px !important;
        }
        
        .markdown-body .pl-5 {
            padding-left: 32px !important;
        }
        
        .markdown-body .pl-6 {
            padding-left: 40px !important;
        }
        
        .markdown-body::before {
            display: table;
            content: "";
        }
        
        .markdown-body::after {
            display: table;
            clear: both;
            content: "";
        }
        
        .markdown-body>*:first-child {
            margin-top: 0 !important;
        }
        
        .markdown-body>*:last-child {
            margin-bottom: 0 !important;
        }
        
        .markdown-body a:not([href]) {
            color: inherit;
            text-decoration: none;
        }
        
        .markdown-body .anchor {
            float: left;
            padding-right: 4px;
            margin-left: -20px;
            line-height: 1;
        }
        
        .markdown-body .anchor:focus {
            outline: none;
        }
        
        .markdown-body p,
        .markdown-body blockquote,
        .markdown-body ul,
        .markdown-body ol,
        .markdown-body dl,
        .markdown-body table,
        .markdown-body pre {
            margin-top: 0;
            margin-bottom: 16px;
        }
        
        .markdown-body hr {
            height: 0.25em;
            padding: 0;
            margin: 24px 0;
            background-color: #e1e4e8;
            border: 0;
        }
        
        .markdown-body blockquote {
            padding: 0 1em;
            color: #6a737d;
            border-left: 0.25em solid #dfe2e5;
        }
        
        .markdown-body blockquote>:first-child {
            margin-top: 0;
        }
        
        .markdown-body blockquote>:last-child {
            margin-bottom: 0;
        }
        
        .markdown-body kbd {
            display: inline-block;
            padding: 3px 5px;
            font-size: 11px;
            line-height: 10px;
            color: #444d56;
            vertical-align: middle;
            background-color: #fafbfc;
            border: solid 1px #c6cbd1;
            border-bottom-color: #959da5;
            border-radius: 3px;
            box-shadow: inset 0 -1px 0 #959da5;
        }
        
        .markdown-body h1,
        .markdown-body h2,
        .markdown-body h3,
        .markdown-body h4,
        .markdown-body h5,
        .markdown-body h6 {
            margin-top: 24px;
            margin-bottom: 16px;
            font-weight: 600;
            line-height: 1.25;
        }
        
        .markdown-body h1 .octicon-link,
        .markdown-body h2 .octicon-link,
        .markdown-body h3 .octicon-link,
        .markdown-body h4 .octicon-link,
        .markdown-body h5 .octicon-link,
        .markdown-body h6 .octicon-link {
            color: #1b1f23;
            vertical-align: middle;
            visibility: hidden;
        }
        
        .markdown-body h1:hover .anchor,
        .markdown-body h2:hover .anchor,
        .markdown-body h3:hover .anchor,
        .markdown-body h4:hover .anchor,
        .markdown-body h5:hover .anchor,
        .markdown-body h6:hover .anchor {
            text-decoration: none;
        }
        
        .markdown-body h1:hover .anchor .octicon-link,
        .markdown-body h2:hover .anchor .octicon-link,
        .markdown-body h3:hover .anchor .octicon-link,
        .markdown-body h4:hover .anchor .octicon-link,
        .markdown-body h5:hover .anchor .octicon-link,
        .markdown-body h6:hover .anchor .octicon-link {
            visibility: visible;
        }
        
        .markdown-body h1 {
            padding-bottom: 0.3em;
            font-size: 2em;
            border-bottom: 1px solid #eaecef;
        }
        
        .markdown-body h2 {
            padding-bottom: 0.3em;
            font-size: 1.5em;
            border-bottom: 1px solid #eaecef;
        }
        
        .markdown-body h3 {
            font-size: 1.25em;
        }
        
        .markdown-body h4 {
            font-size: 1em;
        }
        
        .markdown-body h5 {
            font-size: 0.875em;
        }
        
        .markdown-body h6 {
            font-size: 0.85em;
            color: #6a737d;
        }
        
        .markdown-body ul,
        .markdown-body ol {
            padding-left: 2em;
        }
        
        .markdown-body ul ul,
        .markdown-body ul ol,
        .markdown-body ol ol,
        .markdown-body ol ul {
            margin-top: 0;
            margin-bottom: 0;
        }
        
        .markdown-body li {
            word-wrap: break-all;
        }
        
        .markdown-body li>p {
            margin-top: 16px;
        }
        
        .markdown-body li+li {
            margin-top: 0.25em;
        }
        
        .markdown-body dl {
            padding: 0;
        }
        
        .markdown-body dl dt {
            padding: 0;
            margin-top: 16px;
            font-size: 1em;
            font-style: italic;
            font-weight: 600;
        }
        
        .markdown-body dl dd {
            padding: 0 16px;
            margin-bottom: 16px;
        }
        
        .markdown-body table {
            display: block;
            width: 100;
            overflow: auto;
        }
        
        .markdown-body table th {
            font-weight: 600;
        }
        
        .markdown-body table th,
        .markdown-body table td {
            padding: 6px 13px;
            border: 1px solid #dfe2e5;
        }
        
        .markdown-body table tr {
            background-color: #fff;
            border-top: 1px solid #c6cbd1;
        }
        
        .markdown-body table tr:nth-child(2n) {
            background-color: #f6f8fa;
        }
        
        .markdown-body img {
            max-width: 100;
            box-sizing: content-box;
            background-color: #fff;
        }
        
        .markdown-body img[align=right] {
            padding-left: 20px;
        }
        
        .markdown-body img[align=left] {
            padding-right: 20px;
        }
        
        .markdown-body code {
            padding: 0.2em 0.4em;
            margin: 0;
            background-color: rgba(27,31,35,0.05);
            border-radius: 3px;
        }
        
        .markdown-body pre {
            word-wrap: normal;
        }
        
        .markdown-body pre>code {
            padding: 0;
            margin: 0;
            word-break: normal;
            white-space: pre;
            background: transparent;
            border: 0;
        }
        
        .markdown-body .highlight {
            margin-bottom: 16px;
        }
        
        .markdown-body .highlight pre {
            margin-bottom: 0;
            word-break: normal;
        }
        
        .markdown-body .highlight pre,
        .markdown-body pre {
            padding: 16px;
            overflow: auto;
            font-size: 85;
            line-height: 1.45;
            background-color: #f6f8fa;
            border-radius: 3px;
        }
        
        .markdown-body pre code {
            display: inline;
            max-width: auto;
            padding: 0;
            margin: 0;
            overflow: visible;
            line-height: inherit;
            word-wrap: normal;
            background-color: transparent;
            border: 0;
        }
        
        .markdown-body .full-commit .btn-outline:not(:disabled):hover {
            color: #005cc5;
            border-color: #005cc5;
        }
        
        .markdown-body kbd {
            display: inline-block;
            padding: 3px 5px;
            font: 11px "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
            line-height: 10px;
            color: #444d56;
            vertical-align: middle;
            background-color: #fafbfc;
            border: solid 1px #d1d5da;
            border-bottom-color: #c6cbd1;
            border-radius: 3px;
            box-shadow: inset 0 -1px 0 #c6cbd1;
        }
        
        .markdown-body :checked+.radio-label {
            position: relative;
            z-index: 1;
            border-color: #0366d6;
        }
        
        .markdown-body .task-list-item {
            list-style-type: none;
        }
        
        .markdown-body .task-list-item+.task-list-item {
            margin-top: 3px;
        }
        
        .markdown-body .task-list-item input {
            margin: 0 0.2em 0.25em -1.6em;
            vertical-align: middle;
        }
        
        .markdown-body hr {
            border-bottom-color: #eee;
        }
        
        .markdown-body {
            box-sizing: border-box;
            min-width: 200px;
            max-width: 980px;
            margin: 0 auto;
            padding: 45px;
        }
        
        @media (max-width: 767px) {
            .markdown-body {
                padding: 15px;
            }
        }
        </style>
    </head>
    <body>
        <div class="markdown-body"><h1>Initial Research</h1>
<p>This work is for expanding upon the broad points discussed on 19th July with Prof. Gary Royle.</p>
<h2>All possible routes</h2>
<p>Computers applications can assist cancer research in the fields of medical imaging and diagnostics, drug discovery, automated treatment search and delivery, computer assisted clinical trials, patient advisory service, and predictive epidemiology.</p>
<p>Medical imaging and diagnostics involves can help with the detection of cancer forming cells, substances, cancerous cells, tumors, and cysts. Computer assisted radiology can make use of the application of artificial intelligence techniques in studying the radiological data, looking for patterns which could be useful in detecting cancerous cell growth and other cancer like conditions.</p>
<p>Computer based 'artificial intelligence' (AI) can help in medical research by performing expertanalysis of patient data, using the 'machine learning' (ML) techniques upon the available medical data by generating various models of inference like heuristic, statistical, data driven, or deep learning. The machine learning is a branch of artifical intelligence in which the computer 'learns' to evolve data processing algorithms based on the statistical data being processed. Machine learning algorithms involves the use of mathematical and statitical techniques for information discovery.</p>
<p>Deep learning is a branch within the machine learning which operates on very large data sets to 'train' or improve the algorithms and potentially 'learn' or 'discover' new 'features' or pieces of information from the processing of the data. An artificially intelligent system operates on certain premises or assumptions which again are based on previously available information from the field of study. One of the ML techniques is 'deep learning' where algorithmic analysis is performed upon large amounts of data sets and information models are built, which might lead to new 'information discovery' or extraction of useful 'features' which might have been previously unknown about the subject matter.</p>
<p>Transfer learning is a method used within deep learning where the models of information built previously on a large data set are then applied to another related data sets. Transfer learning is also inductive learning in which knowledge common to two potentially different domains of study could be used to cross train from one domain to another, and then used to discover more about the other related or completely different domain of study.</p>
<p>The machine learning benefits from the advances in availability of artifically intelligent computer applications, application libraries, and application platforms from all fields of artificial intelligence and applications of machine learning techniques.</p>
<p>AI applications could be developed to work as automated 'agents' acting between a patient and a medical consultant to improve the availability, scalability, and accuracy of treatment outcomes. The way AI could help in this matter would be by prototyping patient and consultant models, which would then communicate using a 'semantic gateway' to communicate in real time.</p>
<p>The term semantic gateway would be a space where data exists in machine readable form, so that AI agent applications could automatically catalogue the bits of information received or produced by the operating processes within the agent application. These 'AI agents' would be representing the real human beings who would be interacting with each other through these AI agents, with humans and AI agents both providing or consuming data at it's various stages of processing. We could call the AI agents representing human beings as 'AI avatar', 'avatar application', or 'avatar'.</p>
<p>Initial proposed list of the applications of artificial intelligence to the field of healthcare is as given below:</p>
<ul>
<li>Online Avatar for individuals and organisations</li>
<li>Avatar based representation of users of services and providers of those services</li>
<li>Patient Avatar based Medical Records</li>
<li>Crowdsourcing Patient Avatar Medical Records (see Apple’s ResearchKit)</li>
<li>Machine Learning in Radiology</li>
<li>Machine Learning in Cancer Treatment</li>
<li>Drug Discovery</li>
<li>Treatment Suggestions for Personalised Treatment</li>
<li>Treatment Recommendations and Clinical Trials</li>
<li>Patient Behavioural Advisory</li>
<li>Predictive Epidemiology</li>
</ul>
<h2>Why to choose a method</h2>
<p>Amongst the people of various demographic backgrounds the application of data processing by AI automated agents combined with the availability of previous data can iteratively improve the outcome of treatment. Iterative improvement is not just enough as it affects real people and the availability of data from previous proven treatment methods from across the globe can be potentially applied to improve treatment methods, data, models, and algorithms.</p>
<p>Transfer learning approach can be applied from majority demographic group for which extensive patient data is available, upon minority demographic groups for which the patient data available might be a small percentage of the entire set of patient dataset available.</p>
<h2>What is the criteria to choose a method</h2>
<p>Whilst it is possible to develop sophisticated machine leaning algorithms which operate on sparsely available datasets, it is always desirable to have more datapoint as higher number of data points drive automated machine learning techniques such as deep learning and are used as learning material for better developing such machine learning algorithms.</p>
<h2>What are the matrices of success</h2>
<p>There are several criterion to evaluate the approaches illustrated above for success in terms of feasibility, viability, and commercial success of each of those approaches.</p>
<p>The AI agent or avatar should be able to represent a user online as a representative and offline while interacting with the user. Online version of the avatar should be able to interact with other online medical specialists or researchers which could also be operating through their respective AI agents. These online researcher avatars could potentially provide expert guidance on possible treatments based on the medical data shared by the patient.</p>
<p>The AI agent should be a representation of real world human interactions conducted in real time based on patient and vendor information. The value of an end user provided data source is imperative in the age of data economy where the data drives all levels of interaction between people, organisations, businesses, and governments. The availability of people provided patient data will not only drive the medical research to new levels of delivering accurate and timely treatment to people of all demographic backgrounds, but it will also contribute to the fields of artificial intelligence and its important branch machine learning.</p>
<p>AI agent should be able to 'classify' or discover interesting 'artefacts' from the bits of information processed, this will help in 'making sense' of the data being processed or pre-processed for another stage of operation. An AI agent should also be able to apply quantification algorithms on the data being processed, as this will lead to faster extraction of key figures or 'statistics' from the the data being pprocessed when compared with the classifier processing time overheads.</p>
<p>AI agents should produce data which is 'reliable' and matrices which are of 'certain quality', as the data reliability would keep user base engaged in using the AI agent based avatar services. This qualitative approach to data processing would increase the possibility of a wider adoption of the AI agent based avatar service amongst its users.</p>
<h2>Cost and time criteria coming from commercial background</h2>
<p>The cost of setting up a communications channel for AI agent based system suitable for gathering data globally from the people in a location agnostic fashion is quite reasonable, considering the availability of cloud technologies such as Akamai for providing secure cloud communication solutions for data distribution amongst all parties concerned.</p>
<p>The cost and time savings in setting up compute solutions and resources is minimal these days with cloud compute vendors such as Google/AWS/Azure all of whom deliver their cloud based compute resources in a software defined fashion called Platform As A Service (PaaS), all of which are relatively easy to setup and capable of delivering extremely scalable and feature rich solutions thus making it easy to deliver production grade software services.</p>
<p>Academic, and scientific that is more balanced approach
The academic research is extremely important part of digging out the depth of possibilities and potential solutions based the current body of knowledge available world wide in the academia and institutions of knowledge.</p>
<p>The commercial exposure brings the completion of this academic pursuit in that it becomes easier to design, develop, and deploy the requisite software artefacts successfully and thus providing a scope to run several potentially powerful and useful thoughts and ideas coming from the basic academic in the medical industry in general, medical physics and compute scientific research and engineering in particular.</p>
<h2>References</h2>
<p>Chapter 1. What is epidemiology?, thebmj: https://www.bmj.com/about-bmj/resources-readers/publications/epidemiology-uninitiated/1-what-epidemiology
http://www.helsinki.fi/esslli/courses/readers/K10.pdf
https://www.datasciencecentral.com/profiles/blogs/automated-deep-learning-so-simple-anyone-can-do-it
https://www.kofax.com/Go/2019/kofax-intelligent-automation-platform
https://www.datarobot.com/lp/see-automl-will-transform-business/
https://pages.matillion.com/machine-learning-ebook?utm_source=google&amp;utm_medium=cpc&amp;utm_campaign=User_Guide_Machine_Learning_EMEA&amp;utm_term=ebook_machine%2520learning&amp;ckmscn=***********&amp;gclid=Cj0KCQjwho7rBRDxARIsAJ5nhFpvh7i4a2IO_Q6zZnG1anH9FkhgIc7WAUVH2SUFqOVZLeb_l2WPZrgaAjmgEALw_wcB
https://scholar.google.co.uk/scholar?q=automated+machine+learning+techniques&amp;hl=en&amp;as_sdt=0&amp;as_vis=1&amp;oi=scholart
https://en.m.wikipedia.org/wiki/Automated_machine_learning
https://skymind.ai/wiki/automl-automated-machine-learning-ai
https://www.ml4aad.org/automl/
https://docs.microsoft.com/en-us/azure/machine-learning/service/concept-automated-ml
https://www.kdnuggets.com/2019/01/automated-machine-learning-python.html
https://medium.com/thinkgradient/automated-machine-learning-an-overview-5a3595d5c4b5
https://ml.informatik.uni-freiburg.de/papers/15-NIPS-auto-sklearn-preprint.pdf
https://subscription.packtpub.com/video/big_data_and_business_intelligence/9781789800289?uuid=97511409-3f40-4719-b05a-19f61b9e88a3
https://subscription.packtpub.com/video/big_data_and_business_intelligence/9781789530087?uuid=97511409-3f40-4719-b05a-19f61b9e88a3
https://en.wikipedia.org/wiki/Artificial_intelligence_in_healthcare</p>
<h2>Commercial applications</h2>
<p>https://www.nvidia.com/en-us/industries/healthcare-life-sciences/
https://orionhealth.com/uk/knowledge-hub/reports/machine-learning-in-healthcare/
https://peltarion.com/platform?gclid=CjwKCAjwzdLrBRBiEiwAEHrAYlGSxul3MG-pr5cjefB2RhdyzXwb071uowocwVPaqNtAdqL0XN_IpBoCfakQAvD_BwE
https://c3.ai/c3-ai-suite/?mkwid=smj1EfYxT_dc|pcrid|************|pmt|b|pkw|%2Bmachine%20%2Blearning|slid||targetids|kwd-***********|groupid|***********|&amp;pgrid=***********&amp;ptaid=kwd-***********&amp;gclid=CjwKCAjwzdLrBRBiEiwAEHrAYg7UmGih1BeGIxRLpP7j0sPpPYTC-_6iBD7XhcGWJ8wycP76gMwJDRoCA-cQAvD_BwE
https://www.genpact.com/industries/healthcare?&amp;gclid=CjwKCAjwzdLrBRBiEiwAEHrAYg6pTrfV8VRUORZPQ-fdipZgi1DBqPI0LHojRQnlTLb-4EMjAhgwehoCvPAQAvD_BwE</p>
<h2>Sources of data</h2>
<p>Cancer research UK statistics, with loads of metrics and statistics for patient outcome https://www.cancerresearchuk.org/health-professional/cancer-statistics-for-the-uk</p>
</div>
    </body>
</html>
