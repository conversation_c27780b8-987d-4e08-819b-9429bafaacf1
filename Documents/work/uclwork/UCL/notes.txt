Credentials.
02089095719
Please see below for your IT system and NHS Mail credentials, thanks.
User Login - <PERSON><PERSON>.Sharma
Password: Bluegreen1234 (password is case sensitive). Changed to NewSolidPassword123
Email address: <EMAIL>
Password: a5gb-feh3-mpfl (password is case sensitive). Changed to NewSolidPassword123
Memorable word: MeerutCity

Work to do.

ETL:
- Make anonymisation essential part of the transform layer, not skippable.
  - Hashing of the source field identifiers such as patient identity, etc.
  - Third party pseudonimisation lookup table based on key values
  - Patient timelines (dates of birth, surgery, diagnosis, death, etc.) need to replaced by the age of patient in years and months

Four pieces of work:
- New RNOH sample management system (Lab Ware?)
- New clinical data model for RNOH (OMOP CDM)
- Mapping of Lab Ware clinical data to OMOP CDM
- De-identification of SVS (within a new OMOP CDM plugin?)
- De-identification of SVS (within a new PIXL module for RNOH?)

- New sample management system
- New clinical DB (part of LabWare)
- Migration of legacy samples and data
- Integration with MediLims
- Mapping of Labware clinical data -> OMOP
- Copying of clinical files to temp research store prior to pathology reporting
- De-identifcation of SVS
- Safe tranfer to UCL research architecture
  - OMERO (digital pathology slides)
  - OMOP Database

Work In Progress (WIP).

Listed below are the three main areas of focus with concrete coding or development work, ordered by receding priority.

1. Select one of the synthetic data sources (with known vocabulary) and construct the data ingestion pipeline from the data source to the OMOP CDM.
Work on the code sample to import the SVS image formatted files and de-identifying the data on them, complete project with code and unit and integration test cases.
2. Refactor parts of the PIXL pipeline to introduce a new pathology-api talking to the RabbitMQ message bus, using existing Azure Key Vault to get credentials for the
3. Data Safe Haven (DSH) FTPS connection to an FTP server (pending the details of the connection to the Azure infrastructure, target FTP server, and running the PIXL locally as well on the UCL VMs).

1. Synthea for ETL development.

OMOP conversion process:
- Data source: Use Synthea (https://github.com/synthetichealth/synthea) for obtaining the synthetic patient data.
- Analysis phase:
  - Scan Data with WhiteRabbit (https://github.com/OHDSI/WhiteRabbit)
  - Use Rabbit-in-a-Hat for mapping source data fields to the OMOP CDM tables (Rabbit-in-a-Hat comes with WhiteRabbit)
  - Vocabularies: Are mappings of unique concept IDs from the OMOP CDM to their definitions found in medical vocabularies
    - Standard vocabularies: Only one standard vocabulary exists for a standard (unique) concept ID defined in OMOP
      - Conditions: SNOMED vocabulary
      - Drugs: RxNorm or RxNorm Extension vocabulary
- Quality control phase:
  - Use ACHILLES for descriptive statistics (broad database characterisations) on OMOP CDM database (https://github.com/OHDSI/Achilles)
  - Use DataQualityDashboard for systematic data quality checks (https://github.com/OHDSI/DataQualityDashboard)
  - Use Iris for high-level descriptive summary of a population
  - Use ARES to display the results from both the ACHILLES and DataQualityDashboard packages to support data quality and characterization research (https://github.com/OHDSI/Ares)
- Development
  - Create ETL Pipeline using CMake based C++ and Docker compose
  - Test pipeline running locally with one of these local Continuous Integration (CI) tools - act (https://github.com/nektos/act), or Concourse (https://concourse-ci.org/)
  - Test integration with a cloud based CI engine - GitHub Actions (https://developer.github.com/actions/), AWS CodeBuild (https://aws.amazon.com/codebuild/), or Azure DeveOps (https://azure.microsoft.com/en-us/products/devops)

# Glossary

Data Safe Haven (DSH) through an FTPS connection to an FTP server.
VNA - Vendor Neutral Archive. VNA serves as a centralised storage solution for medical images and associated data, regardless of the specific imaging devices or systems used to generate those images.
XNAT - eXtensible Neuroimaging Archive Toolkit. It is an open-source imaging informatics software platform designed to facilitate imaging-based research.
OMERO - Open Microscopy Environment Remote Objects. It is an open-source platform for managing, visualizing, and analyzing biological imaging data.
OHDSI - Observational Health Data Sciences and Informatics. It is an international collaborative effort that uses this standardised data to generate evidence for better health decisions and care through large-scale analytics.
OMOP CDM - Observational Medical Outcomes Partnership Common Data Model. It standardises the structure and content of observational healthcare data to facilitate collaborative research and analysis.
NPIC - Digital PACS system for digital pathology, developed by a consortium, which is a spin out from Leeds University.
PIXL - 

# Progress
1. C++ implementation of the ETL classes
2. Updated class/sequence/state diagrams
3. Extended transformation rules

1. Add more table-specific transformations?
2. Expand the validation framework?
3. Add more complex transformation rules?

Add more build configurations?
Include additional CI/CD steps?
Add security scanning?
Include performance testing?

Would you like me to:
1. Add more connector implementations?
2. Add more test cases?
3. Implement specific OMOP data type handling?
4. Add connection pooling?

Would you like me to:
Add more validation rules?
Include additional configuration formats?
Add caching mechanisms?
Include more error handling?

Create a docker environment which is used to run this Ansible Playbook, add relevant packages to this docker environment. Also create a shell script which takes ansible commands and passes them on to the Ansible command within the docker container.

# Project structure
ls -R src | python3 -c "
import sys
import yaml

def parse_ls_output(lines):
    structure = {}
    current_dir = '.'
    for line in lines:
        line = line.strip()
        if line.endswith(':'):
            current_dir = line[:-1]
            structure[current_dir] = []
        elif line and not line.startswith('total'):
            structure.setdefault(current_dir, []).append(line)
    return structure

ls_output = sys.stdin.readlines()
folder_structure = parse_ls_output(ls_output)
print(yaml.dump(folder_structure, default_flow_style=False))
" | grep -Ev "(\.md)|(\.txt)|(\.yml)|(\.sh)|(\.py)|(\.in)|(cmake)|(CMakeLists\.txt)"

# File dumps
Source files.

find src/app/api -type f \( -name '*.cpp' -o -name '*.h' -o -name '*.csv' -o -name '*.json' -o -name '*.yaml' -o -name '*.sql' -o -name 'CMakeLists.txt' \) -print0 | while IFS= read -r -d '' file; do
  echo -e "File $file:\n"
  cat "$file"
  echo    # Print a blank line between files
  echo    # Print a blank line between files
done > dump.txt

pbcopy < dump.txt

find docs/ai -type f \( -name '*.md' \) -print0 | while IFS= read -r -d '' file; do
  echo -e "File $file:\n"
  cat "$file"
  echo    # Print a blank line between files
  echo    # Print a blank line between files
done > dump.txt

pbcopy < dump.txt

find src/lib/extract -type f \( -name '*.cpp' -o -name '*.h' \) -print0 | while IFS= read -r -d '' file; do
  echo -e "File $file:\n"
  cat "$file"
  echo    # Print a blank line between files
  echo    # Print a blank line between files
done > dump.txt

pbcopy < dump.txt

find tests/integration/test_helpers -type f \( -name '*.h' \) -print0 | while IFS= read -r -d '' file; do
  echo -e "File $file:\n"
  cat "$file"
  echo    # Print a blank line between files
  echo    # Print a blank line between files
done > dump.txt

pbcopy < dump.txt

find src/lib/cdm -type f \( -name '*.cpp' -o -name '*.h' \) -print0 | while IFS= read -r -d '' file; do
  echo -e "File $file:\n"
  cat "$file"
  echo    # Print a blank line between files
  echo    # Print a blank line between files
done > dump.txt

pbcopy < dump.txt

Prune a sub-directory for a shorter dump.

find src/lib \( -path src/lib/transform -prune \) -o -type f -name '*.h' -print0 | while IFS= read -r -d '' file; do
  echo -e "File $file:\n"
  cat "$file"
  echo    # Print a blank line between files
  echo    # Print a blank line between files
done > dump.txt

pbcopy < dump.txt

find . -type f \( -name '*.patch' \) -print0 | while IFS= read -r -d '' file; do
  echo -e "File $file:\n"
  cat "$file"
  echo    # Print a blank line between files
  echo    # Print a blank line between files
done > dump.txt

pbcopy < dump.txt

Test cases.

find tests/unit/extract -type f \( -name '*.cpp' -o -name '*.h' -o -name '*.csv' -o -name '*.json' -o -name '*.yaml' -o -name '*.sql' -o -name 'CMakeLists.txt' \) -print0 | while IFS= read -r -d '' file; do
  echo -e "File $file:\n"
  cat "$file"
  echo    # Print a blank line between files
  echo    # Print a blank line between files
done > dump.txt

pbcopy < dump.txt

find tests/integration/security -type f \( -name '*.cpp' -o -name '*.h' -o -name '*.csv' -o -name '*.json' -o -name '*.yaml' -o -name '*.sql' -o -name 'CMakeLists.txt' \) -print0 | while IFS= read -r -d '' file; do
  echo -e "File $file:\n"
  cat "$file"
  echo    # Print a blank line between files
  echo    # Print a blank line between files
done > dump.txt

pbcopy < dump.txt

# Work
omop-etl.

- Encryption class should be extended with FIPS 140-2 compliant modules for PHI at rest.
- RedisCache implementation should be extended with invalidation policies tied to vocabulary update events from OHDSI’s Athena service.
- A more unified error–handling framework could simplify debugging and improve resilience.
- It might be useful to ensure that dynamic reloading or runtime overrides (especially for critical parameters such as database connection details or batch sizes) are supported.
- Adding dedicated test harnesses or integration test modules can be beneficial for continuous validation of both the ETL logic and data quality rules.
- The connector and adapter design seems modular. Ensure that it’s straightforward to add new connectors (for example, for Oracle databases or other emerging standards) without impacting existing code.
- While monitoring classes (like ETLMetricsCollector, ETLProgressMonitor, and HealthMonitor) are in place, consider expanding them to support real–time dashboards or integration with popular monitoring systems if not already provided.
- The presence of an APIDocumentationGenerator is promising. Maintaining clear, auto–generated documentation (e.g., via OpenAPI/Swagger for REST endpoints) will be key to making the system accessible to integrators and developers.
- Consider consolidating cache strategies under a more unified interface.
- Missing components / features:
  - Documentation generation for ETL processes and data lineage
  - Versioning control for ETL process definitions
  - More advanced vocabulary mappings for specific healthcare domains
  - Extended FHIR integration for modern healthcare interoperability
- Key gaps:
  - Limited support for concept relationship resolution
  - Incomplete implementation of THEMIS ETL conventions for observational data harmonization
  - Future work should integrate OHDSI’s Atlas cohort definition API directly into the transformation layer for real-time analytics support
  - Potential need for internationalization support
- Three pillars of robust OMOP CDM implementations:
  - Vocabulary resolution completeness
  - Temporal validity enforcement
  - Intermediate staging
- Immediate implementation priorities should be:
  - Implement VocabularyResolutionEngine with concept relationship support
  - Introduce TemporalIntegrityService for event sequencing
  - Develop StagingSchemaManager with WhiteRabbit integration
  - Extend PHIAnonymizer with statistical disclosure controls

scripts:
# Run all unit tests (default behavior)
./scripts/dev-test.sh

# Run extract library unit tests only
./scripts/dev-test.sh -l extract

# Run core library integration tests
./scripts/dev-test.sh -l core -T integration

# Run specific connection pool tests in extract library
./scripts/dev-test.sh -l extract -t "ConnectionPool*"

# Force rebuild and run all test types for transform library
./scripts/dev-test.sh -l transform -T all -f

# Run all libraries with verbose output
./scripts/dev-test.sh -v -T all

CMake Preset:
cmake --preset x86_64-release -DCODE_COVERAGE:BOOL=false

CMake Build:
cmake --build --preset x86_64-release --config Release

Docker Build:

Start.
docker run -d --name omop-etl-dev-container -v "$(pwd):/workspace" omop-etl-dev tail -f /dev/null

Clean.
docker stop omop-etl-dev-container && docker rm omop-etl-dev-container

Configure.
docker exec omop-etl-dev-container bash -c "cd /workspace && cmake --preset docker-debug"
docker exec omop-etl-dev-container bash -c "cd /workspace && cmake --preset docker-debug -DBUILD_TESTS=ON"

Build.
docker exec omop-etl-dev-container bash -c "cd /workspace && cmake --build --preset docker-debug --parallel 4"
docker exec omop-etl-dev-container bash -c "cd /workspace && cmake --build --preset docker-debug --parallel 4 --target run_all_tests"

Run unit tests.
Run CTest in a standalone container (omop-etl-dev-container) in the /workspace directory with verbose output for failed tests:
docker exec omop-etl-dev-container bash -c "cd /workspace && ctest --output-on-failure --verbose"

Run within a Docker Compose setup, targeting the omop-etl-dev service, executing tests in build/docker-debug with a timeout, and filtering the output for key test results:
docker-compose -f scripts/docker-compose.yml --profile dev exec omop-etl-dev bash -c "cd build/docker-debug && ctest --output-on-failure --verbose --timeout 30 | grep -E '(PASSED|FAILED|Test.*:|tests ran|tests from)' | head -100"

Run multiple unit tests using Docker Compose:
docker-compose --profile dev run --rm omop-etl-dev ctest --output-on-failure --verbose -R "loader_base_test|database_loader_test|additional_loaders_test"

docker-compose --profile dev run --rm omop-etl-dev ctest --output-on-failure --verbose -R "loader_base_test|database_loader_test|additional_loaders_test"

Run integration tests.
docker exec omop-etl-dev-container bash -c "cd /workspace/build/docker-debug && ./tests/unit/cdm/test_cdm_all"
docker exec omop-etl-dev-container bash -c "cd /workspace/build/docker-debug && find . -name '*test*' -type f -executable | grep -E '(extract|load|transform)' | head -10"
docker exec omop-etl-dev-container bash -c "cd /workspace/build/docker-debug && ./bin/batch_loader_test"

CTest.

Basic CTest Commands

Run all tests:
docker-compose -f scripts/docker-compose.yml --profile dev exec omop-etl-dev bash -c "cd /workspace/build/docker-debug && ctest"

Run only unit tests:
docker-compose -f scripts/docker-compose.yml --profile dev exec omop-etl-dev bash -c "cd /workspace/build/docker-debug && ctest -L unit"

Run only integration tests:
docker-compose -f scripts/docker-compose.yml --profile dev exec omop-etl-dev bash -c "cd /workspace/build/docker-debug && ctest -L integration"

Component-Specific Unit Tests

CDM unit tests:
docker-compose -f scripts/docker-compose.yml --profile dev exec omop-etl-dev bash -c "cd /workspace/build/docker-debug && ctest -R test_omop_cdm_unit"

Core unit tests:
docker-compose -f scripts/docker-compose.yml --profile dev exec omop-etl-dev bash -c "cd /workspace/build/docker-debug && ctest -R test_omop_core_unit"

Common unit tests:
docker-compose -f scripts/docker-compose.yml --profile dev exec omop-etl-dev bash -c "cd /workspace/build/docker-debug && ctest -R test_omop_common_unit"

Extract unit tests:
docker-compose -f scripts/docker-compose.yml --profile dev exec omop-etl-dev bash -c "cd /workspace/build/docker-debug && ctest -R test_omop_extract_unit"

Transform unit tests:
docker-compose -f scripts/docker-compose.yml --profile dev exec omop-etl-dev bash -c "cd /workspace/build/docker-debug && ctest -R test_omop_transform_unit"

Load unit tests:
docker-compose -f scripts/docker-compose.yml --profile dev exec omop-etl-dev bash -c "cd /workspace/build/docker-debug && ctest -R test_omop_load_unit"

Integration Tests by Component

CDM integration tests:
docker-compose -f scripts/docker-compose.yml --profile dev exec omop-etl-dev bash -c "cd /workspace/build/docker-debug && ctest -R cdm_integration_tests"

Core integration tests:
docker-compose -f scripts/docker-compose.yml --profile dev exec omop-etl-dev bash -c "cd /workspace/build/docker-debug && ctest -R core_integration_tests"

Useful CTest Options

Verbose output:
docker-compose -f scripts/docker-compose.yml --profile dev exec omop-etl-dev bash -c "cd /workspace/build/docker-debug && ctest --verbose"

Parallel execution:
docker-compose -f scripts/docker-compose.yml --profile dev exec omop-etl-dev bash -c "cd /workspace/build/docker-debug && ctest -j4"

Stop on first failure:
docker-compose -f scripts/docker-compose.yml --profile dev exec omop-etl-dev bash -c "cd /workspace/build/docker-debug && ctest --stop-on-failure"

List available tests:
docker-compose -f scripts/docker-compose.yml --profile dev exec omop-etl-dev bash -c "cd /workspace/build/docker-debug && ctest --show-only"

Run specific test pattern:
docker-compose -f scripts/docker-compose.yml --profile dev exec omop-etl-dev bash -c "cd /workspace/build/docker-debug && ctest -R 'test_omop.*unit'

Valgrind.

The debug the segfault issues.

docker exec -it omop-etl-dev valgrind --tool=memcheck --leak-check=full --show-leak-kinds=all --track-origins=yes --verbose --log-file=valgrind_output.txt /workspace/build/docker-debug/bin/cdm_integration_tests

docker exec -it omop-etl-dev cat /workspace/valgrind_output.txt

XNAT.

Tasks:

1. Make the current OHIF plugin working with XNAT.
2. Create a plugin for the latest OHIF viewer, the OHIF site - https://ohif.org/, the GitHub site - https://github.com/OHIF/Viewers.

Current Ansible work:

For a VM configured on Rancher using Harvester, I need to create code repository structure with code using Harvester Terraform provider to configure to create the VMs. I need to enable and configure Ingress to VM, configure the VM, configure VM to run the Ansible, and run the Ansible code to deploy XNAT using the above repository. To create a basic VM configured for SSH access use this GitHub public repository as an example  - https://github.com/UCL-ARC/terraform-harvester-vm-demo. Although the modules, components and scripts folders are yet to be created. The VM Ingress also needs to be enabled using terraform. The VM recources need to be configured using Helm charts with Helmsman. I have pasted a guide for configuring Ingress, terraform and kubectl.

xnat_svc
Kite@9Surf4Pizza

xnat
Lone99StrangE3$

curl -s -X POST -u admin:admin http://localhost:8080/setup -d "dbName=xnat&dbHost=***********&dbUser=xnat&dbPass=Lone99StrangE3$"

curl -s -X GET -u admin:admin http://localhost:8080/xapi/system/version

curl -s -X GET -u admin:admin http://localhost:8080/xapi/siteConfig
curl -s -X GET -u xnat_svc:Kite@9Surf4Pizza http://localhost:8080/xapi/siteConfig
curl -s -X GET -u xnat_svc:Kite@9Surf4Pizza http://localhost:8080/xapi/siteConfig/ohif.viewer.default

curl -s -X PUT -u xnat_svc:Kite@9Surf4Pizza \
  -H "Content-Type: application/json" \
  --data '{"containers":{"enabled":false}}' \
  http://localhost:8080/xapi/siteConfig

curl -s -X PUT -u xnat_svc:Kite@9Surf4Pizza \
  -H "Content-Type: application/json" \
  -d '{"ohif.viewer.default":"true"}' \
  http://localhost:8080/xapi/siteConfig

curl -s -X GET -u admin:admin http://localhost:8080/setup/status
curl -s -X GET -u admin:admin http://localhost:8080/xapi/users

curl -s -X GET -u admin:admin http://localhost:8080/xapi/plugins
curl -s -X GET -u xnat_svc:Kite@9Surf4Pizza http://localhost:8080/xapi/plugins

curl -s -X GET -u xnat_svc:Kite@9Surf4Pizza http://localhost:8080//xnat/pluginAdmin

curl -s -X GET -u xnat_svc:Kite@9Surf4Pizza http://localhost:8080/xapi/viewer/projects/TestProject1/experiments/UCDE_XNAT_E00001

Packet capture.

tcpdump -vvv -lvi any "tcp port 8080" -w httpalt.log

Run the Tomcat server manually.

CATALINA_BASE=/var/lib/tomcat9 CATALINA_HOME=/usr/share/tomcat9 java --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED -Djava.util.logging.config.file=/var/lib/tomcat9/conf/logging.properties -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager -Djava.awt.headless=true -Djdk.tls.ephemeralDHKeySize=2048 -Djava.protocol.handler.pkgs=org.apache.catalina.webresources -Dorg.apache.catalina.security.SecurityListener.UMASK=0027 -Dxnat.home=/data/xnat/home -Djava.net.preferIPv4Stack=true -Dcatalina.base=/var/lib/tomcat9 -Dcatalina.home=/usr/share/tomcat9 -Djava.io.tmpdir=/var/lib/tomcat9/temp

Try a URL till it succeeds.

URL="https://xnat-web.medp-proj-cde.condenser.arc.ucl.ac.uk"; while [ "$(curl -s -o /dev/null -w '%{http_code}' "$URL")" = "404" ]; do sleep 5; done; echo "Success: $URL is now available"
