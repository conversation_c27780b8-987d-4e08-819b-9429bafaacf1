apiVersion: kubevirt.io/v1
kind: VirtualMachine
metadata:
  annotations:
    field.cattle.io/description: ETL Server VM
    harvesterhci.io/vmRunStrategy: RerunOnFailure
    harvesterhci.io/volumeClaimTemplates: >-
      [{"metadata":{"name":"etl-vm-01-disk-0-elfvx","annotations":{"harvesterhci.io/imageId":"harvester-public/image-hn5cg"}},"spec":{"accessModes":["ReadWriteMany"],"resources":{"requests":{"storage":"15Gi"}},"volumeMode":"Block","storageClassName":"longhorn-image-hn5cg"}}]
    kubevirt.io/latest-observed-api-version: v1
    kubevirt.io/storage-observed-api-version: v1
    network.harvesterhci.io/ips: '[]'
  creationTimestamp: '2025-02-28T15:54:04Z'
  finalizers:
    - harvesterhci.io/VMController.UnsetOwnerOfPVCs
    - kubevirt.io/virtualMachineControllerFinalize
  generation: 4
  labels:
    harvesterhci.io/creator: harvester
    harvesterhci.io/os: linux
  managedFields:
    - apiVersion: kubevirt.io/v1
      fieldsType: FieldsV1
      fieldsV1:
        f:metadata:
          f:annotations:
            .: {}
            f:field.cattle.io/description: {}
            f:harvesterhci.io/vmRunStrategy: {}
            f:harvesterhci.io/volumeClaimTemplates: {}
            f:network.harvesterhci.io/ips: {}
          f:finalizers:
            .: {}
            v:"harvesterhci.io/VMController.UnsetOwnerOfPVCs": {}
          f:labels:
            .: {}
            f:harvesterhci.io/creator: {}
            f:harvesterhci.io/os: {}
        f:spec:
          .: {}
          f:template:
            .: {}
            f:metadata:
              .: {}
              f:annotations:
                .: {}
                f:harvesterhci.io/sshNames: {}
              f:labels:
                .: {}
                f:harvesterhci.io/vmName: {}
            f:spec:
              .: {}
              f:affinity: {}
              f:domain:
                .: {}
                f:cpu:
                  .: {}
                  f:cores: {}
                  f:sockets: {}
                  f:threads: {}
                f:devices:
                  .: {}
                  f:disks: {}
                  f:inputs: {}
                  f:interfaces: {}
                f:features:
                  .: {}
                  f:acpi:
                    .: {}
                    f:enabled: {}
                f:machine:
                  .: {}
                  f:type: {}
                f:resources:
                  .: {}
                  f:limits:
                    .: {}
                    f:cpu: {}
                    f:memory: {}
              f:evictionStrategy: {}
              f:hostname: {}
              f:networks: {}
              f:terminationGracePeriodSeconds: {}
              f:volumes: {}
      manager: harvester
      operation: Update
      time: '2025-02-28T15:56:45Z'
    - apiVersion: kubevirt.io/v1
      fieldsType: FieldsV1
      fieldsV1:
        f:metadata:
          f:annotations:
            f:kubevirt.io/latest-observed-api-version: {}
            f:kubevirt.io/storage-observed-api-version: {}
          f:finalizers:
            v:"kubevirt.io/virtualMachineControllerFinalize": {}
        f:spec:
          f:runStrategy: {}
      manager: Go-http-client
      operation: Update
      time: '2025-03-04T15:20:54Z'
    - apiVersion: kubevirt.io/v1
      fieldsType: FieldsV1
      fieldsV1:
        f:status:
          .: {}
          f:conditions: {}
          f:created: {}
          f:desiredGeneration: {}
          f:observedGeneration: {}
          f:printableStatus: {}
          f:ready: {}
          f:volumeSnapshotStatuses: {}
      manager: Go-http-client
      operation: Update
      subresource: status
      time: '2025-03-04T15:22:03Z'
  name: etl-vm-01
  namespace: medp-proj-cde-ns
  resourceVersion: '625368736'
  uid: 488aa91b-0137-418f-a4b6-18c87cad79c0
spec:
  runStrategy: RerunOnFailure
  template:
    metadata:
      annotations:
        harvesterhci.io/sshNames: '["medp-proj-cde-ns/jw-pub-key","medp-proj-cde-ns/ns-condenser-pub"]'
      creationTimestamp: null
      labels:
        harvesterhci.io/vmName: etl-vm-01
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: network.harvesterhci.io/mgmt
                    operator: In
                    values:
                      - 'true'
      architecture: amd64
      domain:
        cpu:
          cores: 4
          sockets: 1
          threads: 1
        devices:
          disks:
            - bootOrder: 1
              disk:
                bus: virtio
              name: disk-0
            - disk:
                bus: virtio
              name: cloudinitdisk
          inputs:
            - bus: usb
              name: tablet
              type: tablet
          interfaces:
            - bridge: {}
              macAddress: 66:c2:dc:12:42:0d
              model: virtio
              name: default
        features:
          acpi:
            enabled: true
        machine:
          type: q35
        memory:
          guest: 16284Mi
        resources:
          limits:
            cpu: '4'
            memory: 16Gi
          requests:
            cpu: 250m
            memory: 10922Mi
      evictionStrategy: LiveMigrate
      hostname: etl-vm-01
      networks:
        - multus:
            networkName: medp-proj-cde-ns/medp-proj-cde-net
          name: default
      terminationGracePeriodSeconds: 120
      volumes:
        - name: disk-0
          persistentVolumeClaim:
            claimName: etl-vm-01-disk-0-elfvx
        - cloudInitNoCloud:
            networkDataSecretRef:
              name: etl-vm-01-bwqva
            secretRef:
              name: etl-vm-01-bwqva
          name: cloudinitdisk
status:
  conditions:
    - lastProbeTime: null
      lastTransitionTime: '2025-03-04T15:21:10Z'
      status: 'True'
      type: Ready
    - lastProbeTime: null
      lastTransitionTime: null
      status: 'True'
      type: LiveMigratable
    - lastProbeTime: '2025-03-04T15:22:03Z'
      lastTransitionTime: null
      status: 'True'
      type: AgentConnected
  created: true
  desiredGeneration: 4
  observedGeneration: 4
  printableStatus: Running
  ready: true
  volumeSnapshotStatuses:
    - enabled: false
      name: disk-0
      reason: 2 matching VolumeSnapshotClasses for longhorn-image-hn5cg
    - enabled: false
      name: cloudinitdisk
      reason: Snapshot is not supported for this volumeSource type [cloudinitdisk]