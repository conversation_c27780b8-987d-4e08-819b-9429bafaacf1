This project is an implementation of OMOP CDM pipeline using CMake and standard C++ project to create the ETL pipeline application. Please produce a robust standard C++ based complete implementation, complete with C++ base and implementation classes, producing a layered project structure containing multiple CMake files in subfolders containing various project sub-modules, which are listed further below. The project should be platform neutral and use latest C++ standard specification. The project should also contains src directory for source. Use Doxygen comments for all the class member functions and data members.

This C++20 project provides a flexible, high-performance framework for healthcare data transformation with support for multiple data sources and use configuration files in YAML format, for mapping data sources to OMOP OHDSI compliant CDM database tables.

In effect the mapping configuration YAML files is used to perform the transformation from the source data (which could be in CSV format or a custom database format), to the target OMOP CDM database tables.

Much work has been done on the ETL OMOP libraries. But the application interface and API including the REST and gRPC services for the ETL application need to be developed further to match the contents of the ETL OMOP library implementation code, as the implementation of the application is lagging behind the library development significantly.

Check the C++ source code header and implementation files in the project contained in the folder src/app, and fix any coding issues, bugs, assumptions, completeness, build failures, and errors. Fix the source code header and implementation files in the source code header and implementation files in the project folder src/app for the issues discovered in exisitng code including removing un-implemented code and removing TODO code snippets, basic / demo implementation, stub methods, refactor existing code in the folder src/app as required. Do not just remove the unimplemented code like TODOs, instead implement any code functionality found missing, not implemented, coding assumptions, or incomplete implementations.

Ensure that the src/app application's unit test cases contained in the folder tests/unit, have 100% code coverage, and fix the test files for any coding issues, bugs, assumptions, completeness, build failures, errors, and code coverage issues in these test cases. Also, check whether all these test cases have one line comment just before the test case explaining what the test case does. Ensure these tests use UK region localised date time, currency, decimal placement, temperature, postal code, amongst other regional differences. Fix the src/app application's unit test cases contained in the folder tests/unit for the issues discovered in exisitng code including removing un-implemented code and removing TODO code snippets, basic / demo implementation, stub methods, refactor existing test code in the folder tests/unit as required. Do not just remove the unimplemented code like TODOs, instead properly implement any code functionality found missing, not implemented, coding assumptions, or incomplete implementations.

Ensure that the src/app application's integration test cases contained in the folder tests/integration, have 100% feature coverage, and fix the test files for any coding issues, bugs, assumptions, completeness, build failures, errors, and code coverage issues in these test cases. Also, check whether all these test cases have one line comment just before the test case explaining what the test case does. Ensure these tests use UK region localised date time, currency, decimal placement, temperature, postal code, amongst other regional differences. Fix the src/app application's integration test cases contained in the folder tests/integration for the issues discovered in exisitng code including removing un-implemented code and removing TODO code snippets, basic / demo implementation, stub methods, refactor existing test code in the folder tests/integration as required. Do not just remove the unimplemented code like TODOs, instead properly implement any code functionality found missing, not implemented, coding assumptions, or incomplete implementations. Remove mock classes or don't use mock classes in the integration test cases. Use real database connection using the database containers which have been created using the docker build scripts. Feel free to add a new docker container if needed for any missing database or any other service that the integration tests might require.

This is a C++20 project, but be pragmatic in your approach towards enforcing any specific C++ standard strategy. Focus on elegance and also the current best industrial practices for the C++20 projects and for C++ projects in general.

Build the application and run the unit and integration test cases for the application. If necessary, make fixes to the source code and unit and integration test cases directly in the project's root directory to the actual project source and test files within the src/app, tests/unit, and tests/integration directories.

Don't use PIMPL idiom at all. Just implement all the functionality within main class's member functions themselves with all the private data members and implementation within the main class, don't create a contained PIMPL class.

Do not delete exiting application source code or test cases, just add new test cases, if you have refactor the test cases and application source code then be careful not to remove existing functionality.

Do not change application implementation code to fit the unit or integration test cases. Instead fix the test cases to match the application functionality. Also, do not simplify the test cases instead keep them to provide better code coverage.

Don't use the parent folder path in the include directives like "../", as the includes are supposed to be handled by the CMake include and library include directives defined in the CMake files. Moreover the CMake files are already working correctly for the library code, but may be they need to be similarly updated for the application CMake files. See examples of the CMake files in the src/lib directory.

Instead of using (void) to suppress the unused variables warning, use maybe_unused directive.

Make fixes to the application source code, unit, and integration test cases in the project's root directory to the actual project source and test files, i.e. the src/app, tests/unit, and tests/integration directories, do not use arbitrary directories like build_test build-test etc. Do not remove any existing code or functionality, or try to skip the test cases, or remove the functionality or test cases. Instead focus should be on implementing the missing assumptions and functionality, and then rebuilding and re-testing and fixing the test cases.

Do not comment out or remove functionality from src/app directory source code. Do not comment out or remove test cases and test case assetions from the tests/unit and tests/integration directories for the test cases code in order to make them pass or remove functionality. Instead add, implement, fix, refactor source code or test code to fix any code related issues, broken, missing, failed, or stubbed test cases.

Please also check and fix and enable test cases which are failed, disabled, skipped, stubbed, or errors.

Run all tests within the docker containers provided. There are separate docker containers for dev, and various source and target databases. Use docker containers and valgrind if needed for debugging purposes.

Commands for building, running unit and integration test cases for application are given in the documentation file docs/development/docker-build-and-testing-guide.md.



Give a breakup of how many unit and integration test cases are passing, failing, erroring for the application. 

When fully finished testing and after 100% integration test cases are passing, update the test document docs/testing/integration/cdm-integration-test-report.md, and also update the testing README file docs/testing/README.md.