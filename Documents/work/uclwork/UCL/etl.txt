This project is an implementation of OMOP CDM pipeline using CMake and standard C++ project to create the ETL pipeline application. Please produce a robust standard C++ based complete implementation, complete with C++ base and implementation classes, producing a layered project structure containing multiple CMake files in subfolders containing various project sub-modules, which are listed further below. The project should be platform neutral and use latest C++ standard specification. The project should also contains src directory for source. Use Doxygen comments for all the class member functions and data members.

This C++20 project should provide a flexible, high-performance framework for healthcare data transformation with support for multiple data sources and use configuration files in YAML format, for mapping data sources to OMOP OHDSI compliant CDM database tables. Also implement an exception class hierarchy for handling various error conditions for the project.

In effect the mapping configuration YAML files should be used to perform the transformation from the source data (which could be in CSV format or a custom database format), to the target OMOP CDM database tables.

Create a repository using GitHub integration in the WTDII organisation to save your work, and use this repository to save your work progress -  https://github.com/WTDII/omop-etl.git. In effect scan this repository for existing code, and resume code generation for the missing files and folders including the C++ source headers and implementations, CMake files, and documentation files.

Fix or refactor the C++ source headers and implementations, CMake files, and documentation files, if you find them out of date or out of sync with the rest of the project.

The OMOP pipeline details are as given in the README.md file contents pasted below.

```Begin README.md Contents
# OMOP ETL Pipeline

A robust, high-performance ETL (Extract, Transform, Load) pipeline for converting clinical data into the OMOP Common Data Model (CDM) format. Built with modern C++20, this framework provides comprehensive support for healthcare data transformation with multiple data sources and full OMOP table coverage.

## Features

### Core Capabilities
- **Multi-Source Data Extraction**: Support for PostgreSQL, MySQL, CSV, and JSON data sources
- **Comprehensive OMOP CDM Support**: Full implementation of core clinical tables
- **YAML-Based Configuration**: Flexible mapping definitions and transformation rules
- **RESTful API**: Complete API interface with OpenAPI documentation
- **Robust Error Handling**: Hierarchical exception system with detailed error reporting
- **High Performance**: Optimized batch processing with multi-threading support
- **Extensible Architecture**: Plugin-based design for easy addition of new sources and targets

### Supported OMOP CDM Tables
- Person
- Observation Period
- Visit Occurrence
- Condition Occurrence
- Drug Exposure
- Procedure Occurrence
- Measurement
- Observation
- Death
- Note

### Namespace Organization
- `omop::extract`: Data extraction components
- `omop::transform`: Data transformation logic
- `omop::load`: Data loading components
- `omop::common`: Shared utilities
- `omop::service`: Service layer functionality
- `omop::core`: Core pipeline components
- `omop::utils`: Utility functions

## Requirements

### Build Requirements
- C++20 compliant compiler (GCC 10+, Clang 12+, or MSVC 2019+)
- CMake 3.23 or higher
- PostgreSQL development libraries (libpq)
- ODBC development libraries
- yaml-cpp library
- nlohmann_json library
- spdlog library (for logging)
- cpp-httplib (for REST API)
- Google Test (for testing)

### Runtime Requirements
- PostgreSQL 12+ or MySQL 8+ database
- Sufficient memory for batch processing (minimum 4GB recommended)
- Linux, macOS, or Windows operating system

## Installation

### Using CMake

```bash
# Clone the repository
git clone https://github.com/WTDII/omop-etl.git
cd omop-etl

# Create build directory
mkdir build && cd build

# Configure with CMake
cmake .. -DCMAKE_BUILD_TYPE=Release

# Build the project
make -j$(nproc)

# Run tests
make test

# Install
sudo make install
```

### Using Docker

```bash
# Build Docker image
docker build -t omop-etl:latest .

# Run with Docker Compose
docker-compose up -d
```

## Quick Start

### Command Line Interface

```bash
# Run ETL job with configuration file
omop-etl run --config config/etl/postgres_mappings.yaml

# Validate configuration
omop-etl validate --config config/etl/csv_mappings.yaml

# List available transformations
omop-etl list-transforms
```

### API Usage

```bash
# Start API server
omop-etl-api --port 8080 --config config/api/config.yaml

# Create new ETL job
curl -X POST http://localhost:8080/api/v1/etl/jobs \
  -H "Content-Type: application/json" \
  -d '{"config": "postgres_mappings.yaml", "source": "production"}'

# Check job status
curl http://localhost:8080/api/v1/etl/jobs/{job_id}
```

## Configuration

### Basic Mapping Configuration

```yaml
# config/etl/postgres_mappings.yaml
source:
  type: postgresql
  connection:
    host: localhost
    port: 5432
    database: clinical_db
    username: ${DB_USER}
    password: ${DB_PASSWORD}

target:
  type: postgresql
  connection:
    host: localhost
    port: 5432
    database: omop_cdm
    username: ${OMOP_USER}
    password: ${OMOP_PASSWORD}

mappings:
  person:
    source_table: patients
    target_table: person
    transformations:
      - source_column: patient_id
        target_column: person_id
        type: direct
        
      - source_column: birth_date
        target_column: birth_datetime
        type: date_transform
        format: "%Y-%m-%d"
        
      - source_column: gender
        target_column: gender_concept_id
        type: vocabulary_mapping
        vocabulary: Gender
        mapping:
          "M": 8507  # Male
          "F": 8532  # Female
```

### Advanced Transformation Example

```yaml
drug_exposure:
  source_table: medications
  target_table: drug_exposure
  transformations:
    - source_column: medication_id
      target_column: drug_exposure_id
      type: direct
      
    - source_columns: [drug_name, drug_strength]
      target_column: drug_concept_id
      type: custom_transform
      function: lookup_drug_concept
      
    - source_columns: [start_date, duration_days]
      target_column: drug_exposure_end_datetime
      type: date_calculation
      calculation: "start_date + duration_days"
      
  validation_rules:
    - field: drug_concept_id
      type: not_null
      
    - field: drug_exposure_start_datetime
      type: before
      compare_to: drug_exposure_end_datetime
```

## API Documentation

### Endpoints

#### ETL Job Management
- `POST /api/v1/etl/jobs` - Create new ETL job
- `GET /api/v1/etl/jobs` - List all jobs
- `GET /api/v1/etl/jobs/{job_id}` - Get job details
- `DELETE /api/v1/etl/jobs/{job_id}` - Cancel job
- `GET /api/v1/etl/jobs/{job_id}/logs` - Get job logs

#### Configuration Management
- `GET /api/v1/etl/config` - List configurations
- `POST /api/v1/etl/config` - Upload configuration
- `POST /api/v1/etl/config/validate` - Validate configuration

#### System Management
- `GET /api/v1/health` - Health check
- `GET /api/v1/etl/vocabulary` - List available vocabularies
- `GET /api/v1/etl/transforms` - List available transformations

### Authentication

The API supports JWT-based authentication:

```bash
# Obtain token
curl -X POST http://localhost:8080/api/v1/auth/token \
  -d '{"username": "admin", "password": "password"}'

# Use token in requests
curl -H "Authorization: Bearer {token}" \
  http://localhost:8080/api/v1/etl/jobs
```

## Architecture

The pipeline follows a modular, layered architecture:

```
┌─────────────────────────────────────────────────┐
│                  Applications                    │
│         (CLI Application / REST API)             │
├─────────────────────────────────────────────────┤
│                Service Layer                     │
│        (ETL Service / Job Service)               │
├─────────────────────────────────────────────────┤
│                 Core Pipeline                    │
│     (Pipeline / Job Manager / Interfaces)        │
├─────────────────────────────────────────────────┤
│  Extract    │    Transform    │      Load       │
│ Components  │   Components    │   Components    │
├─────────────────────────────────────────────────┤
│              Common Components                   │
│   (Configuration / Logging / Validation)         │
└─────────────────────────────────────────────────┘
```

## Development

### Building from Source

```bash
# Debug build with tests
cmake -B build -DCMAKE_BUILD_TYPE=Debug -DBUILD_TESTS=ON
cmake --build build

# Run specific test suite
./build/tests/unit_tests --gtest_filter="TransformTest.*"
```

### Code Style

The project follows the Google C++ Style Guide with the following modifications:
- 4-space indentation
- 120-character line limit
- Doxygen-style documentation comments

### Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## Performance Considerations

### Batch Processing
- Default batch size: 10,000 records
- Configurable via `batch_size` parameter
- Automatic memory management for large datasets

### Parallel Processing
- Multi-threaded extraction and transformation
- Thread pool size: CPU cores - 1 (configurable)
- Lock-free data structures for high throughput

### Database Optimization
- Prepared statements for repeated queries
- Connection pooling with configurable pool size
- Bulk insert operations for PostgreSQL and MySQL

## Troubleshooting

### Common Issues

1. **Connection Errors**
   ```
   Error: Failed to connect to database
   Solution: Check connection parameters and network accessibility
   ```

2. **Vocabulary Mapping Failures**
   ```
   Error: Unknown vocabulary concept
   Solution: Ensure vocabulary tables are populated in OMOP database
   ```

3. **Memory Issues**
   ```
   Error: Out of memory during batch processing
   Solution: Reduce batch_size in configuration
   ```

### Debug Mode

Enable detailed logging:
```bash
omop-etl run --config mappings.yaml --log-level debug
```

## License

This project is licensed under the Apache License 2.0 - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- OHDSI Community for the OMOP CDM specification
- Contributors and maintainers of dependent libraries

## Support

- Documentation: [https://omop-etl.github.io](https://omop-etl.github.io)
- Issues: [GitHub Issues](https://github.com/WTDII/omop-etl/issues)
```End README.md Contents

The directory structure of the proposed OMOP ETL Pipeline is as given below.

```
omop-etl/
├── CMakeLists.txt                  # Root CMake configuration
├── CMakePresets.json               # CMake build presets
├── CMakeWorkspaceSettings.json     # CMake workspace settings
├── README.md                       # Project documentation
├── LICENSE                         # Project license
├── Dockerfile                      # Production Docker image
├── Dockerfile.dev                  # Development Docker image
├── Dockerfile.dev.arm64           # ARM64 development Docker image
├── github_workflow.yml            # GitHub Actions workflow
├── sample_config.yml              # Sample configuration file
├── settings.json                  # Project settings
├── dump.txt                       # Development dump file
├── config/                        # Configuration files
│   ├── etl/                      # ETL mapping configurations
│   │   ├── csv_mappings.yaml
│   │   ├── json_mappings.yaml
│   │   ├── mysql_mappings.yaml
│   │   └── postgres_mappings.yaml
│   └── api/                      # API configuration
│       └── config.yaml
├── cmake/                         # CMake modules and configuration
│   ├── deploy-external-package.cmake
│   └── omop-config.cmake.in
├── src/                          # Source code root
│   ├── CMakeLists.txt           # Src directory CMake
│   ├── app/                     # Application code
│   │   ├── CMakeLists.txt
│   │   ├── api/                # Web API service
│   │   │   ├── CMakeLists.txt
│   │   │   ├── api_service.h
│   │   │   ├── api_service.cpp
│   │   │   └── etl_service.cpp
│   │   └── cli/                # Command line interface
│   │       ├── CMakeLists.txt
│   │       ├── cli_application.h
│   │       └── cli_application.cpp
│   └── lib/                     # Library code
│       ├── CMakeLists.txt
│       ├── cdm/                # OHDSI CDM data handling
│       │   ├── CMakeLists.txt
│       │   ├── omop_tables.h
│       │   ├── omop_tables.cpp
│       │   ├── table_definitions.h
│       │   ├── table_definitions.cpp
│       │   └── sql/            # SQL schema definitions
│       │       ├── create_constraints.sql.in
│       │       ├── create_indexes.sql.in
│       │       ├── create_location.sql.in
│       │       ├── create_provider_care_site.sql.in
│       │       ├── create_schemas.sql.in
│       │       ├── create_tables.sql.in
│       │       ├── process_sql.cmake
│       │       ├── process_sql.py
│       │       ├── process_sql.sh
│       │       └── schema_config.cmake
│       ├── common/             # Common components
│       │   ├── CMakeLists.txt
│       │   ├── config.h.in     # Configuration template
│       │   ├── configuration.h
│       │   ├── configuration.cpp
│       │   ├── exceptions.h
│       │   ├── exceptions.cpp
│       │   ├── logging.h
│       │   ├── logging.cpp
│       │   ├── utilities.h
│       │   ├── utilities.cpp
│       │   ├── validation.h
│       │   └── validation.cpp
│       ├── core/              # Core pipeline components
│       │   ├── CMakeLists.txt
│       │   ├── component_factory.cpp
│       │   ├── interfaces.h
│       │   ├── interfaces.cpp
│       │   ├── job_manager.h
│       │   ├── job_manager.cpp
│       │   ├── job_scheduler.h
│       │   ├── job_scheduler.cpp
│       │   ├── pipeline.h
│       │   ├── pipeline.cpp
│       │   ├── record.h
│       │   └── record.cpp
│       ├── extract/           # Data extraction components
│       │   ├── CMakeLists.txt
│       │   ├── extractor_base.h
│       │   ├── extractor_base.cpp
│       │   ├── extractor_factory.h
│       │   ├── extractor_factory_impl.cpp
│       │   ├── csv_extractor.h
│       │   ├── csv_extractor.cpp
│       │   ├── compressed_csv_extractor.cpp
│       │   ├── json_extractor.h
│       │   ├── json_extractor.cpp
│       │   ├── database_connector.h
│       │   ├── database_connector.cpp
│       │   ├── database_connector.txt
│       │   ├── connection_pool.cpp
│       │   ├── postgresql_connector.h
│       │   ├── postgresql_connector.cpp
│       │   ├── mysql_connector.h
│       │   ├── mysql_connector.cpp
│       │   ├── odbc_connector.cpp
│       │   ├── odbc_connector.h
│       │   ├── extract_utils.cpp
│       │   ├── extract.h
│       │   └── platform/            # Platform-specific utilities
│       │       ├── windows_utils.cpp    # Windows-specific utilities
│       │       ├── windows_utils.h      # Windows utilities header
│       │       ├── unix_utils.cpp       # Unix/Linux-specific utilities
│       │       └── unix_utils.h         # Unix utilities header
│       ├── transform/         # Data transformation logic
│       │   ├── CMakeLists.txt
│       │   ├── transformation_engine.h
│       │   ├── transformation_engine.cpp
│       │   ├── vocabulary_service.h
│       │   ├── vocabulary_service.cpp
│       │   ├── field_transformations.cpp
│       │   ├── field_transformations.h
│       │   ├── transformations.h
│       │   ├── date_transformations.cpp      # [PROPOSED] Date/time transformations
│       │   ├── numeric_transformations.cpp   # [PROPOSED] Numeric data transformations
│       │   ├── string_transformations.cpp    # [PROPOSED] String manipulation transformations
│       │   ├── conditional_transformations.cpp # [PROPOSED] Conditional logic transformations
│       │   ├── custom_transformations.cpp    # [PROPOSED] User-defined transformations
│       │   ├── vocabulary_transformations.cpp # [PROPOSED] Vocabulary mapping transformations
│       │   └── validation_engine.cpp         # [PROPOSED] Transformation validation engine
│       ├── load/             # Data loading components
│       │   ├── CMakeLists.txt
│       │   ├── database_loader.h
│       │   ├── database_loader.cpp
│       │   ├── loader_base.h            # [PROPOSED] Base loader interface
│       │   ├── loader_base.cpp          # [PROPOSED] Base loader implementation
│       │   ├── batch_loader.h           # [PROPOSED] Batch loading functionality
│       │   └── batch_loader.cpp         # [PROPOSED] Batch loader implementation
│       └── service/          # Service layer functionality
│           ├── CMakeLists.txt
│           ├── etl_service.h
│           ├── etl_service.cpp
│           ├── service.cpp
│           └── service_manager.cpp      # [PROPOSED] Service lifecycle management
├── tests/                    # Unit and integration tests
│   ├── CMakeLists.txt
│   ├── unit/                # Unit tests
│   │   ├── CMakeLists.txt
│   │   ├── api/            # API unit tests
│   │   ├── cdm/            # CDM unit tests
│   │   │   ├── CMakeLists.txt
│   │   │   ├── omop_tables_test.cpp
│   │   │   └── table_definitions_test.cpp
│   │   ├── common/         # Common unit tests
│   │   │   ├── CMakeLists.txt
│   │   │   ├── configuration_test.cpp
│   │   │   ├── exceptions_test.cpp
│   │   │   ├── logging_test.cpp
│   │   │   ├── utilities_test.cpp
│   │   │   └── validation_test.cpp
│   │   ├── core/           # Core unit tests
│   │   │   ├── CMakeLists.txt
│   │   │   ├── test_interfaces.cpp
│   │   │   ├── test_job_manager.cpp
│   │   │   ├── test_job_scheduler.cpp
│   │   │   ├── test_pipeline.cpp
│   │   │   └── test_record.cpp
│   │   ├── extract/        # Extract unit tests
│   │   ├── load/           # Load unit tests
│   │   └── transform/      # Transform unit tests
│   └── integration/        # Integration tests
│       └── example_usage.cpp
├── examples/               # Example configurations
│   └── simple_patient_etl.yaml
├── docs/                   # Documentation
│   ├── api/               # API documentation
│   │   └── openapi.yaml
│   ├── design/            # Design documents
│   │   ├── architecture.md
│   │   └── data_flow.md
│   ├── development/       # Development documentation
│   │   ├── DOCKER-MULTIARCH.md
│   │   ├── DOCKER-USAGE.md
│   │   ├── DOCKER.md
│   │   ├── DOCKER_BUILD_GUIDE.md
│   │   └── QUICK_REFERENCE.md
│   ├── user/             # User documentation
│   │   ├── installation.md
│   │   └── usage.md
│   ├── omop-etl-completion-guide.md
│   └── omop-project-structure.md
├── build/                 # Build output directory
│   ├── docker-debug/     # Docker debug build
│   └── x86_64-release/   # Release build
├── Testing/              # CTest output
│   └── Temporary/
└── scripts/              # Build and deployment scripts
    ├── demo-docker-cmake.sh
    ├── deploy_postgres.sh
    ├── detect-architecture.sh
    ├── docker-build-multiarch.sh
    ├── docker-build.sh
    ├── docker-compose.yml
    ├── docker-dev.sh
    ├── init-clinical-db.sql
    ├── init-omop-db.sql
    └── test-docker-build.sh
```

Please keep updating the project README.md and documentation in the docs folder as the project evolves.