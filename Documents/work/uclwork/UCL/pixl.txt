# PIXL

## Multi-service Architecture

The decision to adopt a multi-service architecture for the PIXL project was made on November 1, 2023, by the original PIXL team. This architectural choice aligns with the microservices design pattern, which structures an application as a set of independently deployable, loosely coupled services.

Key reasons for choosing a multi-service architecture include:

1. Logical separation: It allows breaking the code into distinct packages and services, each focusing on specific functionality.

2. Improved concurrency: By running each service in its own process, it overcomes Python's Global Interpreter Lock (GIL) limitations, enabling true concurrent processing of tasks.

3. Enforced modularity: This approach encourages careful consideration of code placement and restricts services from directly using code from other services, promoting loose coupling.

4. Compatibility with deployment constraints: The architecture works well with UCLH's requirement to deploy services using Docker and Docker Compose, with the potential to extend to Kubernetes if needed.

This decision aligns with modern software development practices, offering benefits such as:

- Simplified services: Each service focuses on a small number of subdomains, making them easier to understand and maintain[1].
- Team autonomy: Different teams can develop, test, and deploy their services independently[1].
- Faster deployment pipeline: Smaller services are quicker to test and can be deployed independently[1].
- Technology stack flexibility: Different services can use different technologies and be upgraded independently[1].

While this approach offers numerous advantages, it's important to note that it also introduces additional complexity in areas such as data aggregation, operational management, and inter-service communication[3]. The team will need to address these challenges as the project progresses.

## Message-based Processing of Images

The document describes the decision to implement a message-based processing system for handling large-scale image processing in the PIXL project. Here's a summary of the key points:

1. Decision: The team chose to use a queue for buffering requests and a database for tracking export status, rather than using a database alone for scheduling.

2. Context:
   - The system needs to handle hundreds to tens of thousands of imaging studies per project.
   - Each study needs to be found individually in source systems.

3. Decision Drivers:
   - Ability to process multiple research projects simultaneously
   - Persistence when services are down
   - Support for secondary DICOM source if study isn't found in primary
   - Limit total number of images being processed for a given source system
   - Avoid reprocessing studies already exported for a project

4. Chosen Solution Benefits:
   - Fulfills all requirements
   - Allows investment in generic, transferrable technologies
   - Uses RabbitMQ, a technology the team has previously invested in

5. Challenges:
   - Requires managing extra services
   - Additional development effort
   - Initial implementation had issues that required fixing

This decision aligns with modern architectural practices for handling large-scale image processing, as seen in the search results. For example:

- The use of a message broker (queue) for robust, scalable processing of new images[1].
- Separation of processing into synchronous and asynchronous parts for optimizing response times[1].
- A scalable edge-cloud cooperation architecture for optimizing image processing efficacy and response rate[2].

The chosen architecture allows for flexibility, scalability, and efficient resource allocation, which are crucial for handling the expected volume of imaging studies across multiple research projects.

## DICOM server and processing

The decision to use Orthanc as the DICOM server for the PIXL project was made on November 1, 2023, by the original PIXL team. This choice aligns well with the project's requirements and the available information about Orthanc's capabilities.

Orthanc is described as a lightweight, robust DICOM store with rich scripting capabilities[1]. It offers several advantages that address the decision drivers:

1. Robustness: Orthanc is a well-established and "battle-tested" DICOM server[1].

2. Local caching: Orthanc can act as a local cache for DICOM studies, reducing the load on clinical imaging systems[1].

3. Python support: Orthanc offers Python-based plugins, aligning with the team's preferred programming language[2][8].

4. Customization: The ability to write Python plugins allows for project-specific anonymization profiles and custom field hashing[2][8].

5. Docker compatibility: Orthanc can be deployed using Docker, fitting UCLH's infrastructure constraints[4].

Additional benefits of choosing Orthanc include:

- DICOMWeb plugin for export capabilities[7].
- Cross-platform and standalone nature, making deployment quick and easy[1].
- Active development and community support[5].

While XNAT was considered as an alternative due to its history of use in the medical imaging subgroup, Orthanc was chosen for its lightweight nature and better alignment with the project's specific needs.

The decision to use Orthanc allows the PIXL team to leverage its Python expertise for custom extensions[8], while benefiting from a robust and flexible DICOM server solution. This choice supports the project's requirements for DICOM querying, storage, anonymization, and export within the given infrastructure constraints.

## Multiple-project configuration

The decision to use Azure Key Vault for storing secrets in the PIXL multi-project configuration was made on March 5, 2024, by Milan Malfait, Peter Tsrunchev, Jeremy Stein, and Stef Piatek. This decision addresses the need for secure storage of project-specific anonymization profiles, export destinations, and hashing salts.

Key points of the decision:

1. Azure Key Vault was chosen over file-based options and self-hosted secret storage services.

2. Main advantages:
   - Centralized secret management accessible by multiple deployments
   - Expirable secrets to limit potential compromise impact
   - Separate key vaults for per-project salts and export endpoints
   - Only destination type needs to be defined, with connection details stored in Key Vault

3. Drawbacks:
   - Requires other implementations to set up Azure storage accounts or develop new secret management
   - Developers need to update a `.env` file for system testing
   - Slight increase in cost, partially offset by credential caching

4. This decision supports the requirement for project-specific hashing to prevent inappropriate data linkage between research projects.

5. It aligns with the technical story "PIXL can take multiple projects" (Issue #330).

The decision to use Azure Key Vault provides a balance between security, ease of management, and scalability for the multi-project configuration in PIXL.

## Project-based study routing

The decision to use a custom REST API endpoint for project-based study routing in PIXL was made on November 27, 2024, by Stef Piatek and Paul Smith. This decision addresses the need to pass project-specific configuration for de-identification along with DICOM files to `orthanc-anon`.

Key points of the decision:

1. Custom REST API endpoint was chosen over adding a custom DICOM tag.

2. Main advantages of the chosen option:
   - Keeps original instances unaltered
   - Allows for faster de-identification through thread-pooling
   - Simplifies the overall workflow

3. The primary drawback is the inability to alter the queue of de-identification jobs without taking down `orthanc-anon`.

4. This decision was made because the previous approach of updating DICOM tags was causing `orthanc-raw` to crash, especially with large studies.

5. The new approach involves sending a REST request to `orthanc-anon` with the study UID and project once a study has been pulled from the DICOM source. `orthanc-anon` then adds this job to a threadpool and returns a 200 status to the client.

6. This decision aligns with the technical story "PIXL can take multiple projects" (Issue #330) and is related to the multiple project configuration decision (ADR-0004).

The custom REST API endpoint approach provides a more stable and efficient solution for project-based study routing in PIXL, addressing the issues encountered with the previous DICOM tag method.

## Export of parquet files and DICOM data

The decision to use an `export-api` service for exporting both DICOM images and structured data files was made on February 26, 2024, by Haroon Chughtai, Jeremy Stein, Milan Malfait, Ruaridh Gollifer, and Stef Piatek. This approach was chosen over a shared Python library for clearer separation of responsibilities.

Key advantages of the `export-api` service include:

1. Centralized secret management: The service will handle all secrets and orchestrate exports, reducing the risk of secret exposure.
2. Efficient caching: Long-running service allows for caching of export secrets, potentially improving performance.
3. Clear separation of responsibilities: A dedicated service for exports simplifies the overall architecture.

The main drawback is the need for additional code to interact with the service from the CLI for parquet export.

This decision aligns with the need for flexible export options, including:

1. Rolling export of images for projects with large data volumes.
2. Automated export of images and structured data via FTPS.
3. Export of images via DICOMWeb.

The `export-api` service approach is consistent with modern web-based workflows for medical imaging, as seen in the search results. For example, it can leverage standard RESTful APIs for DICOM data export, similar to the Azure DICOM service's export API[1]. This approach also aligns with the trend towards web-based solutions using FHIR and DICOMweb protocols for medical imaging workflows[2].

By centralizing the export functionality in a dedicated service, the PIXL project can more easily manage the complexities of exporting both DICOM images and structured data to various endpoints, while maintaining a clear separation of concerns in the overall architecture.

### Parquet

Parquet files, as mentioned in the "Export of parquet files and DICOM data" document, are a type of structured data file format designed for efficient storage and processing of large-scale data. Key characteristics of Parquet files include:

1. Columnar storage: Unlike row-based formats like CSV, Parquet organizes data by columns, allowing for faster querying and analysis of specific fields.

2. Compression efficiency: Parquet files use advanced compression techniques, resulting in smaller file sizes and reduced storage costs.

3. Performance optimization: The columnar format allows for efficient data skipping and predicate pushdown, improving query performance, especially for analytical workloads.

4. Schema support: Parquet files include built-in schema information, making them well-suited for direct import into databases or data warehouses.

5. Complex data type support: Parquet can handle nested data structures, making it versatile for various data types.

6. Big data ecosystem compatibility: Parquet is widely used with big data processing frameworks like Apache Spark, Hive, and Presto.

In the context of the PIXL project, Parquet files are likely used to store structured data extracted from or related to the DICOM images, allowing for efficient storage and analysis of medical imaging metadata or associated information.
