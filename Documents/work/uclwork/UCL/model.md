Model for the RNOH ER diagram.

```yaml
$type: org.nasdanika.architecture.core.Module
name: Conceptual Model
elements:
  - $type: org.nasdanika.architecture.core.Node
    name: User
    description: Represents a user in the system
    properties:
      - name: id
      - name: name
      - name: email
      - name: password

  - $type: org.nasdanika.architecture.core.Node
    name: Role
    description: Represents a role assigned to users
    properties:
      - name: id
      - name: name

  - $type: org.nasdanika.architecture.core.Node
    name: Permission
    description: Represents a permission associated with roles
    properties:
      - name: id
      - name: name

  - $type: org.nasdanika.architecture.core.Node
    name: Resource
    description: Represents a resource in the system
    properties:
      - name: id
      - name: name
      - name: type

  - $type: org.nasdanika.architecture.core.Node
    name: Action
    description: Represents an action that can be performed on resources
    properties:
      - name: id
      - name: name

  - $type: org.nasdanika.architecture.core.Node
    name: Organization
    description: Represents an organization in the system
    properties:
      - name: id
      - name: name

  - $type: org.nasdanika.architecture.core.Node
    name: Group
    description: Represents a group within an organization
    properties:
      - name: id
      - name: name

  - $type: org.nasdanika.architecture.core.Node
    name: Project
    description: Represents a project within an organization
    properties:
      - name: id
      - name: name

relationships:
  - $type: org.nasdanika.architecture.core.Relationship
    name: User_Role
    source: User
    target: Role
    description: Many-to-Many relationship between User and Role

  - $type: org.nasdanika.architecture.core.Relationship
    name: Role_Permission
    source: Role
    target: Permission
    description: Many-to-Many relationship between Role and Permission

  - $type: org.nasdanika.architecture.core.Relationship
    name: Permission_Resource
    source: Permission
    target: Resource
    description: Many-to-Many relationship between Permission and Resource

  - $type: org.nasdanika.architecture.core.Relationship
    name: Permission_Action
    source: Permission
    target: Action
    description: Many-to-Many relationship between Permission and Action

  - $type: org.nasdanika.architecture.core.Relationship
    name: User_Organization
    source: User
    target: Organization
    description: Many-to-Many relationship between User and Organization

  - $type: org.nasdanika.architecture.core.Relationship
    name: Organization_Group
    source: Organization
    target: Group
    description: One-to-Many relationship between Organization and Group

  - $type: org.nasdanika.architecture.core.Relationship
    name: Organization_Project
    source: Organization
    target: Project
    description: One-to-Many relationship between Organization and Project

  - $type: org.nasdanika.architecture.core.Relationship
    name: Group_User
    source: Group
    target: User
    description: Many-to-Many relationship between Group and User

  - $type: org.nasdanika.architecture.core.Relationship
    name: Project_User
    source: Project
    target: User
    description: Many-to-Many relationship between Project and User

  - $type: org.nasdanika.architecture.core.Relationship
    name: Resource_Organization
    source: Resource
    target: Organization
    description: Many-to-One relationship between Resource and Organization
```

