# Find files with last line with a new line or line break

This approach works for text files and ignores empty files since they don't have a "last line."

If you want to check for files without a trailing newline, replace -z with -n in the condition:

bash:

find . -type f -exec bash -c '[[ -n $(tail -c 1 "$1") ]] && echo "$1"' _ {} \;

# Port forwarding

1. Local Port Forwarding (From Your Local Computer to the Remote VM)

Add -N to prevent executing a remote command (just forward ports) and -f to run in the background.

ssh -f -N -L 8080:localhost:80 -L 8443:localhost:443

8080 and 8443 are arbitrary local ports (you can use any unused ports, e.g., 80 and 443 if they’re free locally).
localhost refers to the remote VM itself from its own perspective.

2. Remote Port Forwarding (Purely on the Remote VM)

Enable GatewayPorts (Optional).

By default, SSH binds forwarded ports to localhost on the destination. To make them accessible from other machines, edit the SSH server config on the destination:

Open /etc/ssh/sshd_config on the destination.
Set GatewayPorts yes or GatewayPorts clientspecified.
Restart SSH: sudo systemctl restart sshd.

Run the SSH Command on the Remote VM.

For ports 80 and 443:

ssh -R 8080:localhost:80 -R 8443:localhost:443 user@destination-ip

3. Make It Persistent

To run in the background:

ssh -f -N -R 8080:localhost:80 -R 8443:localhost:443 user@destination-ip

To keep it alive after logout, use nohup or a tool like autossh:

nohup ssh -f -N -R 8080:localhost:80 -R 8443:localhost:443 user@destination-ip &

# Execute all executable files in a directory using Bash
1. Simple for Loop

for file in /path/to/directory/*; do
  if [ -f "$file" ] && [ -x "$file" ]; then
    "$file"
  fi
done

2. Using find

find /path/to/directory -maxdepth 1 -type f -perm +111 -exec {} \;

3. Using run-parts (Debian/Ubuntu)

run-parts --verbose /path/to/directory

# Packet capture
tcpdump -vvv -lvi any "tcp port 80" -w http.log

# SSL
Viewing a site's certificate chain.
openssl s_client -connect xnat-web.medp-proj-cde.condenser.arc.ucl.ac.uk:443 -showcerts

Viewing a site's certificate information.
No text:
openssl s_client -connect xnat-web.medp-proj-cde.condenser.arc.ucl.ac.uk:443 -showcerts | openssl x509 -noout -dates -issuer -subject -fingerprint

With text:
openssl s_client -connect xnat-web.medp-proj-cde.condenser.arc.ucl.ac.uk:443 -showcerts | openssl x509 -noout -dates -issuer -subject -fingerprint -text

Verifying an existing certificate.
openssl x509 -in /etc/nginx/ssl/certs/selfservice.production.tools.tax.service.gov.uk.crt -noout -dates -issuer -subject -fingerprint -text
openssl x509 -in certificate.pem -noout -dates -issuer -subject -fingerprint -text
openssl x509 -noout -dates -issuer -subject -fingerprint -text < certificate.pem
keytool -printcert -file certificate.pem
ssh-keygen -l -f certificate.pem

Verifying a list of certificate files.
No text:
for site "${("jira" "confluence" "github" "user-management")}"; do openssl x509 -noout -dates -fingerprint < openssl s_client -connect $site.tools.tax.service.gov.uk:443 -showcerts; done
for certfile in $(ls *.crt); do openssl x509 -noout -dates -fingerprint < $certfile; done

Checking installed certificates and key on the local filesystem.
find /etc/ssl/certs -name *.crt | xargs md5sum | grep -E "(051A35F7D5A04841FDD681D691B6E38A7A020EA1)"
locate *.crt | xargs md5sum | grep -E "(051A35F7D5A04841FDD681D691B6E38A7A020EA1)"

# jar and war
Verify jar files within a war archive.
for j in $(find /var/lib/tomcat9/webapps/ROOT/WEB-INF/lib/ -name '*.jar'); do jar -tvf $j > /dev/null 2>&1; [ "$?" -ne 0 ] && echo "$j jar is broken"; done
for j in $(find /usr/share/tomcat9/lib/ -name '*.jar'); do jar -tvf $j > /dev/null 2>&1; [ "$?" -ne 0 ] && echo "$j jar is broken"; done
for j in $(find /usr/share/java/ -name '*.jar'); do jar -tvf $j > /dev/null 2>&1; [ "$?" -ne 0 ] && echo "$j jar is broken"; done

# Database
Connect to xnat-data Postgres instance.

psql -h 10.134.26.2 -p 5432 -U xnat -d xnat -t -A -c "USE xnat;"
psql -h 10.134.26.2 -p 5432 -U xnat -d xnat -t -A -c "SELECT * FROM mytable;" > names.txt

cat << EOF > query.sql
SELECT * FROM mytable WHERE id < 10;
EOF

psql -h 10.134.26.2 -p 5432 -U xnat -d xnat -f query.sql

