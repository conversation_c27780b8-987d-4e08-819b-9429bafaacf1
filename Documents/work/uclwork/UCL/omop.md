OMOP and its relation to OHDSI.

OMOP Common Data Model (CDM).
The OMOP Common Data Model allows for the systematic analysis of disparate observational databases.

https://www.ohdsi.org/data-standardization/#:~:text=The%20Observational%20Medical%20Outcomes%20Partnership,that%20can%20produce%20reliable%20evidence

# OMOP

The OMOP (Observational Medical Outcomes Partnership) Common Data Model (CDM) is a standardized framework for organizing and analyzing observational healthcare data. It plays a crucial role in the OHDSI (Observational Health Data Sciences and Informatics) initiative, which aims to improve health by empowering a community to collaboratively generate evidence that promotes better health decisions and care[1][3].

## Purpose and Significance

The OMOP CDM is designed to:

1. Standardize the structure and content of observational data[1].
2. Enable efficient analyses that can produce reliable evidence[1].
3. Allow for systematic analysis of disparate observational databases[2].
4. Transform data from various sources into a common format and representation[2].

## Key Features

**Data Standardization**: The OMOP CDM brings healthcare data from different organizations into a common format, allowing for collaborative research, large-scale analytics, and sharing of sophisticated tools and methodologies[1].

**Versatility**: It can accommodate both administrative claims and Electronic Health Record (EHR) data, supporting evidence generation from a wide variety of sources[1][2].

**Standardized Vocabularies**: A central component of the OMOP CDM is the OHDSI standardized vocabularies, which organize and standardize medical terms across various clinical domains[1].

## Benefits

1. **Efficiency**: Researchers don't need to understand database-specific schema details[6].
2. **Comparability**: Firmly controlled terminology makes datasets within the OMOP CDM comparable[6].
3. **Accessibility**: Concepts are freely available for researchers to access[6].
4. **Time-saving**: Up to 80% less programming time may be required compared to working with raw data[6].

## Structure and Organization

The OMOP CDM is patient-centric, meaning every clinical event has, at minimum, a patient identifier and a date[6]. Key components include:

- **Domains**: Patient and medical information are organized into domains (e.g., drug domain, condition domain) stored in specific tables and fields[6].
- **Standard Concepts**: Domain-specific tables are populated with standard concepts, which have unique domain assignments[6].
- **CONCEPT Table**: Patient record content is transformed into machine-readable "concepts" stored in this table[6].

## Application in OHDSI

OHDSI leverages the OMOP CDM to:

1. Develop open-source tools for data quality assessment, characterization, and analysis[1][3].
2. Facilitate medical product safety surveillance, comparative effectiveness studies, and patient-level predictive modeling[1][3].
3. Support collaborative research across data sources both within and outside the United States[2].

## Global Impact and Recognition

The OMOP CDM has gained significant traction globally:

- Used by over 3700 collaborators in 83 countries[7].
- Recognized as one of the top digital health tools for data interoperability by Digital Square at PATH[7].
- Nearly one billion unique anonymized patient records from 49 different countries exist in an OMOP CDM format[7].

## Implementation and Tools

Researchers can access OMOP data using tools like ATLAS, a web-based platform developed by the OHDSI community for designing and executing analyses on standardized, patient-level observational data[5].

In conclusion, the OMOP Common Data Model serves as a cornerstone for OHDSI's mission to improve health through collaborative, standardized data analysis. Its widespread adoption and recognition underscore its importance in advancing observational health research and promoting evidence-based healthcare decisions globally.

Citations:
[1] https://www.ohdsi.org/data-standardization/
[2] https://health.ucdavis.edu/data/omop.html
[3] https://www.ohdsi.org/data-standardization/
[4] https://learn.microsoft.com/en-us/azure/architecture/example-scenario/digital-health/patient-data-ohdsi-omop-cdm
[5] https://idr.ufhealth.org/research-services/omop/
[6] https://bmcmedresmethodol.biomedcentral.com/articles/10.1186/s12874-021-01434-3
[7] https://www.ohdsi.org/omop-cdm-digital-square/
[8] https://medicine.unimelb.edu.au/school-structure/general-practice-and-primary-care/research/research-groups/health-and-biomedical-research-information-technology-unit/omop-common-data-model

# How does the OMOP CDM improve data interoperability in healthcare

The OMOP Common Data Model (CDM) significantly enhances data interoperability in healthcare through several key mechanisms:

## Standardization of Data Structure

The OMOP CDM provides a standardized framework for organizing healthcare data, which is crucial for interoperability[1]. By transforming diverse Electronic Medical Record (EMR) data into a common format, it enables:

1. Seamless integration of data from various sources
2. Consistent representation of clinical information across different healthcare settings
3. Efficient analysis of disparate observational databases

## Common Vocabulary

One of the core features of the OMOP CDM is its use of standardized vocabularies:

- It employs a set of standardized medical terminologies, such as SNOMED-CT, which create a common language for describing medical concepts[6].
- This standardization ensures that clinical terms and concepts are uniformly represented across different datasets, facilitating accurate data comparison and analysis[1].

## Enhanced Collaboration

The OMOP CDM's standardized approach significantly improves collaboration in healthcare research:

- It enables researchers to "scale-up" and "de-risk" collaborative research by securely sharing de-identified and aggregated data[1].
- The model allows for the execution of analyses across multiple OMOP-converted databases while ensuring patient-level data remains securely within its local site[1].

## Global Adoption and Impact

The widespread adoption of the OMOP CDM has led to significant improvements in global healthcare data interoperability:

- By 2022, approximately 12% of EMRs worldwide were converted to the OMOP CDM format, encompassing data from 453 databases and over 928 million unique patient records across 41 countries[1].
- This extensive adoption facilitates large-scale, international collaborative research efforts and promotes a global "common data language" in healthcare[2][4].

## Efficient Data Analysis

The OMOP CDM's structure supports more efficient and reliable data analysis:

- It eliminates the need to combine different forms of data, reducing the risk of human error in data aggregation[4].
- Researchers can use shared end-to-end analysis packages without direct data exchange, enhancing data security and privacy[1].

By addressing these key aspects of data standardization and integration, the OMOP CDM significantly enhances interoperability in healthcare data management and analysis, fostering more robust and collaborative research efforts on a global scale.

Citations:
[1] https://informatics.bmj.com/content/31/1/e100953
[2] https://www.aridhia.com/blog/working-with-the-omop-cdm-in-the-aridhia-dre/
[3] https://www.lynx.care/blog/omop-cdm-data-warehouses-to-foster-research-to-improve-patient-outcomes
[4] https://www.inovalon.com/blog/the-top-5-benefits-of-using-the-omop-common-data-model-in-healthcare-analytics/
[5] https://kodjin.com/blog/omop-and-fhir-data-standardization/
[6] https://answerdigital.com/insights/the-omop-cdm-a-contender-for-standardising-healthcare-data/

## How does the OMOP CDM ensure data security and privacy
## What challenges might arise when implementing the OMOP CDM
## How does the OMOP CDM compare to other data standardization models
## What role does pseudonymisation play in the OMOP CDM
## How does the OMOP CDM facilitate cross-institutional research



# What are the main benefits of using the OMOP CDM for research

The OMOP Common Data Model (CDM) offers several key benefits for research:

1. Standardization and Interoperability: It provides a common format for healthcare data, enabling seamless integration and analysis of information from diverse sources[1][5]. This standardization allows for easy comparisons between different healthcare institutions and datasets[4].

2. Efficient Analysis: Researchers can use a single study query across multiple and diverse health providers, making research more efficient, transparent, reproducible, and scalable[4]. This can lead to up to 80% less programming time compared to working with raw data[1].

3. Enhanced Collaboration: The standardized approach facilitates cross-institutional collaborations and multi-site studies without the need for direct data exchange, ensuring patient-level data remains secure within local sites[3][6].

4. Improved Data Quality: The OMOP CDM emphasizes data quality, allowing for consistent checks and ensuring research data meets high standards[3].

5. Standardized Vocabularies: It employs a comprehensive set of standardized medical terminologies, creating a common language for describing medical concepts and enabling more accurate data comparison and analysis[5].

6. Simplified Governance: The uniform representation of data streamlines data governance processes and eases complexities associated with conducting single-site and multi-site studies[3].

7. Global Research Opportunities: Adoption of the OMOP CDM aligns research with European and global markets, facilitating the inclusion of data from multiple countries in comparative studies[7].

8. Versatility: The CDM can accommodate both administrative claims and Electronic Health Record (EHR) data, supporting evidence generation from a wide variety of sources[5].

9. Access to Analytical Tools: Once data is converted to the OMOP CDM, researchers can utilize a suite of standardized analytics tools, including open-source options for data quality assessment, characterization, and analysis[5][6].

10. Enhanced Security and Privacy: The standardized model ensures uniform application of security and privacy protocols, safeguarding secondary health data and maintaining patient privacy[3].

Citations:
[1] https://bmcmedresmethodol.biomedcentral.com/articles/10.1186/s12874-021-01434-3
[2] https://answerdigital.com/insights/the-omop-cdm-a-contender-for-standardising-healthcare-data/
[3] https://informatics.bmj.com/content/31/1/e100953
[4] https://www.bmd-software.com/news/omop-cdm-an-approach-for-standardizing-health-data/
[5] https://www.ohdsi.org/data-standardization/
[6] https://forums.ohdsi.org/t/reasons-to-ohdsis-omop-cdm/5273
[7] https://www.hdruk.ac.uk/news/nhs-research-sde-network-agrees-to-adopt-common-data-model/
[8] https://ohdsi.github.io/CommonDataModel/background.html
[9] https://pmc.ncbi.nlm.nih.gov/articles/PMC11025850/


## How does the OMOP CDM reduce programming time for researchers
## What are the key features of the OHDSI standardized vocabularies
## How does the OMOP CDM promote reproducibility in research
## What are the benefits of using a standardized data model for healthcare research
## How does the OMOP CDM enhance data governance processes

# How does OHDSI support the adoption of the OMOP CDM

OHDSI (Observational Health Data Sciences and Informatics) supports the adoption of the OMOP Common Data Model (CDM) through several key initiatives and resources:

1. Open-Source Tools: OHDSI develops and maintains a suite of open-source tools for data quality assessment, characterization, and analysis of OMOP CDM-formatted data[4].

2. Standardized Vocabularies: OHDSI provides standardized vocabularies that are central to the OMOP CDM, enabling organization and standardization of medical terms across various clinical domains[4].

3. Community Support: OHDSI fosters an active community of users and experts who assist with data conversions (ETLs) and provide guidance on CDM adoption and maintenance[4].

4. Educational Resources: The organization offers resources to help convert various datasets into the CDM format, along with tools to leverage data once it's in CDM format[4].

5. OHDSI Journey: Some OHDSI partners, like The Hyve, have developed structured processes (e.g., the three-stage OHDSI Journey) to support clients in adopting OHDSI tooling and the OMOP CDM[1].

6. Global Chapters: OHDSI has established regional chapters, such as the Australian OHDSI Chapter, to support OMOP adoption and develop collaborations between database stakeholders[5].

7. Quality Assurance Tools: OHDSI has developed tools like the OHDSI Quality Dashboard to ensure the quality of data converted into the OMOP CDM, improving transparency and enabling validation[5].

8. Collaborative Research: By standardizing data through the OMOP CDM, OHDSI facilitates collaborative research across different data sources and countries[4].

9. European Coordination: OHDSI Europe works to stimulate OMOP CDM adoption in Europe, addressing specific European challenges such as diverse vocabularies and data structures[6].

10. Regulatory Engagement: OHDSI interacts with regulators to evaluate the use of OMOP CDM in various contexts, such as pharmacovigilance[6].

These initiatives collectively support the widespread adoption of the OMOP CDM by providing tools, knowledge, community support, and a standardized framework for observational health data research.

Citations:
[1] https://www.thehyve.nl/articles/comprehensive-guide-to-adopting-ohdsi-omop
[2] https://learn.microsoft.com/en-us/azure/architecture/example-scenario/digital-health/patient-data-ohdsi-omop-cdm
[3] https://ohdsi.github.io/CommonDataModel/
[4] https://www.ohdsi.org/data-standardization/
[5] https://informatics.bmj.com/content/31/1/e100953
[6] https://www.ohdsi-europe.org/index.php/info

## What are the main benefits of using the OHDSI tools suite
## How does the OHDSI Journey help in establishing an actionable RWE feedback loop
## What specific training and workshops are provided in the first stage of the OHDSI Journey
## How does the OHDSI team ensure the reliability and accuracy of harmonized OMOP data
## What are the key components of the OHDSI tools suite deployed in the second stage


# What are some real-world applications of the OMOP CDM

The OMOP Common Data Model (CDM) has several real-world applications in healthcare and medical research:

1. Drug Safety Signal Detection: The standardized format allows for efficient analysis of large-scale data to identify potential safety issues with medications[3].

2. Real-World Treatment Outcome Analysis: Researchers can study the effectiveness of treatments across diverse patient populations and healthcare settings[3].

3. Population Health Forecasting: The OMOP CDM enables large-scale population health studies, facilitating predictions and trend analysis[3].

4. Comparative Effectiveness Research: By standardizing data from multiple sources, OMOP CDM supports studies comparing the effectiveness of different treatments or interventions[3].

5. Heart Failure Research: A study demonstrated the successful application of OMOP CDM to convert and analyze electronic health records (EHR) linked across multiple healthcare settings for heart failure research[2].

6. Process Mining in Healthcare: Researchers have used OMOP CDM data to extract, construct, and analyze event logs for healthcare process mining, providing insights into clinical pathways and patient journeys[4].

7. Clinical Trial Support: There's growing interest in adapting the OMOP CDM for interventional trial data, potentially enabling the use of real-world data assets to support clinical trial analyses[5].

8. Pharmacovigilance: The standardized format of OMOP CDM facilitates large-scale safety surveillance of pharmaceutical products[3].

9. Cross-Platform Analytics: OMOP CDM enables researchers to perform analyses across combined datasets from various sources, enhancing the scope and power of observational studies[3].

10. Predictive Modeling: The standardized format allows for the application of predictive models across diverse data types, potentially improving patient care and outcomes[3].

These applications demonstrate the versatility and value of the OMOP CDM in advancing healthcare research, improving patient outcomes, and supporting evidence-based decision-making in clinical practice.

Citations:
[1] https://www.inovalon.com/blog/the-top-5-benefits-of-using-the-omop-common-data-model-in-healthcare-analytics/
[2] https://pubmed.ncbi.nlm.nih.gov/34514354/
[3] https://nashbio.com/blog/healthcare-data/the-basics-of-omop-data-standardization-in-healthcare/
[4] https://pubmed.ncbi.nlm.nih.gov/36595527/
[5] https://www.thehyve.nl/articles/omop-cdm-clinical-trials

## How does the OMOP CDM improve drug safety surveillance
## What role does the OMOP CDM play in comparative effectiveness research
## How can the OMOP CDM be used for population health forecasting
## What are the challenges of mapping primary care prescriptions in the OMOP CDM
## How does the OMOP CDM facilitate real-world treatment outcome analysis


# How does the OMOP CDM handle different data sources and formats

The OMOP Common Data Model (CDM) effectively handles different data sources and formats through a standardized approach to data transformation and representation:

1. Data Transformation: During the extract, transform, load (ETL) process, diverse healthcare data is converted into a common format defined by the OMOP CDM. This process standardizes the structure (tables, fields) and content (terminology, vocabulary) of observational data[6].

2. Standardized Structure: The OMOP CDM organizes data into a relational database structure with fixed tables and columns. This structure can accommodate various types of healthcare data, including administrative claims and Electronic Health Records (EHR)[2][4].

3. Common Vocabularies: A central component of the OMOP CDM is the use of standardized vocabularies. These allow for the organization and standardization of medical terms across different clinical domains, ensuring consistent representation of concepts like diagnoses, procedures, and medications[2].

4. Source-to-Concept Mapping: For source codes not directly supported by the OMOP Vocabulary, the model provides a mechanism to map these to standard concepts. This is done through the Source_to_concept_map files, which are created during the ETL process[6].

5. Flexibility for Unique Concepts: If a source code is not supported by the OMOP Vocabulary, new records can be created in the CONCEPT table with specific ID ranges (>**********) to distinguish site-specific concepts from standard OMOP Vocabulary concepts[6].

6. Documentation of Source Data: The CDM_SOURCE table contains details about the source database and the transformation process, including information about data origin, purpose of collection, and the ETL process used[3].

7. Accommodation of Multiple Data Feeds: If a source database is derived from multiple data feeds, the integration of these disparate sources is documented in the ETL specifications[7].

By employing these mechanisms, the OMOP CDM enables the systematic analysis of disparate observational databases, allowing researchers to generate evidence from a wide variety of sources while maintaining data integrity and comparability.

## How does the OMOP CDM handle data from different countries
## What are the steps involved in converting data into the OMOP CDM format
## How does the OMOP CDM ensure data security and privacy
## What tools are available for data quality assessments in the OMOP CDM
## How does the OMOP CDM support collaborative research across different data sources

# DevOps / ETL

How to use tools like elasticsearch, logstash etc., to do the ETL work for OMOP CDM database creation? Are there other DevOps tools better suited then using the ELK toolset for OMOP CDM dtabases processing?

While the ELK (Elasticsearch, Logstash, Kibana) stack is a powerful set of tools for data processing and analysis, it is not typically used for creating OMOP Common Data Model (CDM) databases. The OMOP CDM requires a specific approach to ETL (Extract, Transform, Load) that is better suited to specialized tools designed for this purpose. However, we can discuss how to approach OMOP CDM ETL and some more appropriate tools for this task.

## OMOP CDM ETL Process

The ETL process for OMOP CDM typically involves these steps:

1. Data extraction from source systems
2. Data transformation to fit the OMOP CDM structure
3. Mapping source codes to standard concepts
4. Loading data into the OMOP CDM tables

## Recommended Tools for OMOP CDM ETL

Instead of using the ELK stack, the OHDSI (Observational Health Data Sciences and Informatics) community recommends several specialized tools for OMOP CDM ETL:

1. **WhiteRabbit**: This tool scans source data and provides detailed information on tables, fields, and values. It's useful in the initial stages of ETL design[7].

2. **Rabbit-In-a-Hat**: This tool uses the scan from WhiteRabbit to create a graphical interface for mapping source data to the CDM structure[7].

3. **OHDSI ETL Tools**: The OHDSI community has developed various open-source tools to assist with ETL processes, including data quality assessment and characterization tools.

4. **Custom ETL Frameworks**: Some organizations have developed their own ETL frameworks. For example, there's an open-source ETL tool on GitHub that uses YAML files to define mapping logic[5].

## ETL Implementation

The actual implementation of the ETL process often involves:

1. **SQL Scripts**: Many ETL processes for OMOP CDM are implemented using SQL scripts, which can be more directly controlled and optimized for database operations[3].

2. **ETL Tools**: Some organizations use data integration tools like Talend Open Studio for Data Integration to implement their ETL processes[2].

3. **Custom Code**: Depending on the complexity of the source data and the specific requirements, custom code (often in Python or another programming language) may be used to handle complex transformations[1].

## DevOps Considerations

While not specifically DevOps tools, the following practices can enhance the OMOP CDM ETL process:

1. **Version Control**: Use Git or another version control system to manage ETL scripts and configurations.

2. **Automation**: Implement automated testing and validation of the ETL process, possibly using tools like Jenkins or GitLab CI.

3. **Containerization**: Consider using Docker to create reproducible environments for ETL processes.

4. **Monitoring**: Implement monitoring of the ETL process, possibly using tools like Prometheus and Grafana.

In conclusion, while the ELK stack is powerful for log analysis and certain types of data processing, it's not typically used for OMOP CDM ETL. The specialized nature of OMOP CDM transformation often requires more tailored solutions. The OHDSI community provides several tools specifically designed for this purpose, which are generally more suitable than general-purpose data processing tools like ELK for creating and maintaining OMOP CDM databases.

Related:
What are the advantages of using Estuary Flow over Logstash for ETL pipelines
How does Talend Open Studio compare to Logstash for OMOP CDM database creation
Are there any specific challenges when using Rabbit tools for OMOP CDM mapping
What are the key features of the OHDSI tools for ETL processes
How does the performance of Elasticsearch impact the ETL process for OMOP CDM

What are the best practices for optimizing Elasticsearch indexing for OMOP CDM data
How can I determine the optimal bulk size for my specific Elasticsearch setup
What are the benefits of using the Bulk API for indexing OMOP CDM data
How does disabling refresh and replicas impact indexing performance
What Elasticsearch settings should I prioritize for better indexing speed
How can I use ILM to manage OMOP CDM indices effectively

How does Elasticsearch handle large datasets in the ETL process for OMOP CDM


What are the common performance issues when using Elasticsearch for OMOP CDM ETL
How can incremental loading be implemented effectively with Elasticsearch for OMOP CDM
What are the advantages of using Logstash over Talend for Elasticsearch indexing in OMOP CDM ETL


How well can the OMOP CDM be be used or be useful to design a an architecture system in TOGAF methodology, without using the semantic modelling capability of OPMI?

The OMOP Common Data Model (CDM) can be effectively used to design an architecture system within the TOGAF methodology, even without the semantic modeling capabilities of OPMI. Here's how OMOP CDM aligns with and supports TOGAF:

1. Standardized Data Structure: OMOP CDM provides a standardized structure for healthcare data, which aligns with TOGAF's emphasis on creating a consistent enterprise architecture[1].

2. Data Architecture Support: OMOP CDM can serve as a foundation for the Data Architecture domain in TOGAF, offering a pre-defined schema for healthcare data representation[1][7].

3. Interoperability: OMOP CDM enhances interoperability across different healthcare systems, supporting TOGAF's goal of creating an integrated enterprise architecture[4].

4. Vocabulary Standardization: OMOP CDM includes standardized vocabularies, which can be leveraged in TOGAF's Information Architecture to ensure consistent terminology across the enterprise[1].

5. Scalability: OMOP CDM supports federated research approaches, allowing for scalable architecture designs that align with TOGAF's principles of flexibility and adaptability[4].

6. Data Quality: Tools like the Data Quality Dashboard for OMOP CDM can be integrated into TOGAF's quality assurance processes[7].

7. Integration Capabilities: OMOP CDM can be used as an integration layer, supporting TOGAF's focus on creating cohesive systems across the enterprise[5][8].

8. Analytics Support: OMOP CDM facilitates advanced analytics, aligning with TOGAF's emphasis on creating architectures that support business intelligence and decision-making[2].

While OMOP CDM provides a robust framework for healthcare data standardization and integration within TOGAF, it's important to note that it is domain-specific to healthcare. For a comprehensive enterprise architecture, TOGAF practitioners would need to consider additional models and frameworks to cover other business domains.

Citations:
[1] https://www.ohdsi.org/data-standardization/
[2] https://www.d4l.io/blog/data-analytics-omop-common-data-model/
[3] https://pubs.opengroup.org/togaf-standard/master-data-management/
[4] https://biovox.eu/a-guide-to-the-omop-common-data-model/
[5] https://pubmed.ncbi.nlm.nih.gov/32570362/
[6] https://github.com/databricks-industry-solutions/omop-cdm
[7] https://ohdsi.github.io/CommonDataModel/
[8] https://pmc.ncbi.nlm.nih.gov/articles/PMC7269719/

The OMOP Common Data Model (CDM) can be effectively used to design an architecture system within the TOGAF methodology, even without the semantic modeling capabilities of OPMI. Here's how OMOP CDM aligns with and supports TOGAF:

1. Standardized Data Structure: OMOP CDM provides a standardized structure for healthcare data, which aligns with TOGAF's emphasis on creating a consistent enterprise architecture[1].

2. Data Architecture Support: OMOP CDM can serve as a foundation for the Data Architecture domain in TOGAF, offering a pre-defined schema for healthcare data representation[1][7].

3. Interoperability: OMOP CDM enhances interoperability across different healthcare systems, supporting TOGAF's goal of creating an integrated enterprise architecture[4].

4. Vocabulary Standardization: OMOP CDM includes standardized vocabularies, which can be leveraged in TOGAF's Information Architecture to ensure consistent terminology across the enterprise[1].

5. Scalability: OMOP CDM supports federated research approaches, allowing for scalable architecture designs that align with TOGAF's principles of flexibility and adaptability[4].

6. Data Quality: Tools like the Data Quality Dashboard for OMOP CDM can be integrated into TOGAF's quality assurance processes[7].

7. Integration Capabilities: OMOP CDM can be used as an integration layer, supporting TOGAF's focus on creating cohesive systems across the enterprise[5][8].

8. Analytics Support: OMOP CDM facilitates advanced analytics, aligning with TOGAF's emphasis on creating architectures that support business intelligence and decision-making[2].

While OMOP CDM provides a robust framework for healthcare data standardization and integration within TOGAF, it's important to note that it is domain-specific to healthcare. For a comprehensive enterprise architecture, TOGAF practitioners would need to consider additional models and frameworks to cover other business domains.

Citations:
[1] https://www.ohdsi.org/data-standardization/
[2] https://www.d4l.io/blog/data-analytics-omop-common-data-model/
[3] https://pubs.opengroup.org/togaf-standard/master-data-management/
[4] https://biovox.eu/a-guide-to-the-omop-common-data-model/
[5] https://pubmed.ncbi.nlm.nih.gov/32570362/
[6] https://github.com/databricks-industry-solutions/omop-cdm
[7] https://ohdsi.github.io/CommonDataModel/
[8] https://pmc.ncbi.nlm.nih.gov/articles/PMC7269719/How can the OMOP CDM be integrated into a TOGAF architecture framework

Related.
What are the advantages of using OMOP CDM in a TOGAF-based system
How does the OMOP CDM handle data standardization in a TOGAF architecture
Can the OMOP CDM be adapted for use in TOGAF's Architecture Development Method (ADM)
What are the potential limitations of using OMOP CDM without semantic modeling in TOGAF

