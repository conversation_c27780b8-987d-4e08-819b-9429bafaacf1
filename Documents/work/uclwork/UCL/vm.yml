apiVersion: kubevirt.io/v1
kind: VirtualMachine
metadata:
  name: xnat-db
  namespace: medp-proj-cde-ns
spec:
  runStrategy: RerunOnFailure
  template:
    spec:
      domain:
        cpu:
          cores: 2
        devices:
          disks:
            - bootOrder: 1
              disk:
                bus: virtio
              name: rootdisk
            - disk:
                bus: virtio
              name: cloudinitdisk
          interfaces:
            - bridge: {}
              model: virtio
              name: nic-1
        machine:
          type: q35
        memory:
          guest: 16284Mi
        resources:
          limits:
            cpu: '2'
            memory: 16Gi
          requests:
            cpu: 125m
            memory: 10922Mi
      hostname: xnat-db
      networks:
        - multus:
            networkName: medp-proj-cde-ns/medp-proj-cde-net
          name: nic-1
      volumes:
        - name: rootdisk
          persistentVolumeClaim:
            claimName: xnat-db-rootdisk
        - cloudInitNoCloud:
            userData: |
              #cloud-config
              bootcmd:
                - [ dnf, config-manager, --set-enabled, crb ]
                - [ dnf, install, -y, epel-release ]

              ssh_authorized_keys:
                - ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIDluk4iBNqbClG/kUEiAtbY9CKYwAJ3RFlUUYbKkTnDZ
          name: cloudinitdisk
