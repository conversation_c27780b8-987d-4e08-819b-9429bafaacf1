UCL


Employee.

Fixed Term Contractor.

<PERSON><PERSON>

746305

<PERSON><PERSON>
UCL ID: rmaphar
UPI (Employee ID / Number): NSHAR87
Employee number: 8036060
<EMAIL>
<EMAIL>
Research Software Engineer
Organisation: UCL Staff
Role: Research Software Engineer in F42 Dept of Med Phys & Biomedical Eng and welcome to UCL 



Student.

Supplementary Personal Statement.

I wish to pursue research studies in the field of AI based approach to computer vision, medical imaging, medical modelling science as part of my research area for the Medical Physics MPhil/PhD programme that I wish to pursue. I have a long background as computer based medical imaging applications development, and computer CGI libraries development. I have worked in the past with medical professionals to develop computer software and hardware based applications and devices for use in medical diagnostics and research. I have also worked in past as an computer imaging software engineer, where I have developed imaging libraries and SDKs for use in developing computer graphics, imaging, and playback software applications.

I wish to pursue this stream of research in Medical Physics MPhil/PhD programme, as I wish to formalise my extensive experience in the field of imaging and further develop my knowledge in the field of computer vision and imaging. I wish to contribute to the fields of computer vision and imaging research under the guidance of a programme tutor from UCL, as I believe UCL is foremost in research of computer vision and imaging.

The relevant skills which I can bring to the area of medical imaging research is based on my previous experience in medical imaging computer applications development, computer graphics applications and libraries development, data engineering and architecture experience, advanced computing - real time computing, highly scalable transaction processing, distributed and cloud computing, parallel execution computing, services architecture, development and operational support experience.

My computer science and engineering skills are summarised as below.

Application container design, build, creation, development, and deployment using Docker, Kubernetes, and other secure container technologies (5 years)
Server application, network, and web services development in these languages - RESTful, Python, Java, C++, Golang
Cloud technologies such as Amazon AWS Terraform and CloudFormation design, development, and support. (2+ year).
Configuration management technologies such as Puppet, Ansible (2+ years)
Platform As A Service technologies such as Jenkins, GitHub, Docker, Kubectl (2+ years)
Software As A Service side development using Python, Java, C++/QT, JavaScript, VB, and many others.

My medical software application development experience in brief.

Designed and developed and was involved in the full SDLC for a hospital management system called HISWise for a medium sized hospital.
Designed and developed and was involved in the full SDLC for the medical imaging system called IMAGEr used for medical image data analysis, storage and communication.

I would be grateful if I was accepted as a research student in the area of Vision and Imaging as part of the Medical Physics MPhil/PhD programme.

https://evision.ucl.ac.uk/urd/sits.urd/run/siw_lgn
http://iris.ucl.ac.uk/iris/browse/profile?upi=GROYL68
http://iris.ucl.ac.uk/iris/browse/profile?upi=APGIB90
http://iris.ucl.ac.uk/iris/browse/profile?upi=CEELW72
http://iris.ucl.ac.uk/iris/browse/profile?upi=DJHAW78
http://iris.ucl.ac.uk/iris/browse/profile?upi=DSTOY26


Dear Gary,

I am writing to you in relation to my application for the research degree programme in medical imaging research and development, which I wish to pursue under your supervision. I had applied for the same degree research programme more than a decade ago, but at that time I could not find enough funds to pursue the programme.

If you would kindly consider my application for doing an active research in medical imaging field under your supervision and able guidance, then I would be extremely grateful.

I have an extensive experience in medical imaging computer application research and development from years ago, but it is might be dated now and require refreshing. I am currently working as a self employed IT researcher, and work with my clients in the area of software and network architecture, development, and operations.

I have also attached my CV along with this email for your kind consideration.

My application details are as follows:
Programme: Research Degree: Computer Science [RRDCOMSING01]
Student number: 746305
Application reference: 03/01
Entry month/year: 08/2019
Mode of attendance: Part-time

Please let me know if you have any information or queries.

Thank you. Kind regards.

Sincerely,

Nikhil Sharma


Dear Gary,

I am writing to you in relation to my application for the research degree programme in medical imaging research and development, which I wish to pursue under your supervision. I had applied for the same degree research programme more than a decade ago, but at that time I could not find enough funds to pursue the programme.

If you would kindly consider my application for doing an active research in medical imaging field under your supervision and able guidance, then I would be extremely grateful.

I have an extensive experience in medical imaging computer application research and development from years ago, but it is might be dated now and require refreshing. I am currently working as a self employed IT researcher, and work with my clients in the area of software and network architecture, development, and operations.

I have also attached my CV along with this email for your kind consideration.

My application details are as follows:
Programme: Research Degree: Computer Science [RRDCOMSING01]
Student number: 746305
Application reference: 03/01
Entry month/year: 08/2019
Mode of attendance: Part-time

Please let me know if you have any information or queries.

Thank you. Kind regards.

Sincerely,

Nikhil Sharma


Hi Gary,

Thank you for your time earlier, it was good to see you and talk to you after a long time.

Following up on our conversation, please find below a synopsis of my skillset and experience relevant to this degree programme.

The relevant skills which I can bring to the area of medical imaging research is based on my previous experience in medical imaging computer applications development, computer graphics applications and libraries development, data engineering and architecture experience, advanced computing - real time computing, highly scalable transaction processing, distributed and cloud computing, parallel execution computing, services architecture, development and operational support experience.

My computer science and engineering skills are summarised as below.

Application container design, build, creation, development, and deployment using Docker, Kubernetes, and other secure container technologies (5 years)
Server application, network, and web services development in these languages - RESTful, Python, Java, C++, Golang
Cloud technologies such as Amazon AWS Terraform and CloudFormation design, development, and support. (2+ year).
Configuration management technologies such as Puppet, Ansible (2+ years)
Build server technologies such as Jenkins, GitHub, Docker, Kubectl (2+ years)
Client side development using Java, C++/QT, JavaScript, VB, and VBScript.

My medical software application development experience in brief.

Designed and developed and was involved in the full SDLC for a hospital management system called ​HISWise​ for a medium sized hospital.
Designed and developed and was involved in the full SDLC for the medical imaging system called ​IMAGEr​ used for medical image data analysis, storage and communication.

Thank you. Kind regards.

Nikhil


Hi Holly,

Hope you are doing well. 

I am an alumni UCL student previously enrolled onto a PhD programme in Medical Engineering, which I had to leave due to various reasons at that time. Is it possible to get re-enrolled with my old student ID or do I do a fresh registration?

I am currently working with Prof. Gary Royle on a few medical engineering projects, and he suggested that I get in touch with you.

My ex-student details are as follows:
Programme: Research Degree: Computer Science [RRDCOMSING01]
Student number: 746305
Application reference: 03/01
Entry month/year: 08/2019
Mode of attendance: Part-time

Many thanks. Kind regards,

Nikhil Sharma



Fee Payments

Payment Outcome
Order ID:	ONL01656104
Date:	22nd August 2019
Your transaction has been SUCCESSFUL.
Please keep/print a copy of this page for your records. Thank you for your payment, it will be credited to the student's Portico account within 2 working days.

UCL Portals

Portico - The UCL Student Information Service: https://evision.ucl.ac.uk/urd/sits.urd/run/siw_lgn
Fee payment: https://payonline.ucl.ac.uk//feepay.php
Student portal: https://myaccount.ucl.ac.uk/new-student, https://myaccount.ucl.ac.uk/find-my-userid

UPI (Printed on ID Card): NSHAR87
Student Number (previously known as PRN): 746305
UCL User ID is: rmaphar
You are registered in MyAccount so please use one of the options below if you would like to reset your password or contact the Service Desk - 020 7679 5000 (internal 25000)
Research Degree: Cancer Research using Artificial Intelligence and Machine Learning
Department: Medical Physics & Bioengineering [RRDMPHSING01]
Academic Year: 2019/20
UCL Email Address: <EMAIL>
Personal Email Address: <EMAIL>	
Mobile Phone Number: ***********
Current contact address:
113 Etchingham Park Road
Finchley Central
London
N3 2EE
England
Tel: +************

Personal Address
Flat 60, Block C, 2 Station Grove, Wembley, London HA0 4AF

Accommodation
UCL does not provide bed linen or towels in the bedrooms. If you wish, you can purchase a bedding pack prior to arrival. This will be delivered to your room and prices start from £22. The pack consists of a duvet, pillow, duvet cover, pillow case and sheet and can be ordered via our preferred online shop (https://www.unikitout.com/pages/ucl-student-packs).

You can also get a 10% discount (for UCL students only) by entering the code UCL10 at checkout.

UCL Remote Working
https://www.ucl.ac.uk/isd/services/communicate-collaborate/remote-working

Office 365
https://www.office.com/?auth=2

UCL Research Computing Documentation
https://www.rc.ucl.ac.uk/docs/

UCL Doctoral Skills Development Programme
https://doctoral-skills.ucl.ac.uk/list-training.pht
Medical Physics & Biomedical Engineering - https://doctoral-skills.ucl.ac.uk/your-dept-training.pht?dept_ID=44

Communications in Computer and Information Science from Springer Link
https://link.springer.com/bookseries/7899
Username: <EMAIL>
Password: qIY28PzSnxL5Mr9cm*

Integrated Data Platform for Cancer
Central Database:
As you know we would like to create a database (or possibly linked databases) that contain a comprehensive data set for specific cohorts of patients that have had radiotherapy. This includes images, radiotherapy treatment plans, pathology, patient records, etc. Some of these are in DICOM format - we use the XNAT platform to manage these. But some are in different formats. I've heard that it is possible for XNAT to store non-DICOM files, but we haven’t done that. It would be great if you could look into this aspect. I will also link you in to people with access to data that we would like to use.
If you are happy with this line of work I can see that this can form a part of your PhD - build a data platform, develop analytical tools to answer important clinical questions.

I would like to see if we can use the same approach used for the COVID service in other areas, such as cancer screening. Once the COVID system is running we can explore.
Also, I want to develop an integrated data platform for cancer. this will be a database in which specific patient data is held, which we can then apply analysis techniques to learn specific features about the data. this is maybe something that could form part of your project if its of interest.
let me know if it would be useful to catch up at any point.

I would like to know more about the UCL XNAT platform and possibly gain enough access rights to the platform to start my research work for the design and development of the linked databases for the specific cohorts of patients that had radiography.

As part of the above research work I would also look in to design and development of a data processing engine and visualisation framework to process the data sets stored in the linked patient databases. The data visualisation framework could be used to visualise the processed the patient radiography data being produced by the data processing engine.

Glossary
Cohort study: https://en.wikipedia.org/wiki/Cohort_study#:~:text=Cohort%20studies%20represent%20one%20of,protective%20factors

References
A Data Model for Heterogeneous Data Integration Architecture - https://link.springer.com/chapter/10.1007/978-3-319-06932-6_53 (2014_Book_BeyondDatabasesArchitecturesAn.pdf)
Data Integration for Future Medicine (DIFUTURE). An Architectural and Methodological Overview - https://www.ncbi.nlm.nih.gov/pmc/articles/PMC6178202/
Data Integration and Analysis System (DIAS) as a platform for data and model integration: Cases in the field of water resources management and disaster risk reduction - https://datascience.codata.org/articles/10.5334/dsj-2018-029/
