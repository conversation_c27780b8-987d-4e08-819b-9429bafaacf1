This project is an implementation of OMOP CDM pipeline using CMake and standard C++ project to create the ETL pipeline application. Please produce a robust standard C++ based complete implementation, complete with C++ base and implementation classes, producing a layered project structure containing multiple CMake files in subfolders containing various project sub-modules, which are listed further below. The project should be platform neutral and use latest C++ standard specification. The project should also contains src directory for source. Use Doxygen comments for all the class member functions and data members.

This C++20 project provides a flexible, high-performance framework for healthcare data transformation with support for multiple data sources and use configuration files in YAML format, for mapping data sources to OMOP OHDSI compliant CDM database tables.

In effect the mapping configuration YAML files is used to perform the transformation from the source data (which could be in CSV format or a custom database format), to the target OMOP CDM database tables.

The directory structure of the proposed OMOP ETL Pipeline is as given below.

```
omop-etl/
├── CMakeLists.txt                  # Root CMake configuration
├── CMakePresets.json               # CMake build presets
├── CMakeWorkspaceSettings.json     # CMake workspace settings
├── README.md                       # Project documentation
├── LICENSE                         # Project license
├── Dockerfile                      # Production Docker image
├── Dockerfile.dev                  # Development Docker image
├── Dockerfile.dev.arm64           # ARM64 development Docker image
├── .dockerignore                   # Docker ignore rules
├── .clang-tidy                     # Clang-tidy configuration
├── .gitignore                      # Git ignore rules
├── github_workflow.yml            # GitHub Actions workflow
├── sample_config.yml              # Sample configuration file
├── settings.json                  # Project settings
├── dump.txt                       # Development dump file
├── core_pipeline_test.txt         # Core pipeline test output
├── core_validation_result_test.txt # Validation test output
├── core_processing_context_test.txt # Processing context test output
├── additional_core_tests.txt      # Additional core tests output
├── config/                        # Configuration files
│   ├── etl/                      # ETL mapping configurations
│   │   ├── csv_mappings.yaml
│   │   ├── json_mappings.yaml
│   │   ├── microservices_config.yaml
│   │   ├── mysql_mappings.yaml
│   │   └── postgres_mappings.yaml
│   └── api/                      # API configuration
│       └── config.yaml
├── cmake/                         # CMake modules and configuration
│   ├── deploy-external-package.cmake
│   └── omop-config.cmake.in
├── src/                          # Source code root
│   ├── CMakeLists.txt           # Src directory CMake
│   ├── app/                     # Application code
│   │   ├── CMakeLists.txt
│   │   ├── api/                # Web API service
│   │   │   ├── CMakeLists.txt
│   │   │   ├── api_service.h
│   │   │   ├── api_service.cpp
│   │   │   ├── etl_service.cpp
│   │   │   └── microservice_main.cpp
│   │   └── cli/                # Command line interface
│   │       ├── CMakeLists.txt
│   │       ├── cli_application.h
│   │       └── cli_application.cpp
│   └── lib/                     # Library code
│       ├── CMakeLists.txt
│       ├── cdm/                # OHDSI CDM data handling
│       │   ├── CMakeLists.txt
│       │   ├── omop_tables.h
│       │   ├── omop_tables.cpp
│       │   ├── table_definitions.h
│       │   ├── table_definitions.cpp
│       │   └── sql/            # SQL schema definitions
│       │       ├── CMakeLists.txt
│       │       ├── create_constraints.sql.in
│       │       ├── create_indexes.sql.in
│       │       ├── create_location.sql.in
│       │       ├── create_provider_care_site.sql.in
│       │       ├── create_schemas.sql.in
│       │       ├── create_tables.sql.in
│       │       ├── process_sql.cmake
│       │       ├── process_sql.py
│       │       ├── process_sql.sh
│       │       └── schema_config.cmake
│       ├── common/             # Common components
│       │   ├── CMakeLists.txt
│       │   ├── config.h.in     # Configuration template
│       │   ├── configuration.h
│       │   ├── configuration.cpp
│       │   ├── exceptions.h
│       │   ├── exceptions.cpp
│       │   ├── logging.h
│       │   ├── logging.cpp
│       │   ├── utilities.h
│       │   ├── utilities.cpp
│       │   ├── validation.h
│       │   └── validation.cpp
│       ├── core/              # Core pipeline components
│       │   ├── CMakeLists.txt
│       │   ├── component_factory.cpp
│       │   ├── interfaces.h
│       │   ├── interfaces.cpp
│       │   ├── interfaces.h.orig
│       │   ├── job_manager.h
│       │   ├── job_manager.cpp
│       │   ├── job_scheduler.h
│       │   ├── job_scheduler.cpp
│       │   ├── pipeline.h
│       │   ├── pipeline.cpp
│       │   ├── record.h
│       │   └── record.cpp
│       ├── extract/           # Data extraction components
│       │   ├── CMakeLists.txt
│       │   ├── extractor_base.h
│       │   ├── extractor_base.cpp
│       │   ├── extractor_factory.h
│       │   ├── extractor_factory.cpp
│       │   ├── csv_extractor.h
│       │   ├── csv_extractor.cpp
│       │   ├── compressed_csv_extractor.cpp
│       │   ├── json_extractor.h
│       │   ├── json_extractor.cpp
│       │   ├── database_connector.h
│       │   ├── database_connector.cpp
│       │   ├── connection_pool.cpp
│       │   ├── postgresql_connector.h
│       │   ├── postgresql_connector.cpp
│       │   ├── mysql_connector.h
│       │   ├── mysql_connector.cpp
│       │   ├── odbc_connector.h
│       │   ├── odbc_connector.cpp
│       │   ├── extract_utils.cpp
│       │   ├── extract.h
│       │   └── platform/            # Platform-specific utilities
│       │       ├── CMakeLists.txt
│       │       ├── windows_utils.cpp    # Windows-specific utilities
│       │       ├── windows_utils.h      # Windows utilities header
│       │       ├── unix_utils.cpp       # Unix/Linux-specific utilities
│       │       └── unix_utils.h         # Unix utilities header
│       ├── transform/         # Data transformation logic
│       │   ├── CMakeLists.txt
│       │   ├── transformation_engine.h
│       │   ├── transformation_engine.cpp
│       │   ├── vocabulary_service.h
│       │   ├── vocabulary_service.cpp
│       │   ├── field_transformations.cpp
│       │   ├── field_transformations.h
│       │   ├── transformations.h
│       │   ├── date_transformations.cpp      # Date/time transformations
│       │   ├── date_transformations.h
│       │   ├── numeric_transformations.cpp   # Numeric data transformations
│       │   ├── numeric_transformations.h
│       │   ├── string_transformations.cpp    # String manipulation transformations
│       │   ├── string_transformations.h
│       │   ├── conditional_transformations.cpp # Conditional logic transformations
│       │   ├── conditional_transformations.h
│       │   ├── custom_transformations.cpp    # User-defined transformations
│       │   ├── custom_transformations.h
│       │   ├── vocabulary_transformations.cpp # Vocabulary mapping transformations
│       │   ├── vocabulary_transformations.h
│       │   ├── validation_engine.cpp         # Transformation validation engine
│       │   ├── validation_engine.h
│       │   ├── transformation_registry_improvements.h
│       │   └── transformation_result.h
│       ├── load/             # Data loading components
│       │   ├── CMakeLists.txt
│       │   ├── database_loader.h
│       │   ├── database_loader.cpp
│       │   ├── loader_base.h            # Base loader interface
│       │   ├── loader_base.cpp          # Base loader implementation
│       │   ├── batch_loader.h           # Batch loading functionality
│       │   ├── batch_loader.cpp         # Batch loader implementation
│       │   ├── additional_loaders.h     # Additional loader implementations
│       │   ├── additional_loaders.cpp   # Additional loader implementations
│       │   ├── load_cmakelists.txt      # Load module CMake configuration
│       │   └── load_module_readme.md    # Load module documentation
│       └── service/          # Service layer functionality
│           ├── CMakeLists.txt
│           ├── etl_service.h
│           ├── etl_service.cpp
│           └── service.cpp
├── tests/                    # Unit and integration tests
│   ├── CMakeLists.txt
│   ├── unit/                # Unit tests
│   │   ├── CMakeLists.txt
│   │   ├── api/            # API unit tests
│   │   │   └── CMakeLists.txt
│   │   ├── cdm/            # CDM unit tests
│   │   │   ├── CMakeLists.txt
│   │   │   ├── omop_tables_test.cpp
│   │   │   └── table_definitions_test.cpp
│   │   ├── common/         # Common unit tests
│   │   │   ├── CMakeLists.txt
│   │   │   ├── configuration_test.cpp
│   │   │   ├── exceptions_test.cpp
│   │   │   ├── logging_test.cpp
│   │   │   ├── utilities_test.cpp
│   │   │   └── validation_test.cpp
│   │   ├── core/           # Core unit tests
│   │   │   ├── CMakeLists.txt
│   │   │   ├── interfaces_test.cpp
│   │   │   ├── job_manager_test.cpp
│   │   │   ├── job_scheduler_test.cpp
│   │   │   ├── pipeline_test.cpp
│   │   │   └── record_test.cpp
│   │   ├── extract/        # Extract unit tests
│   │   │   ├── CMakeLists.txt
│   │   │   ├── compressed_csv_test.cpp
│   │   │   ├── connection_pool_test.cpp
│   │   │   ├── csv_extractor_test.cpp
│   │   │   ├── database_connector_test.cpp
│   │   │   ├── extract_utils_test.cpp
│   │   │   ├── extract_utils_extended_test.cpp
│   │   │   ├── extractor_base_test.cpp
│   │   │   ├── extractor_factory_test.cpp
│   │   │   ├── json_extractor_test.cpp
│   │   │   ├── mysql_connector_test.cpp
│   │   │   ├── odbc_connector_test.cpp
│   │   │   ├── platform_utils_test.cpp
│   │   │   ├── postgresql_connector_test.cpp
│   │   │   └── extract_performance_test.cpp
│   │   ├── load/           # Load unit tests
│   │   │   ├── CMakeLists.txt
│   │   │   ├── additional_loaders_test.cpp
│   │   │   ├── batch_loader_test.cpp
│   │   │   ├── database_loader_test.cpp
│   │   │   └── load_utils_test.cpp
│   │   └── transform/      # Transform unit tests
│   │       ├── CMakeLists.txt
│   │       ├── conditional_transformations_test.cpp
│   │       ├── custom_transformations_test.cpp
│   │       ├── date_transformations_test.cpp
│   │       ├── field_transformation_helpers_test.cpp
│   │       ├── field_transformation_test.cpp
│   │       ├── numeric_transformations_test.cpp
│   │       ├── string_transformations_test.cpp
│   │       ├── transform_integration_test.cpp
│   │       ├── transform_utils_test.cpp
│   │       ├── transformation_engine_test.cpp
│   │       ├── transformation_registry_test.cpp
│   │       ├── transformation_edge_cases_test.cpp
│   │       ├── transformation_memory_management_test.cpp
│   │       ├── transformation_pipeline_comprehensive_test.cpp
│   │       ├── validation_engine_test.cpp
│   │       ├── vocabulary_service_test.cpp
│   │       ├── vocabulary_transformations_test.cpp
│   │       └── test_helpers.h
│   └── integration/        # Integration tests
│       ├── CMakeLists.txt
│       ├── integration_test_structure.txt
│       ├── example_usage.cpp
│       ├── api/            # API integration tests
│       │   ├── CMakeLists.txt
│       │   ├── test_api_integration.cpp
│       │   ├── test_grpc_api_integration.cpp
│       │   └── test_rest_api_integration.cpp
│       ├── cdm/            # CDM integration tests
│       │   ├── CMakeLists.txt
│       │   ├── condition_occurrence/
│       │   │   └── tes_condition_occurrence-integration.cpp
│       │   ├── death/
│       │   │   └── test_death_integration.cpp
│       │   ├── drug_exposure/
│       │   │   └── test_drug_exposure-integration.cpp
│       │   ├── measurement/
│       │   │   └── test_measurement_integration.cpp
│       │   ├── note/
│       │   │   └── test_note_integration.cpp
│       │   ├── observation/
│       │   │   └── test_observation_integration.cpp
│       │   ├── observation_period/
│       │   │   └── test_observation_period_integration.cpp
│       │   ├── person/
│       │   │   └── test_person_integration.cpp
│       │   ├── procedure_occurrence/
│       │   │   └── test_procedure_occurrence-integration.cpp
│       │   ├── test_omop_tables_integration.cpp
│       │   ├── test_schema_creation_integration.cpp
│       │   ├── visit_detail/
│       │   │   └── test_visit_detail_integration.cpp
│       │   └── visit_occurrence/
│       │       └── test_visit_occurrence_integration.cpp
│       ├── common/         # Common integration tests
│       │   ├── CMakeLists.txt
│       │   ├── test_configuration_integration.cpp
│       │   ├── test_logging_integration.cpp
│       │   ├── test_utilities_integration.cpp
│       │   └── test_validation_integration.cpp
│       ├── config/         # Configuration integration tests
│       │   └── CMakeLists.txt
│       │   └── test_configuration_management.cpp
│       ├── core/           # Core integration tests
│       │   ├── CMakeLists.txt
│       │   ├── test_job_manager_integration.cpp
│       │   ├── test_job_scheduler_integration.cpp
│       │   ├── test_pipeline_integration.cpp
│       │   └── test_record_integration.cpp
│       ├── e2e/            # End-to-end integration tests
│       │   ├── CMakeLists.txt
│       │   ├── test_advanced_transformation_integration.cpp
│       │   ├── test_cross_module_integration.cpp
│       │   ├── test_data_quality_integration.cpp
│       │   ├── test_full_pipeline_integration.cpp
│       │   └── test_performance_integration.cpp
│       ├── extract/        # Extract integration tests
│       │   ├── CMakeLists.txt
│       │   ├── extractor_integration_test.cpp
│       │   ├── test_csv_extractor_integration.cpp
│       │   ├── test_database_extractor_integration.cpp
│       │   └── test_json_extractor_integration.cpp
│       ├── load/           # Load integration tests
│       │   ├── CMakeLists.txt
│       │   ├── test_batch_inserter_integration.cpp
│       │   ├── test_database_loader_integration.cpp
│       │   ├── test_loader_performance_integration.cpp
│       │   └── test_transaction_integration.cpp
│       ├── monitoring/     # Monitoring integration tests
│       │   ├── CMakeLists.txt
│       │   ├── test_alerting_integration.cpp
│       │   ├── test_logging_integration.cpp
│       │   ├── test_metrics_integration.cpp
│       │   └── test_performance_monitoring_integration.cpp
│       ├── performance/    # Performance integration tests
│       │   ├── CMakeLists.txt
│       │   ├── test_concurrent_operations.cpp
│       │   ├── test_load_performance.cpp
│       │   ├── test_memory_usage_integration.cpp
│       │   └── test_throughput_integration.cpp
│       ├── quality/        # Data quality integration tests
│       │   ├── CMakeLists.txt
│       │   ├── test_anomaly_detection.cpp
│       │   ├── test_data_lineage.cpp
│       │   ├── test_data_quality_integration.cpp
│       │   └── test_validation_integration.cpp
│       ├── security/       # Security integration tests
│       │   ├── CMakeLists.txt
│       │   └── test_authentication_integration.cpp
│       ├── service/        # Service integration tests
│       │   ├── CMakeLists.txt
│       │   ├── test_advanced_loaders_integration.txt
│       │   ├── test_advanced_transformations_integration.txt
│       │   ├── test_etl_service_integration.cpp
│       │   ├── test_service_lifecycle_integration.cpp
│       │   ├── test_service_manager_integration.cpp
│       │   ├── test_service_performance_integration.cpp
│       │   └── test_service_reliability_integration.cpp
│       ├── test_data/      # Test data files
│       │   ├── csv/
│       │   │   ├── conditions.csv
│       │   │   ├── medications.csv
│       │   │   ├── patients.csv
│       │   │   ├── procedures.csv
│       │   │   ├── test_data.csv
│       │   │   ├── test_data_compressed.csv.gz
│       │   │   ├── test_data_large.csv
│       │   │   ├── test_data_malformed.csv
│       │   │   ├── test_data_unicode.csv
│       │   │   ├── test_data_utf8.csv
│       │   │   └── vocabulary.csv
│       │   ├── json/
│       │   │   ├── clinical_data.json
│       │   │   ├── patient_records.json
│       │   │   └── vocabulary_mappings.json
│       │   ├── sql/
│       │   │   ├── test_data.sql
│       │   │   └── test_schema.sql
│       │   └── yaml/
│       │       ├── advanced_mapping_config.yaml
│       │       ├── mapping_config.yaml
│       │       └── test_config.yaml
│       ├── test_helpers/   # Test helper utilities
│       │   ├── CMakeLists.txt
│       │   ├── database_fixture.cpp
│       │   ├── database_fixture.h
│       │   ├── test_utils.cpp
│       │   ├── test_utils.h
│       │   └── test_environment.cpp
│       ├── transform/      # Transform integration tests
│       │   ├── CMakeLists.txt
│       │   ├── test_complex_transformations_integration.cpp
│       │   ├── test_custom_transformations_integration.cpp
│       │   ├── test_transformation_engine_integration.cpp
│       │   └── test_vocabulary_integration.cpp
│       ├── workflow/       # Workflow integration tests
│       │   ├── CMakeLists.txt
│       │   ├── test_batch_workflow_integration.cpp
│       │   ├── test_complex_etl_workflow.cpp
│       │   ├── test_error_handling_integration.cpp
│       │   ├── test_workflow_management_integration.cpp
│       │   └── test_workflow_performance_integration.cpp
├── examples/               # Example configurations
│   └── simple_patient_etl.yaml
├── docs/                   # Documentation
│   ├── api/               # API documentation
│   │   └── openapi.yaml
│   ├── design/            # Design documents
│   │   ├── architecture.md
│   │   └── data_flow.md
│   ├── development/       # Development documentation
│   │   ├── BUILD_TARGETS.md
│   │   ├── DOCKER_BUILD_GUIDE.md
│   │   ├── DOCKER-MULTIARCH.md
│   │   ├── DOCKER-USAGE.md
│   │   ├── DOCKER.md
│   │   └── QUICK_REFERENCE.md
│   ├── user/             # User documentation
│   │   ├── installation.md
│   │   └── usage.md
│   ├── implementation_guide.md
│   └── omop-project-structure.md
├── test_configs/         # Test configuration files
│   └── test_pipeline.yaml
├── test_csv_output/      # Test output directory
│   └── output.csv
├── Testing/              # CTest output (temporary)
│   └── Temporary/
│       ├── CTestCostData.txt
│       └── LastTest.log
└── scripts/              # Build and deployment scripts
```

Attached below are the source for the OMOP ETL security library.

Check the C++ source code header and implementation files in the project contained in the folder src/lib/extract, and create patch files to fix any coding issues, bugs, assumptions, completeness, build failures, and errors. Fix the source code header and implementation files in the source code header and implementation files in the project folder src/lib/extract for the issues discovered in exisitng code including removing un-implemented code and removing TODO code snippets, placeholder code, basic / demo implementation, stub methods, refactor existing code in the folder src/lib/extract as required. Do not just remove the unimplemented code like TODOs, instead implement any code functionality found missing, not implemented, coding assumptions, or incomplete implementations. If it comes to database connection type, both MySQL and Postgres need to be supported.

This is a C++20 project, but be pragmatic in your approach towards enforcing any specific C++ standard strategy. Focus on elegance and also the current best industrial practices for the C++20 projects and for C++ projects in general.

Create patch files to fix to the library source code, unit, and integration test cases in the project's root directory to the actual project source and test files, i.e. the src/lib/extract, tests/unit/extract, and tests/integration/extract directories, do not use arbitrary directories like build_test build-test etc. Do not remove any existing code or functionality, or try to skip the test cases, or remove the functionality or test cases. Instead focus should be on implementing the missing assumptions and functionality, and then rebuilding and re-testing and create patch files to fix the test cases.

Do not comment out or remove functionality from src/lib/extract directory source code. Do not comment out or remove test cases and test case assetions from the tests/unit/extract and tests/integration/extract directories for the test cases code in order to make them pass or remove functionality. Instead add, implement, fix, refactor source code or test code to fix any code related issues, broken, missing, failed, or stubbed test cases.

Please also create patch files to fix and enable test cases which are failed, disabled, skipped, stubbed, or errors.

Don't use PIMPL classes, just implement the class member functions themselves with all the private data members and implementation with the main class, instead of creating a contained derivative PIMPL class.

Instead of using (void) to suppress the unused variables warning, use maybe_unused directive.

Make sure to save the processed files and make them available to download before the Max usage limit is reached.

Finally make sure that all the classes for the library extract should use the namespace omop::extract.

Current list of compilation and other fundamental issues of the transform library are as given below.

```
Extract Library Compilation Issues

  1. database_connector.cpp Issues

  Interface Method Mismatches

  - Line 118: current_result_set_->get_column_names() - Method doesn't exist in IResultSet interface
    - Available: column_name() method
    - Fix: Use correct method name or add missing method to interface
  - Line 120: record.fields[column_name] - Direct access to private member
    - Available: record.getFieldsMutable() method
    - Fix: Use accessor method instead of direct field access
  - Line 123: batch.records.push_back() - Direct access to private member
    - Available: batch.getRecordsMutable() method
    - Fix: Use accessor method instead of direct field access

  Missing/Incorrect Member Variables

  - Lines 131, 134, 159, 166: total_records_extracted_ - Variable doesn't exist
    - Available: total_extracted_ member variable
    - Fix: Rename all references to use correct variable name
  - Line 246: limit_ - Variable not declared in scope
    - Fix: Add member variable declaration or use correct variable name

  Method Definition Issues

  - Line 201: has_more_data() - Duplicate definition
    - Problem: Method already defined inline in header file (line 305)
    - Fix: Remove implementation from cpp file or remove inline definition from header
  - Line 205: get_total_records_extracted() - No matching declaration
    - Fix: Add method declaration to header file
  - Line 209: get_processing_time() - No matching declaration
    - Fix: Add method declaration to header file

  Connection Interface Issues

  - Lines 275, 329: connection->connect() - Wrong parameter count
    - Expected: connect(const ConnectionParams& params)
    - Called: connect() with no parameters
    - Fix: Pass required ConnectionParams parameter
  - Line 416: stats.total_wait_time_ms - Member doesn't exist in PoolStats struct
    - Fix: Add member to struct or use correct member name

  Constructor Warning

  - Lines 424-431: Member initialization order warning
    - Problem: total_wait_time_ initialized before active_connections_
    - Fix: Reorder member variables in header to match initialization order

  2. extract_utils.cpp Issues

  Missing Configuration Members

  - Lines 351, 354: config.num_workers - Member doesn't exist in Config struct
    - Fix: Add num_workers member to ParallelExtractor::Config struct

  Missing Method Declarations

  - Line 374: add_task(std::function<void()>) - No declaration in header
    - Fix: Add method declaration to ParallelExtractor class in header
  - Line 382: wait_for_completion() - No declaration in header
    - Fix: Add method declaration to ParallelExtractor class in header

  Method Call Issues

  - Lines 405, 434: add_task() calls fail - Method not declared in scope
    - Fix: Add method declaration and ensure it's accessible
  - Lines 420, 452: wait_for_completion() calls fail - Method not declared in scope
    - Fix: Add method declaration and ensure it's accessible

  3. Root Cause Analysis

  Primary Issues

  1. API Inconsistency: Header files and implementation files are out of sync
  2. Incomplete Refactoring: Looks like the code went through a refactoring where:
    - Direct member access was replaced with accessor methods
    - Method signatures were changed
    - Member variables were renamed
    - But the implementation files weren't fully updated
  3. Missing Interface Methods: Some methods expected by implementation don't exist in interfaces

  Recommended Fix Strategy

  1. Audit all header-implementation pairs in extract library
  2. Update implementation files to match current interface definitions
  3. Add missing method declarations to header files
  4. Standardize member access patterns (direct vs accessor methods)
  5. Fix member variable naming inconsistencies
  6. Add missing configuration struct members

⏺ Update Todos
  ⎿  ☒ Analyze and document all extract library compilation issues

⏺ This comprehensive list shows that the extract library needs significant interface synchronization work between header and implementation files. The issues
  appear to stem from incomplete refactoring where the public interfaces were updated but the implementation files weren't fully synchronized with the changes.

⏺ Deep Analysis: Additional Issues in Extract Library

  1. Architecture & Design Issues

  Interface-Implementation Mismatch

  - DatabaseExtractor class: Uses total_records_extracted_ in implementation but header declares total_extracted_
  - ConnectionPool::PoolStats: Implementation expects total_wait_time_ms member that doesn't exist in struct
  - IResultSet interface: Implementation calls get_column_names() but interface likely has different method name
  - Record/RecordBatch access: Implementation uses direct field access (record.fields, batch.records) but interface uses accessor methods

  Missing Configuration Members

  - ParallelExtractor::Config: Implementation uses num_workers but header only shows num_threads
  - DatabaseExtractor: Implementation uses limit_ member variable not declared in header

  Method Declaration Inconsistencies

  - Methods implemented but not declared in headers:
    - DatabaseExtractor::get_total_records_extracted()
    - DatabaseExtractor::get_processing_time()
    - ParallelExtractor::add_task()
    - ParallelExtractor::wait_for_completion()

  2. Memory & Resource Management Issues

  Connection Pool Problems

  - Connection Creation: connection->connect() called without required ConnectionParams parameter
  - RAII Issues: PooledConnection destructor may not properly handle failed connections
  - Thread Safety: Potential race conditions in connection acquisition/release cycle
  - Resource Leaks: Failed connections not properly cleaned up in pool initialization

  Memory Management

  - Shared Pointer Usage: BatchExtractor uses both unique_ptr and shared_ptr constructors, potential confusion
  - Result Set Lifecycle: current_result_set_ reset patterns may cause premature cleanup
  - Exception Safety: Resource cleanup not guaranteed during exception scenarios

  3. Data Integrity & Validation Issues

  SQL Injection Vulnerabilities

  - Filter Validation: Basic keyword checking insufficient - regex validation exists for ORDER BY but filter validation is primitive
  - Column Name Injection: Direct column name insertion in query building without proper escaping
  - Schema/Table Names: No validation of schema/table names for injection patterns

  Data Type Handling

  - CSV Type Inference: Overly simplistic type detection in CsvFieldParser::convert_field()
  - Null Handling: Inconsistent null value handling across different field types
  - UTF-8 Validation: Multiple UTF-8 validation implementations, potential inconsistencies
  - Date/Time Parsing: No error handling for malformed date/time strings

  4. Performance & Scalability Issues

  Inefficient Operations

  - String Operations: Multiple unnecessary string transformations (repeated tolower(), toupper() calls)
  - Regular Expression Compilation: ORDER BY regex compiled on every validation
  - File System Calls: Repeated std::filesystem::exists() calls in type detection
  - Memory Allocation: Excessive string copying in CSV parsing

  Threading Issues

  - Connection Pool Scalability: No connection timeout handling, threads may wait indefinitely
  - Parallel Extractor Design: Missing proper thread pool implementation
  - Atomic Operations: Statistics counters use atomics but may have race conditions in complex operations

  5. Error Handling & Logging Issues

  Exception Propagation

  - Incomplete Error Context: Database exceptions lose original error codes/context
  - Mixed Exception Types: Some functions throw multiple exception types without clear documentation
  - Resource Cleanup: Exception-unsafe resource management in several places

  Logging Inconsistencies

  - Log Level Misuse: Info-level logging for debug information
  - Missing Context: Log messages lack sufficient context (connection IDs, batch numbers)
  - Performance Impact: Logging inside tight loops without level checks

  6. Configuration & Extensibility Issues

  Hard-coded Values

  - Magic Numbers: Hard-coded batch sizes, timeout values, and retry counts
  - File Extensions: Hard-coded file extension mappings in type detection
  - SQL Dialects: No abstraction for database-specific SQL syntax

  Configuration Validation

  - Type Safety: std::any configuration values lack type validation
  - Range Checking: No bounds checking for numeric configuration values
  - Default Values: Inconsistent default value handling across components

  7. Testing & Maintainability Issues

  Code Duplication

  - UTF-8 Validation: Multiple implementations of same functionality
  - Connection Retry Logic: Similar patterns repeated across connection types
  - Error Message Formatting: Inconsistent error message construction

  Testability Problems

  - Hard Dependencies: Difficult to mock database connections and file systems
  - Static Coupling: Factory patterns make unit testing challenging
  - State Management: Complex internal state makes testing extraction scenarios difficult

  8. Security Issues

  Input Validation

  - Path Traversal: No validation of file paths in CSV extractor
  - Buffer Overflows: Potential issues with large CSV field parsing
  - Resource Exhaustion: No limits on memory usage during large extractions

  Data Exposure

  - Connection Strings: Potential logging of sensitive connection information
  - Error Messages: Database errors may expose schema information
  - Temporary Files: No secure cleanup of temporary extraction files

  Summary

  The extract library has fundamental architectural issues stemming from incomplete refactoring, inconsistent interface implementations, and lack of proper
  separation of concerns. The most critical issues are the interface mismatches that prevent compilation, but deeper problems include security vulnerabilities,
  resource management issues, and poor error handling patterns. A comprehensive refactoring would be needed to address these systemic issues.
```

The C++ source code header and implementation files for the extract library and contained in the project directory src/lib/extract.

```
File src/lib/extract/csv_extractor.cpp:

/**
 * @file csv_extractor.cpp
 * @brief Implementation of CSV data extractor
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "csv_extractor.h"
#include "common/logging.h"
#include "common/exceptions.h"
#include "extractor_factory.h"
#include <algorithm>
#include <cctype>
#include <iomanip>
#include <sstream>
#include <any>
#include <string>
#include "common/validation.h"

namespace omop::extract {

// UTF-8 validation helper
[[maybe_unused]] static bool is_valid_utf8(const std::string& str) {
    const unsigned char* bytes = reinterpret_cast<const unsigned char*>(str.c_str());
    const unsigned char* end = bytes + str.length();

    while (bytes < end) {
        if (*bytes <= 0x7F) {
            // ASCII
            bytes++;
        } else if ((*bytes & 0xE0) == 0xC0) {
            // 2-byte sequence
            if (bytes + 1 >= end || (bytes[1] & 0xC0) != 0x80) return false;
            bytes += 2;
        } else if ((*bytes & 0xF0) == 0xE0) {
            // 3-byte sequence
            if (bytes + 2 >= end || (bytes[1] & 0xC0) != 0x80 ||
                (bytes[2] & 0xC0) != 0x80) return false;
            bytes += 3;
        } else if ((*bytes & 0xF8) == 0xF0) {
            // 4-byte sequence
            if (bytes + 3 >= end || (bytes[1] & 0xC0) != 0x80 ||
                (bytes[2] & 0xC0) != 0x80 || (bytes[3] & 0xC0) != 0x80) return false;
            bytes += 4;
        } else {
            return false;
        }
    }
    return true;
}

// CsvExtractor implementation

CsvExtractor::CsvExtractor() 
    : ExtractorBase("csv_extractor", nullptr, nullptr), 
      parser_(options_) {
}

// CsvFieldParser implementation

std::any CsvFieldParser::convert_field(const std::string& field, const std::string& type_hint) {
    // Handle null values - check for empty, null string, and case-insensitive "null"
    if (field.empty() || field == options_.null_string) {
        return std::any{};
    }
    
    // Check for case-insensitive "null"
    std::string lower_field = field;
    std::transform(lower_field.begin(), lower_field.end(), lower_field.begin(), ::tolower);
    if (lower_field == "null") {
        return std::any{};
    }

    // If no type hint, try to infer type
    if (type_hint.empty()) {
        // Try integer first (prioritize numbers over booleans)
        try {
            size_t pos;
            long long int_val = std::stoll(field, &pos);
            if (pos == field.length()) {
                return int_val;
            }
        } catch (const std::exception&) {
            // Fall through to try double
        }

        // Try double
        try {
            size_t pos;
            double double_val = std::stod(field, &pos);
            if (pos == field.length()) {
                return double_val;
            }
        } catch (const std::exception&) {
            // Fall through to try boolean
        }

        // Try boolean (only if it's not a number)
        std::string upper_field = field;
        std::transform(upper_field.begin(), upper_field.end(), upper_field.begin(), ::toupper);

        if (upper_field == options_.true_string || upper_field == "TRUE" ||
            upper_field == "YES" || upper_field == "Y" || upper_field == "T" ||
            upper_field == "ON" || upper_field == "1") {
            return true;
        }
        if (upper_field == options_.false_string || upper_field == "FALSE" ||
            upper_field == "NO" || upper_field == "N" || upper_field == "F" ||
            upper_field == "OFF" || upper_field == "0") {
            return false;
        }

        // Try date/datetime
        auto datetime = parse_datetime(field, options_.datetime_format);
        if (datetime.time_since_epoch().count() > 0) {
            return datetime;
        }

        auto date = parse_datetime(field, options_.date_format);
        if (date.time_since_epoch().count() > 0) {
            return date;
        }

        // Default to string
        return field;
    }

    // Convert based on type hint
    if (type_hint == "boolean" || type_hint == "bool") {
        std::string upper_field = field;
        std::transform(upper_field.begin(), upper_field.end(), upper_field.begin(), ::toupper);
        return upper_field == options_.true_string || upper_field == "1" ||
               upper_field == "TRUE" || upper_field == "YES" || upper_field == "Y" ||
               upper_field == "T" || upper_field == "ON";
    }
    else if (type_hint == "integer" || type_hint == "int" || type_hint == "bigint") {
        try {
            size_t pos;
            long long int_val = std::stoll(field, &pos);
            // Check if the entire string was consumed
            if (pos == field.length()) {
                return int_val;
            } else {
                // Not all characters were consumed, fall back to string
                return field;
            }
        } catch (const std::exception&) {
            // Fall back to string on conversion error
            return field;
        }
    }
    else if (type_hint == "double" || type_hint == "float" || type_hint == "decimal") {
        try {
            size_t pos;
            double double_val = std::stod(field, &pos);
            // Check if the entire string was consumed
            if (pos == field.length()) {
                return double_val;
            } else {
                // Not all characters were consumed, fall back to string
                return field;
            }
        } catch (const std::exception&) {
            // Fall back to string on conversion error
            return field;
        }
    }
    else if (type_hint == "date") {
        return parse_datetime(field, options_.date_format);
    }
    else if (type_hint == "datetime" || type_hint == "timestamp") {
        return parse_datetime(field, options_.datetime_format);
    }
    else {
        return field;
    }
}

std::chrono::system_clock::time_point CsvFieldParser::parse_datetime(
    const std::string& value, const std::string& format) const {

    std::tm tm = {};
    std::stringstream ss(value);
    ss >> std::get_time(&tm, format.c_str());

    if (ss.fail()) {
        return std::chrono::system_clock::time_point{};
    }

    return std::chrono::system_clock::from_time_t(std::mktime(&tm));
}

std::vector<std::string> CsvFieldParser::parse_line(const std::string& line) {
    std::vector<std::string> fields;
    // Pre-allocate based on expected number of fields
    fields.reserve(32); // Common CSV files have < 32 columns

    size_t pos = 0;
    // Use string_view to avoid copies during parsing
    std::string_view line_view(line);

    while (pos < line.length()) {
        size_t old_pos = pos;
        fields.push_back(parse_field(line, pos));
        
        // Safeguard against infinite loops - ensure pos always advances
        if (pos == old_pos) {
            // If parse_field didn't advance position, force advance by 1
            ++pos;
        }
    }

    return fields;
}

// CsvExtractor implementation

void CsvExtractor::initialize(const std::unordered_map<std::string, std::any>& config,
                            [[maybe_unused]] core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-csv-extractor");
    logger->info("Initializing CSV extractor");
    
    // Debug all config keys
    for (const auto& [key, value] : config) {
        logger->info("CSV Config key: '{}' type: {}", key, value.type().name());
    }
    logger->info("CSV extractor about to process config parameters");

    // Get filepath
    if (config.find("filepath") == config.end()) {
        throw common::ConfigurationException("CSV extractor requires 'filepath' parameter");
    }
    try {
        filepath_ = std::any_cast<std::string>(config.at("filepath"));
        logger->debug("Filepath: {}", filepath_);
    } catch (const std::bad_any_cast& e) {
        logger->error("Failed to cast filepath: {}", e.what());
        throw;
    }

    // Configure options
    if (config.find("delimiter") != config.end()) {
        try {
            // Try char first, then string
            try {
                options_.delimiter = std::any_cast<char>(config.at("delimiter"));
            } catch (const std::bad_any_cast&) {
                // Try as string and take first character
                std::string delim_str = std::any_cast<std::string>(config.at("delimiter"));
                if (!delim_str.empty()) {
                    options_.delimiter = delim_str[0];
                } else {
                    throw std::invalid_argument("Delimiter string cannot be empty");
                }
            }
        } catch (const std::bad_any_cast& e) {
            logger->error("Failed to cast delimiter (should be char or string): {}", e.what());
            throw;
        }
    }
    if (config.find("quote_char") != config.end()) {
        try {
            // Try char first, then string
            try {
                options_.quote_char = std::any_cast<char>(config.at("quote_char"));
            } catch (const std::bad_any_cast&) {
                // Try as string and take first character
                std::string quote_str = std::any_cast<std::string>(config.at("quote_char"));
                if (!quote_str.empty()) {
                    options_.quote_char = quote_str[0];
                } else {
                    throw std::invalid_argument("Quote char string cannot be empty");
                }
            }
        } catch (const std::bad_any_cast& e) {
            logger->error("Failed to cast quote_char (should be char or string): {}", e.what());
            throw;
        }
    }
    if (config.find("escape_char") != config.end()) {
        try {
            // Try char first, then string
            try {
                options_.escape_char = std::any_cast<char>(config.at("escape_char"));
            } catch (const std::bad_any_cast&) {
                // Try as string and take first character
                std::string escape_str = std::any_cast<std::string>(config.at("escape_char"));
                if (!escape_str.empty()) {
                    options_.escape_char = escape_str[0];
                } else {
                    throw std::invalid_argument("Escape char string cannot be empty");
                }
            }
        } catch (const std::bad_any_cast& e) {
            logger->error("Failed to cast escape_char (should be char or string): {}", e.what());
            throw;
        }
    }
    if (config.find("has_header") != config.end()) {
        try {
            options_.has_header = std::any_cast<bool>(config.at("has_header"));
            logger->debug("Has header: {}", options_.has_header);
        } catch (const std::bad_any_cast& e) {
            logger->error("Failed to cast has_header: {}", e.what());
            throw;
        }
    }
    if (config.find("encoding") != config.end()) {
        try {
            options_.encoding = std::any_cast<std::string>(config.at("encoding"));
        } catch (const std::bad_any_cast& e) {
            logger->error("Failed to cast encoding: {}", e.what());
            throw;
        }
    }
    if (config.find("skip_lines") != config.end()) {
        try {
            // Try size_t first, then int
            try {
                options_.skip_lines = std::any_cast<size_t>(config.at("skip_lines"));
            } catch (const std::bad_any_cast&) {
                // Try as int and convert to size_t
                int skip_lines_int = std::any_cast<int>(config.at("skip_lines"));
                if (skip_lines_int < 0) {
                    throw std::invalid_argument("skip_lines cannot be negative");
                }
                options_.skip_lines = static_cast<size_t>(skip_lines_int);
            }
        } catch (const std::bad_any_cast& e) {
            logger->error("Failed to cast skip_lines (should be size_t or int): {}", e.what());
            throw;
        }
    }
    if (config.find("max_lines") != config.end()) {
        try {
            // Try size_t first, then int
            try {
                options_.max_lines = std::any_cast<size_t>(config.at("max_lines"));
            } catch (const std::bad_any_cast&) {
                // Try as int and convert to size_t
                int max_lines_int = std::any_cast<int>(config.at("max_lines"));
                if (max_lines_int < 0) {
                    throw std::invalid_argument("max_lines cannot be negative");
                }
                options_.max_lines = static_cast<size_t>(max_lines_int);
            }
        } catch (const std::bad_any_cast& e) {
            logger->error("Failed to cast max_lines (should be size_t or int): {}", e.what());
            throw;
        }
    }
    if (config.find("max_records") != config.end()) {
        try {
            // Try size_t first since that's what the tests use
            // Use ExtractorBase options_ for max_records
            ExtractorBase::options_.max_records = std::any_cast<size_t>(config.at("max_records"));
            logger->debug("Max records set to (size_t): {}", ExtractorBase::options_.max_records);
        } catch (const std::bad_any_cast& e) {
            try {
                // On this platform, size_t might be unsigned long, so try that
                ExtractorBase::options_.max_records = std::any_cast<unsigned long>(config.at("max_records"));
                logger->debug("Max records set to (unsigned long): {}", ExtractorBase::options_.max_records);
            } catch (const std::bad_any_cast& e2) {
                try {
                    ExtractorBase::options_.max_records = std::any_cast<unsigned int>(config.at("max_records"));
                    logger->debug("Max records set to (unsigned int): {}", ExtractorBase::options_.max_records);
                } catch (const std::bad_any_cast& e3) {
                    logger->error("Failed to cast max_records (tried size_t, unsigned long, unsigned int): original error: {}", e.what());
                    throw;
                }
            }
        }
    }
    
    // Handle column selection
    if (config.find("columns") != config.end()) {
        try {
            ExtractorBase::options_.columns = std::any_cast<std::vector<std::string>>(config.at("columns"));
            logger->debug("Column selection enabled: {} columns", ExtractorBase::options_.columns.size());
        } catch (const std::bad_any_cast& e) {
            logger->error("Failed to cast columns parameter: {}", e.what());
            throw;
        }
    }

    // Initialize parser
    parser_ = CsvFieldParser(options_);

    // Open file
    open_file(filepath_);

    // Skip initial lines if requested
    for (size_t i = 0; i < options_.skip_lines && file_stream_.good(); ++i) {
        std::string line;
        std::getline(file_stream_, line);
        current_line_++;
    }

    // Read header if present
    if (options_.has_header) {
        read_header();
    } else if (config.find("column_names") != config.end()) {
        try {
            column_names_ = std::any_cast<std::vector<std::string>>(config.at("column_names"));
        } catch (const std::bad_any_cast& e) {
            logger->error("Failed to cast column_names: {}", e.what());
            throw;
        }
    }

    // Get column types if provided
    if (config.find("column_types") != config.end()) {
        try {
            // Try vector first (indexed by position)
            try {
                column_types_ = std::any_cast<std::vector<std::string>>(config.at("column_types"));
            } catch (const std::bad_any_cast&) {
                // Try map (column name to type)
                auto column_type_map = std::any_cast<std::unordered_map<std::string, std::string>>(config.at("column_types"));
                
                // Convert map to vector based on column names
                column_types_.resize(column_names_.size());
                for (size_t i = 0; i < column_names_.size(); ++i) {
                    auto it = column_type_map.find(column_names_[i]);
                    if (it != column_type_map.end()) {
                        column_types_[i] = it->second;
                    } else {
                        column_types_[i] = "string"; // Default type
                    }
                }
            }
        } catch (const std::bad_any_cast& e) {
            logger->error("Failed to cast column_types (should be vector<string> or map<string,string>): {}", e.what());
            throw;
        }
    } else {
        // Infer types from data
        infer_column_types();
    }

    // ExtractorBase handles timing automatically
    logger->info("CSV extractor initialized for file: {}", filepath_);
}

core::RecordBatch CsvExtractor::extract_batch(size_t batch_size,
                                            core::ProcessingContext& context) {
    core::RecordBatch batch(batch_size);
    batch.reserve(batch_size);

    // Pre-allocate line buffer for better performance
    std::string line;
    line.reserve(4096); // Common line length

    auto logger = common::Logger::get("omop-csv-extractor");
    size_t count = 0;
    
    logger->debug("Extract batch: batch_size={}, max_records={}, extracted_count={}", 
                  batch_size, ExtractorBase::options_.max_records, stats_.successful_records);

    while (file_stream_.good() && count < batch_size && has_more_data_) {
        // Check max_records before processing
        if (ExtractorBase::options_.max_records > 0 && stats_.successful_records >= ExtractorBase::options_.max_records) {
            logger->debug("Reached max_records limit: {} >= {}", stats_.successful_records, ExtractorBase::options_.max_records);
            has_more_data_ = false;
            break;
        }

        line.clear();
        
        // First try reading a single line - most CSV records are single-line
        if (!std::getline(file_stream_, line)) {
            // End of file
            has_more_data_ = false;
            break;
        }
        current_line_++;
        
        // Check if this line has unmatched quotes (multi-line record)
        bool has_unmatched_quotes = false;
        bool in_quotes = false;
        bool escape_next = false;
        
        for (char c : line) {
            if (escape_next) {
                escape_next = false;
                continue;
            }
            if (c == options_.escape_char) {
                escape_next = true;
                continue;
            }
            if (c == options_.quote_char) {
                in_quotes = !in_quotes;
            }
        }
        has_unmatched_quotes = in_quotes;
        
        // If we have unmatched quotes, we need to read additional lines
        if (has_unmatched_quotes) {
            std::string continuation_line;
            while (file_stream_.good() && in_quotes) {
                if (!std::getline(file_stream_, continuation_line)) {
                    break;
                }
                current_line_++;
                line += '\n' + continuation_line;
                
                // Check quotes in continuation line
                escape_next = false;
                for (char c : continuation_line) {
                    if (escape_next) {
                        escape_next = false;
                        continue;
                    }
                    if (c == options_.escape_char) {
                        escape_next = true;
                        continue;
                    }
                    if (c == options_.quote_char) {
                        in_quotes = !in_quotes;
                    }
                }
            }
        }

        if (line.empty() && options_.skip_empty_lines) {
            continue;
        }

        // max_lines should limit the number of data lines, not including header
        size_t data_lines_read = options_.has_header ? current_line_ - 1 : current_line_;
        if (options_.max_lines > 0 && data_lines_read > options_.max_lines) {
            has_more_data_ = false;
            break;
        }

        try {
            auto fields = parser_.parse_line(line);

            if (!fields.empty()) {
                auto record = create_record(fields);
                batch.addRecord(std::move(record));
                count++;
                stats_.successful_records++;
            }

        } catch (const std::exception& e) {
            stats_.failed_records++;
            context.increment_errors();

            logger->warn("Error parsing line {}: {}", current_line_, e.what());

            // Continue processing on error by default
        }
    }

    // Check if we've reached end of file
    if (!file_stream_.good() ||
        (options_.max_lines > 0 && current_line_ >= options_.max_lines) ||
        file_stream_.peek() == EOF) {
        has_more_data_ = false;
    }

    return batch;
}

void CsvExtractor::finalize([[maybe_unused]] core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-csv-extractor");

    if (file_stream_.is_open()) {
        file_stream_.close();
    }

    auto end_time = std::chrono::system_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(
        end_time - stats_.start_time).count();

    logger->info("CSV extraction completed: {} records extracted, {} errors in {} seconds",
                stats_.successful_records, stats_.failed_records, duration);
}

bool CsvExtractor::has_more_data() const {
    return has_more_data_ && file_stream_.good();
}

std::unordered_map<std::string, std::any> CsvExtractor::get_statistics() const {
    std::unordered_map<std::string, std::any> stats;

    stats["filepath"] = filepath_;
    stats["total_lines"] = current_line_;
    stats["extracted_count"] = stats_.successful_records;
    stats["records_extracted"] = stats_.successful_records;  // Add expected key
    stats["error_count"] = stats_.failed_records;
    stats["column_count"] = column_names_.size();

    auto current_time = std::chrono::system_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(
        current_time - stats_.start_time).count();
    stats["extraction_time_seconds"] = duration;
    stats["extraction_time"] = duration;  // Add expected key

    if (duration > 0) {
        stats["records_per_second"] = static_cast<double>(stats_.successful_records) / duration;
    }

    return stats;
}

void CsvExtractor::open_file(const std::string& filepath) {
    if (!std::filesystem::exists(filepath)) {
        throw common::ExtractionException(
            "CSV file not found: '" + filepath + "'", "csv");
    }

    file_stream_.open(filepath, std::ios::in);
    if (!file_stream_.is_open()) {
        throw common::ExtractionException(
            "Failed to open CSV file: '" + filepath + "'", "csv");
    }

    // Count total lines if needed
    if (options_.max_lines == 0) {
        std::ifstream count_stream(filepath);
        total_lines_ = std::count(std::istreambuf_iterator<char>(count_stream),
                                 std::istreambuf_iterator<char>(), '\n');
        count_stream.close();
    }
}

void CsvExtractor::read_header() {
    std::string header_line;
    std::getline(file_stream_, header_line);
    current_line_++;

    if (header_line.empty()) {
        throw common::ExtractionException("Empty header line in CSV file", "csv");
    }

    column_names_ = parser_.parse_line(header_line);

    // Trim column names
    for (auto& name : column_names_) {
        name.erase(0, name.find_first_not_of(" \t\r\n"));
        name.erase(name.find_last_not_of(" \t\r\n") + 1);
    }
}

void CsvExtractor::infer_column_types(size_t sample_size) {
    auto logger = common::Logger::get("omop-csv-extractor");
    logger->info("Inferring column types from {} sample rows", sample_size);

    // Save current position
    auto current_pos = file_stream_.tellg();

    // Read sample rows
    std::vector<std::vector<std::string>> sample_rows;
    size_t rows_read = 0;

    while (file_stream_.good() && rows_read < sample_size) {
        std::string line;
        std::getline(file_stream_, line);

        if (line.empty() && options_.skip_empty_lines) {
            continue;
        }

        auto fields = parser_.parse_line(line);
        if (!fields.empty()) {
            sample_rows.push_back(fields);
            rows_read++;
        }
    }

    // Infer types for each column
    column_types_.resize(column_names_.size(), "string");

    for (size_t col = 0; col < column_names_.size(); ++col) {
        bool all_integer = true;
        bool all_double = true;
        bool all_boolean = true;
        bool all_date = true;
        bool all_datetime = true;

        for (const auto& row : sample_rows) {
            if (col >= row.size() || row[col].empty() ||
                row[col] == options_.null_string) {
                continue;
            }

            const std::string& value = row[col];

            // Check integer
            if (all_integer) {
                try {
                    size_t pos;
                    std::stoll(value, &pos);
                    // Must consume entire string to be considered integer
                    if (pos != value.length()) {
                        all_integer = false;
                    }
                } catch (...) {
                    all_integer = false;
                }
            }

            // Check double (if not integer, or if integer parsing didn't consume entire string)
            if (all_double) {
                try {
                    size_t pos;
                    std::stod(value, &pos);
                    // Must consume entire string to be considered double
                    if (pos != value.length()) {
                        all_double = false;
                    }
                } catch (...) {
                    all_double = false;
                }
            }

            // Check boolean
            if (all_boolean) {
                std::string upper = value;
                std::transform(upper.begin(), upper.end(), upper.begin(), ::toupper);
                if (upper != options_.true_string && upper != options_.false_string &&
                    upper != "1" && upper != "0" && upper != "TRUE" && upper != "FALSE" &&
                    upper != "YES" && upper != "NO" && upper != "Y" && upper != "N") {
                    all_boolean = false;
                }
            }

            // Skip date/datetime inference by default - CSV extractor keeps dates as strings
            // This can be enabled later if needed via configuration
            all_datetime = false;
            all_date = false;
        }

        // Assign inferred type
        if (all_integer) {
            column_types_[col] = "integer";
        } else if (all_double) {
            column_types_[col] = "double";
        } else if (all_boolean) {
            column_types_[col] = "boolean";
        } else if (all_datetime) {
            column_types_[col] = "datetime";
        } else if (all_date) {
            column_types_[col] = "date";
        } else {
            column_types_[col] = "string";
        }
    }

    // Restore file position
    file_stream_.clear();
    file_stream_.seekg(current_pos);

    logger->info("Column types inferred successfully");
}

core::Record CsvExtractor::create_record(const std::vector<std::string>& fields) {
    core::Record record;

    // Bounds check
    if (fields.size() > column_names_.size()) {
        auto logger = common::Logger::get("omop-csv-extractor");
        logger->warn("Row has {} fields but only {} columns defined. Extra fields will be ignored.",
                    fields.size(), column_names_.size());
    }

    for (size_t i = 0; i < fields.size() && i < column_names_.size(); ++i) {
        const std::string& field_name = column_names_[i];
        const std::string& field_value = fields[i];

        // Get type hint
        std::string type_hint;
        if (i < column_types_.size()) {
            type_hint = column_types_[i];
        }

        // Convert and set field
        try {
            auto converted_value = parser_.convert_field(field_value, type_hint);
            record.setField(field_name, converted_value);
        } catch (const std::exception& e) {
            // Log conversion error but continue
            auto logger = common::Logger::get("omop-csv-extractor");
            logger->debug("Failed to convert field '{}' value '{}': {}",
                        field_name, field_value, e.what());

            // Store as string
            record.setField(field_name, field_value);
        }
    }

    // Add metadata
    // Set metadata using the Record metadata structure
    core::Record::RecordMetadata metadata;
    metadata.custom["source_file"] = filepath_;
    metadata.custom["line_number"] = current_line_;
    metadata.extraction_time = std::chrono::system_clock::now();
    record.setMetadata(metadata);

    // Apply column filtering if specified
    if (!ExtractorBase::options_.columns.empty()) {
        return ExtractorBase::selectColumns(record);
    }

    return record;
}

bool CsvExtractor::read_complete_record(std::string& record) {
    record.clear();
    std::string current_line;
    bool in_quotes = false;
    bool first_line = true;

    while (file_stream_.good()) {
        if (!std::getline(file_stream_, current_line)) {
            // End of file
            break;
        }

        // Always increment line number for each line read
        current_line_++;

        // Check if this line contains quotes
        bool has_quotes = false;
        for (char c : current_line) {
            if (c == options_.quote_char) {
                has_quotes = true;
                break;
            }
        }

        // If we're not in quotes and this line has no quotes, it's a simple single-line record
        if (!in_quotes && !has_quotes) {
            record = current_line;
            return !record.empty() || !options_.skip_empty_lines;
        }

        // Add to record (with newline if this is a continuation)
        if (!first_line) {
            record += '\n';
        }
        record += current_line;
        first_line = false;

        // Count quotes to determine if we're inside a quoted field
        bool escape_next = false;
        for (char c : current_line) {
            if (escape_next) {
                escape_next = false;
                continue;
            }

            if (c == options_.escape_char) {
                escape_next = true;
                continue;
            }

            if (c == options_.quote_char) {
                in_quotes = !in_quotes;
            }
        }

        // If we're not in quotes anymore, the record is complete
        if (!in_quotes) {
            return true;
        }
    }

    // Return whatever we have, even if incomplete
    return !record.empty();
}

bool CsvExtractor::is_compressed_file(const std::string& filepath) {
    static const std::vector<std::string> compressed_exts = {".gz", ".bz2", ".zip", ".xz"};
    for (const auto& ext : compressed_exts) {
        if (filepath.size() >= ext.size() &&
            filepath.compare(filepath.size() - ext.size(), ext.size(), ext) == 0) {
            return true;
        }
    }
    return false;
}

// ExtractorBase pure virtual method implementations

bool CsvExtractor::connect() {
    if (is_connected_) {
        return true;
    }
    
    if (filepath_.empty()) {
        handleError("No file path specified for CSV extractor");
        return false;
    }
    
    try {
        open_file(filepath_);
        is_connected_ = true;
        return true;
    } catch (const std::exception& e) {
        handleError(std::string("Failed to connect to CSV file: ") + e.what());
        return false;
    }
}

void CsvExtractor::disconnect() {
    if (file_stream_.is_open()) {
        file_stream_.close();
    }
    is_connected_ = false;
    has_more_data_ = true;
    current_line_ = 0;
}

std::vector<core::Record> CsvExtractor::extractBatchImpl(size_t batch_size) {
    std::vector<core::Record> records;
    records.reserve(batch_size);
    
    if (!file_stream_.is_open() || file_stream_.eof()) {
        has_more_data_ = false;
        return records;
    }
    
    std::string line;
    size_t records_read = 0;
    
    while (records_read < batch_size && std::getline(file_stream_, line)) {
        current_line_++;
        
        // Skip empty lines if configured
        if (options_.skip_empty_lines && line.empty()) {
            continue;
        }
        
        try {
            auto fields = parser_.parse_line(line);
            if (fields.size() != column_names_.size()) {
                handleError(std::string("Field count mismatch at line ") + 
                           std::to_string(current_line_));
                stats_.failed_records++;
                continue;
            }
            
            // Convert fields to record using the existing method
            core::Record record = create_record(fields);
            records.push_back(std::move(record));
            records_read++;
            stats_.successful_records++;
            
        } catch (const std::exception& e) {
            handleError(std::string("Error parsing line ") + 
                       std::to_string(current_line_) + ": " + e.what());
            stats_.failed_records++;
        }
        
        // Update progress
        updateProgress(stats_.successful_records + stats_.failed_records);
        
        // Check if we've hit max records limit
        if (ExtractorBase::options_.max_records > 0 && 
            (stats_.successful_records + stats_.failed_records) >= ExtractorBase::options_.max_records) {
            has_more_data_ = false;
            break;
        }
    }
    
    if (file_stream_.eof()) {
        has_more_data_ = false;
    }
    
    return records;
}

core::Record CsvExtractor::convertToRecord(const std::any& source_data) {
    try {
        // Expect source_data to be a vector<string> of CSV fields
        auto fields = std::any_cast<std::vector<std::string>>(source_data);
        return create_record(fields);
    } catch (const std::bad_any_cast& e) {
        throw common::ExtractionException(
            std::string("Invalid source data type for CSV conversion: ") + e.what(),
            "csv_extractor");
    }
}

SourceSchema CsvExtractor::getSchema() const {
    SourceSchema schema;
    schema.source_name = filepath_;
    schema.source_type = "csv";
    
    // Build column information
    for (size_t i = 0; i < column_names_.size(); ++i) {
        SourceSchema::Column col;
        col.name = column_names_[i];
        col.data_type = (i < column_types_.size()) ? column_types_[i] : "string";
        col.nullable = true;
        col.description = "CSV column";
        schema.columns.push_back(col);
    }
    
    schema.metadata["delimiter"] = std::string(1, options_.delimiter);
    schema.metadata["quote_char"] = std::string(1, options_.quote_char);
    schema.metadata["has_header"] = options_.has_header ? "true" : "false";
    schema.metadata["encoding"] = options_.encoding;
    
    return schema;
}

omop::common::ValidationResult CsvExtractor::validateSource() {
    omop::common::ValidationResult result;
    
    // Check if file exists
    if (!std::filesystem::exists(filepath_)) {
        result.add_error("", "CSV file does not exist: " + filepath_, "csv_file_not_found");
        return result;
    }
    
    // Check if file is readable
    std::ifstream test_file(filepath_);
    if (!test_file.is_open()) {
        result.add_error("", "Cannot open CSV file: " + filepath_, "csv_file_not_readable");
        return result;
    }
    test_file.close();
    
    // Check if file is empty
    if (std::filesystem::file_size(filepath_) == 0) {
        // Note: ValidationResult doesn't have add_warning, so we'll just log this
        auto logger = common::Logger::get("omop-csv-extractor");
        logger->warn("CSV file is empty: {}", filepath_);
        return result;
    }
    
    // Basic parsing validation
    try {
        std::ifstream file(filepath_);
        std::string first_line;
        if (std::getline(file, first_line)) {
            parser_.parse_line(first_line);
        }
    } catch (const std::exception& e) {
        result.add_error("", "Failed to parse CSV header: " + std::string(e.what()), "csv_parse_error");
        return result;
    }
    
    return result;
}

// MultiFileCsvExtractor implementation

void MultiFileCsvExtractor::initialize(const std::unordered_map<std::string, std::any>& config,
                                     core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-multi-csv-extractor");

    // Get file paths - try both "files" and "filepaths"
    if (config.find("files") != config.end()) {
        try {
            file_paths_ = std::any_cast<std::vector<std::string>>(config.at("files"));
        } catch (const std::bad_any_cast& e) {
            logger->error("Failed to cast 'files' parameter to vector<string>: {}", e.what());
            throw;
        }
    } else if (config.find("filepaths") != config.end()) {
        try {
            file_paths_ = std::any_cast<std::vector<std::string>>(config.at("filepaths"));
        } catch (const std::bad_any_cast& e) {
            logger->error("Failed to cast 'filepaths' parameter to vector<string>: {}", e.what());
            throw;
        }
    } else {
        throw common::ConfigurationException("Multi-file CSV extractor requires 'files' or 'filepaths' parameter");
    }

    if (file_paths_.empty()) {
        throw common::ConfigurationException("No files provided for multi-file CSV extractor");
    }

    // Get skip headers option
    if (config.find("skip_headers_after_first") != config.end()) {
        try {
            skip_headers_after_first_ = std::any_cast<bool>(config.at("skip_headers_after_first"));
        } catch (const std::bad_any_cast& e) {
            logger->error("Failed to cast 'skip_headers_after_first' parameter to bool: {}", e.what());
            throw;
        }
    }

    logger->info("Initializing multi-file CSV extractor with {} files", file_paths_.size());

    // Create modified config for first file
    auto file_config = config;
    file_config["filepath"] = file_paths_[0];

    // Initialize with first file
    CsvExtractor::initialize(file_config, context);
}

core::RecordBatch MultiFileCsvExtractor::extract_batch(size_t batch_size,
                                                     core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-multi-csv-extractor");
    
    // Try to extract from current file first
    auto batch = CsvExtractor::extract_batch(batch_size, context);
    
    // If we got a full batch, return it
    if (batch.size() >= batch_size) {
        return batch;
    }
    
    // If current file is exhausted, try to move to next file
    while (!CsvExtractor::has_more_data() && next_file()) {
        logger->debug("Switched to next file, extracting remaining records");
        
        // Extract from the new file
        auto next_batch = CsvExtractor::extract_batch(batch_size - batch.size(), context);
        
        // Add records from next batch to current batch
        auto next_records = next_batch.getRecords();
        for (auto& record : next_records) {
            batch.addRecord(std::move(record));
        }
        
        // If we got a full batch, return it
        if (batch.size() >= batch_size) {
            break;
        }
    }
    
    return batch;
}

bool MultiFileCsvExtractor::has_more_data() const {
    return CsvExtractor::has_more_data() ||
           (current_file_index_ + 1 < file_paths_.size());
}

bool MultiFileCsvExtractor::next_file() {
    auto logger = common::Logger::get("omop-multi-csv-extractor");

    current_file_index_++;
    if (current_file_index_ >= file_paths_.size()) {
        return false;
    }

    // Close current file
    if (file_stream_.is_open()) {
        file_stream_.close();
    }

    // Open next file
    filepath_ = file_paths_[current_file_index_];
    logger->info("Switching to file {}/{}: {}",
                current_file_index_ + 1, file_paths_.size(), filepath_);

    open_file(filepath_);

    // Skip header if needed
    if (options_.has_header && skip_headers_after_first_) {
        std::string header_line;
        std::getline(file_stream_, header_line);
        current_line_++;
    }

    has_more_data_ = true;
    return true;
}

// CsvDirectoryExtractor implementation

void CsvDirectoryExtractor::initialize(const std::unordered_map<std::string, std::any>& config,
                                     core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-csv-directory-extractor");

    // Get directory path
    if (config.find("directory") == config.end()) {
        throw common::ConfigurationException("CSV directory extractor requires 'directory' parameter");
    }
    
    try {
        directory_path_ = std::any_cast<std::string>(config.at("directory"));
    } catch (const std::bad_any_cast& e) {
        logger->error("Failed to cast 'directory' parameter to string: {}", e.what());
        throw;
    }

    // Get pattern
    std::string pattern = ".*\\.csv$";  // Default pattern
    if (config.find("pattern") != config.end()) {
        try {
            // Try std::string first
            pattern = std::any_cast<std::string>(config.at("pattern"));
        } catch (const std::bad_any_cast&) {
            try {
                // Try const char* if std::string fails
                pattern = std::any_cast<const char*>(config.at("pattern"));
            } catch (const std::bad_any_cast& e) {
                logger->error("Failed to cast 'pattern' parameter to string or const char*: {}", e.what());
                logger->error("Type of 'pattern' parameter: {}", config.at("pattern").type().name());
                throw;
            }
        }
    }
    file_pattern_ = std::regex(pattern, std::regex_constants::icase);

    // Get recursive option
    if (config.find("recursive") != config.end()) {
        try {
            recursive_search_ = std::any_cast<bool>(config.at("recursive"));
        } catch (const std::bad_any_cast& e) {
            logger->error("Failed to cast 'recursive' parameter to bool: {}", e.what());
            throw;
        }
    }

    // Find CSV files
    file_paths_ = find_csv_files(directory_path_, pattern, recursive_search_);

    if (file_paths_.empty()) {
        throw common::ExtractionException(
            "No CSV files found in directory '" + directory_path_ + "' matching pattern '" + pattern + "'",
            "csv_directory");
    }

    logger->info("Found {} CSV files in directory '{}'",
                file_paths_.size(), directory_path_);

    // Initialize with multi-file config
    auto multi_config = config;
    multi_config["files"] = file_paths_;

    MultiFileCsvExtractor::initialize(multi_config, context);
}

std::vector<std::string> CsvDirectoryExtractor::find_csv_files(const std::string& directory,
                                                             const std::string& pattern,
                                                             bool recursive) {
    std::vector<std::string> files;
    std::regex file_regex(pattern, std::regex_constants::icase);

    try {
        if (recursive) {
            for (const auto& entry : std::filesystem::recursive_directory_iterator(directory)) {
                if (entry.is_regular_file() &&
                    std::regex_match(entry.path().filename().string(), file_regex)) {
                    files.push_back(entry.path().string());
                }
            }
        } else {
            for (const auto& entry : std::filesystem::directory_iterator(directory)) {
                if (entry.is_regular_file() &&
                    std::regex_match(entry.path().filename().string(), file_regex)) {
                    files.push_back(entry.path().string());
                }
            }
        }
    } catch (const std::filesystem::filesystem_error& e) {
        throw common::ExtractionException(
            "Error accessing directory '" + directory + "': " + e.what(),
            "csv_directory");
    }

    // Sort files for consistent ordering
    std::sort(files.begin(), files.end());

    return files;
}

// CsvExtractorFactory implementation

std::unique_ptr<core::IExtractor> CsvExtractorFactory::create(const std::string& type) {
    if (type == "csv") {
        return std::make_unique<CsvExtractor>();
    } else if (type == "multi_csv") {
        return std::make_unique<MultiFileCsvExtractor>();
    } else if (type == "csv_directory") {
        return std::make_unique<CsvDirectoryExtractor>();
    } else if (type == "compressed_csv") {
        return std::make_unique<CompressedCsvExtractor>();
    } else {
        throw common::ConfigurationException(
            "Unknown CSV extractor type: '" + type + "'");
    }
}

void CsvExtractorFactory::register_extractors() {
    // Register CSV extractors with the main factory
    ExtractorFactoryRegistry::register_type("csv",
        []() { return std::make_unique<CsvExtractor>(); });

    ExtractorFactoryRegistry::register_type("multi_csv",
        []() { return std::make_unique<MultiFileCsvExtractor>(); });

    ExtractorFactoryRegistry::register_type("csv_directory",
        []() { return std::make_unique<CsvDirectoryExtractor>(); });

    ExtractorFactoryRegistry::register_type("compressed_csv",
        []() { return std::make_unique<CompressedCsvExtractor>(); });

    auto logger = common::Logger::get("omop-csv-extractor-factory");
    logger->info("Registered CSV extractor types: csv, multi_csv, csv_directory, compressed_csv");
}

} // namespace omop::extract

File src/lib/extract/extractor_base.cpp:

/**
 * @file extractor_base.cpp
 * @brief Implementation of abstract base class for data extractors
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "extractor_base.h"
#include <chrono>
#include <algorithm>
#include <thread>
#include <memory>
#include <mutex>

#ifdef __linux__
#include <sys/resource.h>
#endif

namespace omop::extract {



// Thread-local storage for error accumulation
thread_local std::vector<std::string> ExtractionErrorPolicy::accumulated_errors_;
thread_local std::mutex ExtractionErrorPolicy::accumulated_errors_mutex_;

// ExtractionErrorPolicy implementation

bool ExtractionErrorPolicy::handle_error(const std::string& error,
                                        const std::any& context,
                                        std::shared_ptr<common::Logger> logger,
                                        ErrorAction action) {
    switch (action) {
        case ErrorAction::THROW_IMMEDIATELY:
            throw common::ExtractionException(error, "extraction");

        case ErrorAction::LOG_AND_CONTINUE:
            logger->error("Extraction error: {}", error);
            if (context.has_value()) {
                try {
                    logger->debug("Error context: {}", std::any_cast<std::string>(context));
                } catch (...) {
                    // Context not convertible to string
                }
            }
            return true;  // Continue processing

        case ErrorAction::ACCUMULATE_ERRORS: {
            std::lock_guard<std::mutex> lock(accumulated_errors_mutex_);
            if (accumulated_errors_.size() < MAX_ACCUMULATED_ERRORS) {
                accumulated_errors_.push_back(error);
                logger->debug("Error accumulated ({}/{}): {}", 
                    accumulated_errors_.size(), MAX_ACCUMULATED_ERRORS, error);
            } else {
                logger->warn("Maximum accumulated errors ({}) reached, discarding: {}", 
                    MAX_ACCUMULATED_ERRORS, error);
            }
            return true;  // Continue processing
        }
    }

    return false;
}

std::vector<std::string> ExtractionErrorPolicy::get_accumulated_errors() {
    std::lock_guard<std::mutex> lock(accumulated_errors_mutex_);
    return accumulated_errors_;
}

void ExtractionErrorPolicy::clear_accumulated_errors() {
    std::lock_guard<std::mutex> lock(accumulated_errors_mutex_);
    accumulated_errors_.clear();
}

ExtractorBase::ExtractorBase(const std::string& name,
                           std::shared_ptr<common::ConfigurationManager> config,
                           std::shared_ptr<common::Logger> logger)
    : name_(name), config_(config), logger_(logger) {

    if (!logger_) {
        logger_ = common::Logger::get("ExtractorBase");
    }
}

void ExtractorBase::initialize(const std::unordered_map<std::string, std::any>& config,
                              core::ProcessingContext& context) {
    if (is_initialized_) {
        logger_->warn("Extractor {} already initialized", name_);
        return;
    }

    logger_->info("Initializing extractor: {}", name_);
    start_time_ = std::chrono::steady_clock::now();

    try {
        // Update options from config
        if (auto it = config.find("error_policy"); it != config.end()) {
            std::string policy = std::any_cast<std::string>(it->second);
            error_action_ = (policy == "throw") ? ExtractionErrorPolicy::ErrorAction::THROW_IMMEDIATELY :
                           (policy == "accumulate") ? ExtractionErrorPolicy::ErrorAction::ACCUMULATE_ERRORS :
                           ExtractionErrorPolicy::ErrorAction::LOG_AND_CONTINUE;
        }
        if (auto it = config.find("batch_size"); it != config.end()) {
            try {
                options_.batch_size = std::any_cast<size_t>(it->second);
            } catch (const std::bad_any_cast& e) {
                logger_->warn("Invalid type for batch_size config, using default: {}", options_.batch_size);
            }
        }
        if (auto it = config.find("max_records"); it != config.end()) {
            try {
                options_.max_records = std::any_cast<size_t>(it->second);
            } catch (const std::bad_any_cast& e) {
                logger_->warn("Invalid type for max_records config, using default: {}", options_.max_records);
            }
        }
        if (auto it = config.find("continue_on_error"); it != config.end()) {
            try {
                options_.continue_on_error = std::any_cast<bool>(it->second);
            } catch (const std::bad_any_cast& e) {
                logger_->warn("Invalid type for continue_on_error config, using default: {}", options_.continue_on_error);
            }
        }
        if (auto it = config.find("validate_schema"); it != config.end()) {
            try {
                options_.validate_schema = std::any_cast<bool>(it->second);
            } catch (const std::bad_any_cast& e) {
                logger_->warn("Invalid type for validate_schema config, using default: {}", options_.validate_schema);
            }
        }

        // Connect to data source
        if (!connect()) {
            logger_->error("Failed to connect to data source for extractor: {}", name_);
            throw std::runtime_error("Failed to connect to data source");
        }

        is_connected_ = true;

        // Validate source schema if enabled
        if (options_.validate_schema) {
            auto validation_result = validateSource();
            if (!validation_result.is_valid()) {
                std::string error_msg = "Unknown error";
                if (!validation_result.errors().empty()) {
                    error_msg = validation_result.errors()[0].error_message;
                }
                logger_->error("Source validation failed for extractor {}: {}", name_, error_msg);
                throw std::runtime_error("Source validation failed");
            }
        }

        is_initialized_ = true;
        was_ever_initialized_ = true;
        logger_->info("Extractor {} initialized successfully", name_);
        stats_.start_time = std::chrono::system_clock::now();

    } catch (const std::exception& e) {
        logger_->error("Exception during extractor initialization: {}", e.what());
        throw;
    }
}

core::RecordBatch ExtractorBase::extract_batch(size_t batch_size, [[maybe_unused]] core::ProcessingContext& context) {
    core::RecordBatch batch(batch_size);

    if (!is_initialized_) {
        logger_->error("Extractor not initialized");
        // Check if it was never initialized vs closed after initialization
        if (!was_ever_initialized_) {
            // Never initialized - throw exception
            throw std::runtime_error("Extractor not initialized");
        } else {
            // Was initialized but then closed - return empty batch
            return batch;
        }
    }

    try {
        // Extract raw batch from source
        auto raw_batch = extractBatchImpl(batch_size);
        common::ScopedPerformanceTimer perf_monitor("batch_processing", stats_.operation_timings, stats_.operation_counts);

        // Process each record
        for (const auto& raw_record : raw_batch) {
            try {
                common::ScopedPerformanceTimer record_monitor("record_conversion", stats_.operation_timings, stats_.operation_counts);
                stats_.total_records++;

                // Convert to standard record format
                auto record = convertToRecord(raw_record);

                // Apply column selection if specified
                if (!options_.columns.empty()) {
                    record = selectColumns(record);
                }

                // Apply filter if specified
                if (!options_.filter_expression.empty() && !applyFilter(record)) {
                    stats_.skipped_records++;
                    continue;
                }

                batch.addRecord(std::move(record));
                stats_.successful_records++;

            } catch (const std::exception& e) {
                stats_.failed_records++;
                bool should_continue = ExtractionErrorPolicy::handle_error(
                    std::string("Record processing error: ") + e.what(), raw_record, logger_, error_action_);
                if (!should_continue) {
                    break;
                }
            }
        }

        current_position_ += raw_batch.size();
        
        // Check if we've reached the end of data
        if (raw_batch.empty() || raw_batch.size() < batch_size) {
            has_more_data_ = false;
        }

    } catch (const std::exception& e) {
        logger_->error("Batch extraction error: {}", e.what());
        if (!options_.continue_on_error) {
            throw;
        }
    }

    return batch;
}

bool ExtractorBase::has_more_data() const {
    return is_connected_ && is_initialized_ && has_more_data_;
}

std::string ExtractorBase::get_type() const {
    return "base";  // Derived classes should override
}

void ExtractorBase::finalize([[maybe_unused]] core::ProcessingContext& context) {
    logger_->info("Finalizing extractor: {}", name_);

    // Calculate final statistics
    auto end_time = std::chrono::steady_clock::now();
    std::chrono::duration<double> elapsed = end_time - start_time_;
    stats_.extraction_time_seconds = elapsed.count();
    stats_.end_time = std::chrono::system_clock::now();
    stats_.calculate_rates();

    // Log comprehensive metrics
    logger_->info("Extraction completed: {} records extracted, {} failed, {:.2f} seconds",
                 stats_.successful_records, stats_.failed_records, stats_.extraction_time_seconds);
    logger_->info("Performance: {:.2f} records/sec, {:.2f} MB/sec",
                 stats_.records_per_second, stats_.bytes_per_second / (1024 * 1024));

    // Log operation timings
    for (const auto& [op, timing] : stats_.operation_timings) {
        auto count = stats_.operation_counts[op];
        logger_->debug("Operation '{}': {} calls, {:.2f}ms total, {:.2f}ms avg",
                      op, count, timing, timing / count);
    }

    close();
}

std::unordered_map<std::string, std::any> ExtractorBase::get_statistics() const {
    std::unordered_map<std::string, std::any> stats;
    stats["total_records"] = stats_.total_records;
    stats["successful_records"] = stats_.successful_records;
    stats["failed_records"] = stats_.failed_records;
    stats["skipped_records"] = stats_.skipped_records;
    stats["extraction_time_seconds"] = stats_.extraction_time_seconds;
    stats["bytes_processed"] = stats_.bytes_processed;
    stats["records_per_second"] = stats_.records_per_second;
    stats["bytes_per_second"] = stats_.bytes_per_second;
    stats["peak_memory_usage"] = stats_.peak_memory_usage;

    // Add timing breakdown
    std::unordered_map<std::string, std::any> timings;
    for (const auto& [op, timing] : stats_.operation_timings) {
        timings[op + "_total_ms"] = timing;
        timings[op + "_count"] = stats_.operation_counts.at(op);
        timings[op + "_avg_ms"] = timing / stats_.operation_counts.at(op);
    }
    stats["operation_timings"] = timings;

    return stats;
}

void ExtractorBase::reset() {
    logger_->info("Resetting extractor: {}", name_);

    current_position_ = 0;
    has_more_data_ = true;
    stats_ = ExtractionStats();

    // Call derived class reset implementation
    resetImpl();

    // Disconnect and reconnect
    if (is_connected_) {
        disconnect();
        is_connected_ = false;
    }

    if (is_initialized_) {
        connect();
        is_connected_ = true;
    }
}

void ExtractorBase::close() {
    logger_->info("Closing extractor: {}", name_);

    if (is_connected_) {
        disconnect();
        is_connected_ = false;
    }

    is_initialized_ = false;
}

void ExtractorBase::handleError(const std::string& error,
                               const std::optional<std::any>& record_context) {
    logger_->error("Extraction error in {}: {}", name_, error);

    // Increment failed records count
    stats_.failed_records++;

    // Count error types
    size_t colon_pos = error.find(':');
    std::string error_type = (colon_pos != std::string::npos)
        ? error.substr(0, colon_pos)
        : "Unknown";

    stats_.error_counts[error_type]++;

    // Log record context if available
    if (record_context.has_value()) {
        logger_->debug("Error context: record at position {}", current_position_);
    }
}

void ExtractorBase::updateProgress(size_t current, size_t total) {
    if (progress_callback_) {
        progress_callback_(current, total);
    }

    // Log progress periodically
    if (current % 10000 == 0 || current == total) {
        if (total > 0) {
            double percent = (static_cast<double>(current) / total) * 100.0;
            logger_->info("Extraction progress: {}/{} ({:.1f}%)", current, total, percent);
        } else {
            logger_->info("Extraction progress: {} records", current);
        }
    }
}

bool ExtractorBase::applyFilter(const core::Record& record) {
    if (options_.filter_expression.empty()) {
        return true;
    }
    
    // Basic filter implementation using simple field comparisons
    // Format: "field_name=value" or "field_name!=value" or "field_name>value" etc.
    std::string expr = options_.filter_expression;
    
    // Find operator
    std::string operators[] = {"!=", "<=", ">=", "=", "<", ">"};
    std::string op;
    size_t op_pos = std::string::npos;
    
    for (const auto& test_op : operators) {
        size_t pos = expr.find(test_op);
        if (pos != std::string::npos) {
            op = test_op;
            op_pos = pos;
            break;
        }
    }
    
    if (op_pos == std::string::npos) {
        logger_->warn("Invalid filter expression: {}", expr);
        return true; // Pass on invalid filter
    }
    
    std::string field_name = expr.substr(0, op_pos);
    std::string value = expr.substr(op_pos + op.length());
    
    // Trim whitespace
    field_name.erase(0, field_name.find_first_not_of(" \t"));
    field_name.erase(field_name.find_last_not_of(" \t") + 1);
    value.erase(0, value.find_first_not_of(" \t"));
    value.erase(value.find_last_not_of(" \t") + 1);
    
    if (!record.hasField(field_name)) {
        return false; // Field not found, fail filter
    }
    
    try {
        auto field_value = record.getField(field_name);
        std::string field_str;
        
        // Convert field value to string for comparison
        if (field_value.type() == typeid(std::string)) {
            field_str = std::any_cast<std::string>(field_value);
        } else if (field_value.type() == typeid(int)) {
            field_str = std::to_string(std::any_cast<int>(field_value));
        } else if (field_value.type() == typeid(long long)) {
            field_str = std::to_string(std::any_cast<long long>(field_value));
        } else if (field_value.type() == typeid(double)) {
            field_str = std::to_string(std::any_cast<double>(field_value));
        } else if (field_value.type() == typeid(bool)) {
            field_str = std::any_cast<bool>(field_value) ? "true" : "false";
        } else {
            logger_->debug("Unsupported field type for filtering: {}", field_name);
            return true; // Pass on unsupported types
        }
        
        // Apply comparison
        if (op == "=") {
            return field_str == value;
        } else if (op == "!=") {
            return field_str != value;
        } else if (op == "<") {
            return field_str < value;
        } else if (op == ">") {
            return field_str > value;
        } else if (op == "<=") {
            return field_str <= value;
        } else if (op == ">=") {
            return field_str >= value;
        }
        
    } catch (const std::exception& e) {
        logger_->warn("Error applying filter '{}': {}", expr, e.what());
        return true; // Pass on error
    }
    
    return true;
}

core::Record ExtractorBase::selectColumns(const core::Record& record) {
    if (options_.columns.empty()) {
        return record;
    }

    core::Record filtered_record;

    // Copy only selected columns
    for (const auto& column : options_.columns) {
        if (record.hasField(column)) {
            filtered_record.setField(column, record.getField(column));
        }
    }

    // Preserve metadata
    filtered_record.setMetadata(record.getMetadata());

    return filtered_record;
}

// ExtractionStats implementation

void ExtractionStats::calculate_rates() {
    if (extraction_time_seconds > 0) {
        records_per_second = static_cast<double>(successful_records) / extraction_time_seconds;
        bytes_per_second = static_cast<double>(bytes_processed) / extraction_time_seconds;
    }

    // Get peak memory usage and total memory allocated
#ifdef __linux__
    struct rusage usage;
    if (getrusage(RUSAGE_SELF, &usage) == 0) {
        peak_memory_usage = static_cast<size_t>(usage.ru_maxrss * 1024); // Convert KB to bytes
        total_memory_allocated = static_cast<size_t>(usage.ru_inblock * 1024); // Estimate total allocated
    }
#else
    // For non-Linux systems, use a basic estimation
    peak_memory_usage = total_records * 1024; // Rough estimate: 1KB per record
    total_memory_allocated = peak_memory_usage;
#endif
}

} // namespace omop::extract

File src/lib/extract/csv_extractor.h:

#pragma once

#include "core/interfaces.h"
#include "extractor_base.h"
#include <fstream>
#include <sstream>
#include <filesystem>
#include <regex>
#include <string>
#include <optional>
#include <vector>
#include <unordered_map>
#include <any>
#include <chrono>
#include "common/validation.h"

namespace omop::extract {

/**
 * @brief CSV parsing options
 */
struct CsvOptions {
    char delimiter{','};
    char quote_char{'"'};
    char escape_char{'\\'};
    bool has_header{true};
    std::string encoding{"UTF-8"};
    std::vector<std::string> column_names;
    std::vector<std::string> column_types;
    bool skip_empty_lines{true};
    bool trim_fields{true};
    size_t skip_lines{0};
    std::optional<size_t> max_lines;
    std::string null_string{"NULL"};
    std::string true_string{"TRUE"};
    std::string false_string{"FALSE"};
    std::string date_format{"%Y-%m-%d"};
    std::string datetime_format{"%Y-%m-%d %H:%M:%S"};
};

/**
 * @brief CSV field parser
 *
 * Handles parsing of individual CSV fields with proper quote and escape handling.
 */
class CsvFieldParser {
public:
    /**
     * @brief Constructor
     * @param options CSV parsing options
     */
    explicit CsvFieldParser(const CsvOptions& options) : options_(options) {}

    /**
     * @brief Parse a single field
     * @param input Input string
     * @param pos Current position (updated)
     * @return std::string Parsed field value
     */
    std::string parse_field(const std::string& input, size_t& pos);

    /**
     * @brief Parse a complete line
     * @param line Input line
     * @return std::vector<std::string> Parsed fields
     */
    std::vector<std::string> parse_line(const std::string& line);

    /**
     * @brief Convert field to typed value
     * @param field Field value
     * @param type_hint Type hint (optional)
     * @return std::any Typed value
     */
    std::any convert_field(const std::string& field,
                          const std::string& type_hint = "");

    /**
     * @brief Parse date/time value
     * @param value String value
     * @param format Format string
     * @return std::chrono::system_clock::time_point Parsed time
     */
    std::chrono::system_clock::time_point parse_datetime(
        const std::string& value, const std::string& format) const;

    /**
     * @brief Trim whitespace from string
     * @param str String to trim
     * @return std::string Trimmed string
     */
    std::string trim(const std::string& str) const;

    /**
     * @brief Check if a string is valid UTF-8
     * @param str String to check
     * @return bool True if valid UTF-8, false otherwise
     */
    bool is_valid_utf8(const std::string& str) const;

private:
    CsvOptions options_;
};

/**
 * @brief CSV file extractor
 *
 * Extends ExtractorBase for CSV file sources,
 * providing efficient streaming extraction from CSV files with
 * comprehensive error handling, statistics, and progress tracking.
 */
class CsvExtractor : public ExtractorBase {
public:
    /**
     * @brief Constructor
     */
    CsvExtractor();

    /**
     * @brief Initialize the extractor
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                    core::ProcessingContext& context) override;

    /**
     * @brief Extract next batch of records
     * @param batch_size Maximum number of records to extract
     * @param context Processing context
     * @return core::RecordBatch Extracted records
     */
    core::RecordBatch extract_batch(size_t batch_size,
                                    core::ProcessingContext& context) override;

    /**
     * @brief Check if more data is available
     * @return bool True if more data can be extracted
     */
    bool has_more_data() const override;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "csv"; }

    /**
     * @brief Finalize extraction
     * @param context Processing context
     */
    void finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get extraction statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

    /**
     * @brief Check if a file is a compressed CSV file by extension
     * @param filepath File path
     * @return bool True if file is compressed
     */
    static bool is_compressed_file(const std::string& filepath);

    /**
     * @brief Get the schema of the CSV data source
     * @return SourceSchema CSV schema information
     */
    virtual SourceSchema getSchema() const;

    /**
     * @brief Validate the source CSV file
     * @return omop::common::ValidationResult Validation result
     */
    virtual omop::common::ValidationResult validateSource();

protected:
    // ExtractorBase pure virtual method implementations
    
    /**
     * @brief Connect to the CSV data source
     * @return bool True if connection successful
     */
    bool connect() override;
    
    /**
     * @brief Disconnect from the CSV data source
     */
    void disconnect() override;
    
    /**
     * @brief Extract a single batch of records (implementation)
     * @param batch_size Size of batch to extract
     * @return std::vector<core::Record> Vector of records
     */
    std::vector<core::Record> extractBatchImpl(size_t batch_size) override;
    
    /**
     * @brief Convert source data to Record format
     * @param source_data Source data (vector<string> of CSV fields)
     * @return core::Record Converted record
     */
    core::Record convertToRecord(const std::any& source_data) override;

    // CSV-specific methods
    
    /**
     * @brief Open CSV file
     * @param filepath File path
     */
    void open_file(const std::string& filepath);

    /**
     * @brief Read and parse header
     */
    void read_header();

    /**
     * @brief Infer column types from data
     * @param sample_size Number of rows to sample
     */
    void infer_column_types(size_t sample_size = 100);

    /**
     * @brief Create record from parsed fields
     * @param fields Field values
     * @return core::Record Created record
     */
    core::Record create_record(const std::vector<std::string>& fields);

    /**
     * @brief Read a complete CSV record (handling multi-line quoted fields)
     * @param line Output line containing complete record
     * @return bool True if record was read successfully
     */
    bool read_complete_record(std::string& line);

protected:
    // CSV-specific members (protected so derived classes can access)
    std::ifstream file_stream_;                    ///< CSV file stream
    std::string filepath_;                         ///< Path to CSV file
    CsvOptions options_;                           ///< CSV parsing options
    CsvFieldParser parser_;                        ///< CSV field parser
    std::vector<std::string> column_names_;        ///< Column names from header
    std::vector<std::string> column_types_;        ///< Inferred column types
    size_t current_line_{0};                       ///< Current line number in file
    size_t total_lines_{0};                        ///< Total lines in file (if known)
    
    // Note: The following are now handled by ExtractorBase:
    // - extracted_count_ -> use stats_.successful_records
    // - error_count_ -> use stats_.failed_records  
    // - max_records_ -> use options_.max_records
    // - has_more_ -> use has_more_data_
    // - start_time_ -> use stats_.start_time
};

/**
 * @brief Multi-file CSV extractor
 *
 * Extends CsvExtractor to handle multiple CSV files as a single data source.
 */
class MultiFileCsvExtractor : public CsvExtractor {
public:
    /**
     * @brief Constructor
     */
    MultiFileCsvExtractor() = default;

    /**
     * @brief Initialize with multiple files
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                    core::ProcessingContext& context) override;

    /**
     * @brief Extract a batch of records
     * @param batch_size Maximum number of records to extract
     * @param context Processing context
     * @return RecordBatch Batch of extracted records
     */
    core::RecordBatch extract_batch(size_t batch_size,
                                    core::ProcessingContext& context) override;

    /**
     * @brief Check if more data is available
     * @return bool True if more data can be extracted
     */
    bool has_more_data() const override;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "multi_csv"; }

protected:
    /**
     * @brief Move to next file
     * @return bool True if next file opened successfully
     */
    bool next_file();

    std::vector<std::string> file_paths_;
    size_t current_file_index_{0};
    bool skip_headers_after_first_{true};
};

/**
 * @brief CSV directory extractor
 *
 * Extracts data from all CSV files in a directory with pattern matching.
 */
class CsvDirectoryExtractor : public MultiFileCsvExtractor {
public:
    /**
     * @brief Constructor
     */
    CsvDirectoryExtractor() = default;

    /**
     * @brief Initialize with directory
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                    core::ProcessingContext& context) override;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "csv_directory"; }

protected:
    /**
     * @brief Find CSV files in directory
     * @param directory Directory path
     * @param pattern File name pattern (regex)
     * @param recursive Whether to search recursively
     * @return std::vector<std::string> File paths
     */
    std::vector<std::string> find_csv_files(const std::string& directory,
                                            const std::string& pattern = ".*\\.csv$",
                                            bool recursive = false);

private:
    std::string directory_path_;
    std::regex file_pattern_;
    bool recursive_search_{false};
};

/**
 * @brief Compressed CSV extractor
 *
 * Handles extraction from compressed CSV files (gzip, zip, etc.).
 */
class CompressedCsvExtractor : public CsvExtractor {
public:
    /**
     * @brief Compression format
     */
    enum class CompressionFormat {
        None,
        Gzip,
        Zip,
        Bzip2,
        Xz
    };

    /**
     * @brief Constructor
     */
    CompressedCsvExtractor() = default;

    /**
     * @brief Initialize with compressed file
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                    core::ProcessingContext& context) override;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "compressed_csv"; }

    /**
     * @brief Finalize extraction and cleanup
     * @param context Processing context
     */
    void finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get extraction statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

    /**
     * @brief Detect compression format
     * @param filepath File path
     * @return CompressionFormat Detected format
     */
    static CompressionFormat detect_compression(const std::string& filepath);

protected:

    /**
     * @brief Decompress file
     * @param filepath Compressed file path
     * @param format Compression format
     * @return std::string Path to decompressed file
     */
    std::string decompress_file(const std::string& filepath,
                               CompressionFormat format);

    /**
     * @brief Convert compression format to string
     * @param format Compression format
     * @return std::string String representation
     */
    std::string format_to_string(CompressionFormat format) const;

    /**
     * @brief Convert string to compression format
     * @param format_str String representation
     * @return CompressionFormat Compression format
     */
    CompressionFormat string_to_format(const std::string& format_str);

private:
    CompressionFormat compression_format_{CompressionFormat::None};
    std::string temp_file_path_;
    bool cleanup_temp_file_{true};
};

/**
 * @brief CSV extractor factory
 */
class CsvExtractorFactory {
public:
    /**
     * @brief Create CSV extractor
     * @param type Extractor type (csv, multi_csv, csv_directory, compressed_csv)
     * @return std::unique_ptr<core::IExtractor> Extractor instance
     */
    static std::unique_ptr<core::IExtractor> create(const std::string& type);

    /**
     * @brief Register CSV extractors with the main factory
     */
    static void register_extractors();
};

// Implementation helpers

inline std::string CsvFieldParser::trim(const std::string& str) const {
    if (!options_.trim_fields) return str;

    size_t start = str.find_first_not_of(" \t\r\n");
    if (start == std::string::npos) return "";

    size_t end = str.find_last_not_of(" \t\r\n");
    return str.substr(start, end - start + 1);
}

inline std::string CsvFieldParser::parse_field(const std::string& input, size_t& pos) {
    std::string field;
    bool in_quotes = false;
    bool was_quoted = false;  // Track if field was originally quoted
    bool escape_next = false;

    // Check if we have leading whitespace before potential quote
    size_t leading_spaces = 0;
    size_t check_pos = pos;
    while (check_pos < input.length() && std::isspace(input[check_pos]) && input[check_pos] != '\n') {
        leading_spaces++;
        check_pos++;
    }

    // Check if field starts with quote (after any leading whitespace)
    if (check_pos < input.length() && input[check_pos] == options_.quote_char) {
        // This is a quoted field - skip leading whitespace and start after quote
        pos = check_pos + 1;
        in_quotes = true;
        was_quoted = true;
    }
    // If not quoted, start from original position (including any leading whitespace)

    while (pos < input.length()) {
        char c = input[pos];

        if (escape_next) {
            // Handle escape sequences
            switch (c) {
                case 't': field += '\t'; break;
                case 'n': field += '\n'; break;
                case 'r': field += '\r'; break;
                case '\\': field += '\\'; break;
                case '"': field += '"'; break;
                default: field += c; break;
            }
            escape_next = false;
            ++pos;
            continue;
        }

        if (c == options_.escape_char) {
            escape_next = true;
            ++pos;
            continue;
        }

        if (in_quotes) {
            if (c == options_.quote_char) {
                // Check for escaped quote (double quote)
                if (pos + 1 < input.length() && input[pos + 1] == options_.quote_char) {
                    field += c;
                    pos += 2;
                    continue;
                }
                in_quotes = false;
                ++pos;
                // Skip to delimiter or end of line
                while (pos < input.length() &&
                       input[pos] != options_.delimiter &&
                       input[pos] != '\n') {
                    ++pos;
                }
                break;
            }
            field += c;
            ++pos;
        } else {
            if (c == options_.delimiter || c == '\n') {
                break;
            }
            field += c;
            ++pos;
        }
    }

    // Skip delimiter if present
    if (pos < input.length() && input[pos] == options_.delimiter) {
        ++pos;
    }
    
    // Skip newline if present (to handle lines ending with \n)
    if (pos < input.length() && input[pos] == '\n') {
        ++pos;
    }

    // Validate UTF-8 encoding if specified
    if (options_.encoding == "UTF-8" && !field.empty() && !is_valid_utf8(field)) {
        auto logger = common::Logger::get("omop-csv-parser");
        logger->warn("Invalid UTF-8 sequence detected in field, attempting to sanitize");

        // Basic sanitization - replace invalid bytes with ?
        for (size_t i = 0; i < field.length(); ++i) {
            if (static_cast<unsigned char>(field[i]) > 0x7F && !is_valid_utf8(field.substr(i, 1))) {
                field[i] = '?';
            }
        }
    }

    // Only trim unquoted fields if trim_fields is enabled
    // Use was_quoted to preserve whitespace in originally quoted fields
    if (was_quoted) {
        // Preserve whitespace in quoted fields
        return field;
    } else if (options_.trim_fields) {
        return trim(field);
    }
    
    return field;
}

inline bool CsvFieldParser::is_valid_utf8(const std::string& str) const {
    const unsigned char* bytes = reinterpret_cast<const unsigned char*>(str.c_str());
    const unsigned char* end = bytes + str.length();

    while (bytes < end) {
        if (*bytes <= 0x7F) {
            // ASCII
            bytes++;
        } else if ((*bytes & 0xE0) == 0xC0) {
            // 2-byte sequence
            if (bytes + 1 >= end || (bytes[1] & 0xC0) != 0x80) return false;
            bytes += 2;
        } else if ((*bytes & 0xF0) == 0xE0) {
            // 3-byte sequence
            if (bytes + 2 >= end || (bytes[1] & 0xC0) != 0x80 ||
                (bytes[2] & 0xC0) != 0x80) return false;
            bytes += 3;
        } else if ((*bytes & 0xF8) == 0xF0) {
            // 4-byte sequence
            if (bytes + 3 >= end || (bytes[1] & 0xC0) != 0x80 ||
                (bytes[2] & 0xC0) != 0x80 || (bytes[3] & 0xC0) != 0x80) return false;
            bytes += 4;
        } else {
            return false;
        }
    }
    return true;
}

} // namespace omop::extract

File src/lib/extract/mysql_connector.h:

/**
 * @file mysql_connector.h
 * @brief MySQL database connector interface
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This file provides the MySQL implementation of the database connector
 * interface, supporting MySQL and MariaDB databases.
 */

#pragma once

#ifdef OMOP_HAS_MYSQL

#include "extract/database_connector.h"
#include <mysql.h>
#include <memory>
#include <mutex>
#include <vector>

namespace omop::extract {

/**
 * @brief RAII wrapper for MySQL statement
 */
class MySQLStatement {
public:
    /**
     * @brief Constructor
     * @param mysql MySQL connection handle
     */
    explicit MySQLStatement(MYSQL* mysql) {
        stmt_ = mysql_stmt_init(mysql);
        if (!stmt_) {
            throw common::DatabaseException("Failed to initialize MySQL statement", "MySQL", 0);
        }
    }

    /**
     * @brief Destructor
     */
    ~MySQLStatement() {
        if (stmt_) {
            mysql_stmt_close(stmt_);
        }
    }

    // Delete copy operations
    MySQLStatement(const MySQLStatement&) = delete;
    MySQLStatement& operator=(const MySQLStatement&) = delete;

    /**
     * @brief Get raw statement handle
     * @return MYSQL_STMT* Statement handle
     */
    MYSQL_STMT* get() { return stmt_; }

private:
    MYSQL_STMT* stmt_;
};

/**
 * @brief MySQL result set implementation
 *
 * This class provides access to MySQL query results through the
 * IResultSet interface, handling type conversions and NULL values.
 */
class MySQLResultSet : public ResultSetBase {
public:
    /**
     * @brief Constructor
     * @param statement MySQL statement
     */
    explicit MySQLResultSet(std::shared_ptr<MySQLStatement> statement);

    /**
     * @brief Destructor
     */
    ~MySQLResultSet() override;

    /**
     * @brief Move to next row
     * @return bool True if successful
     */
    bool next() override;

    /**
     * @brief Get column value by index
     * @param index Column index (0-based)
     * @return std::any Column value
     */
    std::any get_value(size_t index) const override;

    /**
     * @brief Get column value by name
     * @param column_name Column name
     * @return std::any Column value
     */
    std::any get_value(const std::string& column_name) const override;

    /**
     * @brief Check if column value is NULL
     * @param index Column index
     * @return bool True if NULL
     */
    bool is_null(size_t index) const override;

    /**
     * @brief Check if column value is NULL
     * @param column_name Column name
     * @return bool True if NULL
     */
    bool is_null(const std::string& column_name) const override;

    /**
     * @brief Get column count
     * @return size_t Number of columns
     */
    size_t column_count() const override;

    /**
     * @brief Get column name by index
     * @param index Column index
     * @return std::string Column name
     */
    std::string column_name(size_t index) const override;

    /**
     * @brief Get column type by index
     * @param index Column index
     * @return std::string Column type name
     */
    std::string column_type(size_t index) const override;

private:
    /**
     * @brief Column metadata
     */
    struct ColumnInfo {
        std::string name;
        enum_field_types type;
        unsigned long length;
        unsigned int flags;
        unsigned int decimals;
    };

    /**
     * @brief MySQL bind buffer
     */
    struct BindBuffer {
        enum_field_types buffer_type;
        void* buffer;
        unsigned long buffer_length;
        bool is_null_value;
        unsigned long length_value;
        bool error_value;
        bool* is_null;
        unsigned long* length;
        bool* error;
    };

    /**
     * @brief Get column index by name
     * @param column_name Column name
     * @return size_t Column index
     * @throws DatabaseException if column not found
     */
    size_t get_column_index(const std::string& column_name) const;

    /**
     * @brief Convert MySQL value to appropriate type
     * @param index Column index
     * @return std::any Converted value
     */
    std::any convert_value(size_t index) const;

    /**
     * @brief Load column metadata
     */
    void load_metadata();

    std::shared_ptr<MySQLStatement> statement_;
    MYSQL_RES* result_;
    std::vector<ColumnInfo> columns_;
    std::vector<MYSQL_BIND> bind_buffers_;
    mutable std::unordered_map<std::string, size_t> column_index_cache_;
    int current_row_{-1};
    my_ulonglong row_count_{0};
    unsigned int column_count_{0};
};

/**
 * @brief MySQL prepared statement implementation
 *
 * This class implements prepared statements for MySQL,
 * providing parameterized query execution.
 */
class MySQLPreparedStatement : public IPreparedStatement {
public:
    /**
     * @brief Constructor
     * @param mysql MySQL connection
     * @param sql SQL query
     */
    MySQLPreparedStatement(MYSQL* mysql, const std::string& sql);

    /**
     * @brief Destructor
     */
    ~MySQLPreparedStatement() override;

    /**
     * @brief Bind parameter by index
     * @param index Parameter index (1-based)
     * @param value Parameter value
     */
    void bind(size_t index, const std::any& value) override;

    /**
     * @brief Execute query and return result set
     * @return std::unique_ptr<IResultSet> Query results
     */
    std::unique_ptr<IResultSet> execute_query() override;

    /**
     * @brief Execute update/insert/delete
     * @return size_t Number of affected rows
     */
    size_t execute_update() override;

    /**
     * @brief Clear all bound parameters
     */
    void clear_parameters() override;

private:
    /**
     * @brief Parameter binding information
     */
    struct ParameterBinding {
        std::any value;
        MYSQL_BIND bind;
        std::vector<char> buffer;
        bool is_null;
        unsigned long length;
    };

    /**
     * @brief Bind all parameters
     */
    void bind_parameters();

    /**
     * @brief Set up parameter binding
     * @param binding Parameter binding
     * @param value Parameter value
     */
    void setup_parameter_binding(ParameterBinding& binding, const std::any& value);

    std::shared_ptr<MySQLStatement> statement_;
    std::string sql_;
    std::vector<ParameterBinding> parameters_;
    size_t param_count_{0};
};

/**
 * @brief MySQL database connection implementation
 *
 * This class provides the concrete implementation of IDatabaseConnection
 * for MySQL databases, using the MySQL C API.
 */
class MySQLConnection : public IDatabaseConnection {
public:
    /**
     * @brief Constructor
     */
    MySQLConnection();

    /**
     * @brief Destructor
     */
    ~MySQLConnection() override;

    /**
     * @brief Connect to database
     * @param params Connection parameters
     */
    void connect(const ConnectionParams& params) override;

    /**
     * @brief Disconnect from database
     */
    void disconnect() override;

    /**
     * @brief Check if connected
     * @return bool True if connected
     */
    bool is_connected() const override;

    /**
     * @brief Execute SQL query
     * @param sql SQL query string
     * @return std::unique_ptr<IResultSet> Query results
     */
    std::unique_ptr<IResultSet> execute_query(const std::string& sql) override;

    /**
     * @brief Execute SQL update/insert/delete
     * @param sql SQL statement
     * @return size_t Number of affected rows
     */
    size_t execute_update(const std::string& sql) override;

    /**
     * @brief Prepare SQL statement
     * @param sql SQL statement with parameter placeholders
     * @return std::unique_ptr<IPreparedStatement> Prepared statement
     */
    std::unique_ptr<IPreparedStatement> prepare_statement(const std::string& sql) override;

    /**
     * @brief Begin transaction
     */
    void begin_transaction() override;

    /**
     * @brief Commit transaction
     */
    void commit() override;

    /**
     * @brief Rollback transaction
     */
    void rollback() override;

    /**
     * @brief Get database type name
     * @return std::string Database type
     */
    std::string get_database_type() const override { return "MySQL"; }

    /**
     * @brief Get database version
     * @return std::string Database version string
     */
    std::string get_version() const override;

    /**
     * @brief Set query timeout
     * @param seconds Timeout in seconds
     */
    void set_query_timeout(int seconds) override;

    /**
     * @brief Check if table exists
     * @param table_name Table name
     * @param schema Schema name (optional)
     * @return bool True if table exists
     */
    bool table_exists(const std::string& table_name,
                     const std::string& schema = "") const override;

    /**
     * @brief Get raw MySQL connection handle
     * @return MYSQL* Connection handle (for internal use)
     */
    MYSQL* get_raw_connection() { return mysql_; }

    bool in_transaction() const override { return in_transaction_; }

private:
    /**
     * @brief Check for MySQL errors and throw if needed
     * @param operation Operation description
     */
    void check_error(const std::string& operation) const;

    /**
     * @brief Set connection options from parameters
     * @param params Connection parameters
     */
    void set_connection_options(const ConnectionParams& params);

    MYSQL* mysql_;
    bool connected_{false};
    bool in_transaction_{false};
    mutable std::mutex connection_mutex_;
    int query_timeout_{0};
};

/**
 * @brief MySQL-specific database extractor
 *
 * This class extends DatabaseExtractor with MySQL-specific optimizations.
 */
class MySQLExtractor : public DatabaseExtractor {
public:
    /**
     * @brief Constructor
     * @param connection MySQL connection
     */
    explicit MySQLExtractor(std::unique_ptr<IDatabaseConnection> connection)
        : DatabaseExtractor(std::move(connection)) {}

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "mysql"; }

protected:
    /**
     * @brief Build extraction query with MySQL-specific optimizations
     * @return std::string SQL query
     */
    std::string build_query() const override;
};

/**
 * @brief Registration helper for MySQL components
 */
class MySQLRegistrar {
public:
    /**
     * @brief Register all MySQL components
     */
    static void register_components() {
        DatabaseConnectionFactory::instance().register_type(
            "mysql",
            [](const IDatabaseConnection::ConnectionParams& params) {
                auto conn = std::make_unique<MySQLConnection>();
                conn->connect(params);
                return conn;
            }
        );

        DatabaseConnectionFactory::instance().register_type(
            "mariadb",  // Alias for MariaDB
            [](const IDatabaseConnection::ConnectionParams& params) {
                auto conn = std::make_unique<MySQLConnection>();
                conn->connect(params);
                return conn;
            }
        );
    }

private:
    MySQLRegistrar() = default;
};

} // namespace omop::extract

#endif // OMOP_HAS_MYSQL

File src/lib/extract/postgresql_connector.cpp:

/**
 * @file postgresql_connector.cpp
 * @brief PostgreSQL database connector implementation
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "postgresql_connector.h"
#include "common/logging.h"
#include "common/exceptions.h"
#include <format>
#include <chrono>
#include <ctime>
#include <sstream>
#include <iomanip>

namespace {
    // PostgreSQL type OIDs
    constexpr Oid BOOLOID = 16;
    constexpr Oid INT2OID = 21;
    constexpr Oid INT4OID = 23;
    constexpr Oid INT8OID = 20;
    constexpr Oid FLOAT4OID = 700;
    constexpr Oid FLOAT8OID = 701;
    constexpr Oid TEXTOID = 25;
    constexpr Oid VARCHAROID = 1043;
    constexpr Oid DATEOID = 1082;
    constexpr Oid TIMESTAMPOID = 1114;
    constexpr Oid TIMESTAMPTZOID = 1184;
}

namespace omop::extract {

// PostgreSQL type names
static const std::unordered_map<Oid, std::string> PG_TYPE_NAMES = {
    {16, "boolean"},
    {20, "bigint"},
    {21, "smallint"},
    {23, "integer"},
    {25, "text"},
    {700, "real"},
    {701, "double precision"},
    {1043, "varchar"},
    {1082, "date"},
    {1114, "timestamp"},
    {1184, "timestamptz"}
};

// PostgreSQLResultSet implementation

PostgreSQLResultSet::PostgreSQLResultSet(PGresult* result)
    : result_(result),
      row_count_(PQntuples(result)),
      current_row_(-1),
      column_count_(PQnfields(result)) {

    if (!result_) {
        throw common::DatabaseException("Invalid PostgreSQL result set", "PostgreSQL", 0);
    }
}

PostgreSQLResultSet::~PostgreSQLResultSet() {
    if (result_) {
        PQclear(result_);
    }
}

bool PostgreSQLResultSet::next() {
    if (current_row_ + 1 < row_count_) {
        current_row_++;
        return true;
    }
    return false;
}

std::any PostgreSQLResultSet::get_value(size_t index) const {
    if (index >= static_cast<size_t>(column_count_)) {
        throw common::DatabaseException(
            std::format("Column index {} out of range (0-{})", index, column_count_ - 1),
            "PostgreSQL", 0);
    }

    if (current_row_ < 0 || current_row_ >= row_count_) {
        throw common::DatabaseException("No current row", "PostgreSQL", 0);
    }

    // Bounds check for safety
    if (index >= static_cast<size_t>(PQnfields(result_))) {
        throw common::DatabaseException(
            std::format("Column index {} out of bounds", index), "PostgreSQL", 0);
    }

    if (PQgetisnull(result_, current_row_, static_cast<int>(index))) {
        return std::any{};
    }

    const char* value = PQgetvalue(result_, current_row_, static_cast<int>(index));
    Oid type_oid = PQftype(result_, static_cast<int>(index));

    return convert_value(value, type_oid);
}

std::any PostgreSQLResultSet::get_value(const std::string& column_name) const {
    size_t index = get_column_index(column_name);
    return get_value(index);
}

bool PostgreSQLResultSet::is_null(size_t index) const {
    if (index >= static_cast<size_t>(column_count_)) {
        throw common::DatabaseException(
            std::format("Column index {} out of range", index),
            "PostgreSQL", 0);
    }

    if (current_row_ < 0 || current_row_ >= row_count_) {
        throw common::DatabaseException("No current row", "PostgreSQL", 0);
    }

    return PQgetisnull(result_, current_row_, static_cast<int>(index)) == 1;
}

bool PostgreSQLResultSet::is_null(const std::string& column_name) const {
    size_t index = get_column_index(column_name);
    return is_null(index);
}

size_t PostgreSQLResultSet::column_count() const {
    return static_cast<size_t>(column_count_);
}

std::string PostgreSQLResultSet::column_name(size_t index) const {
    if (index >= static_cast<size_t>(column_count_)) {
        throw common::DatabaseException(
            std::format("Column index {} out of range", index),
            "PostgreSQL", 0);
    }

    return PQfname(result_, static_cast<int>(index));
}

std::string PostgreSQLResultSet::column_type(size_t index) const {
    if (index >= static_cast<size_t>(column_count_)) {
        throw common::DatabaseException(
            std::format("Column index {} out of range", index),
            "PostgreSQL", 0);
    }

    Oid type_oid = PQftype(result_, static_cast<int>(index));
    auto it = PG_TYPE_NAMES.find(type_oid);

    if (it != PG_TYPE_NAMES.end()) {
        return it->second;
    }

    return std::format("oid:{}", type_oid);
}

size_t PostgreSQLResultSet::get_column_index(const std::string& column_name) const {
    // Check cache first
    auto it = column_index_cache_.find(column_name);
    if (it != column_index_cache_.end()) {
        return it->second;
    }

    // Search for column
    int index = PQfnumber(result_, column_name.c_str());
    if (index < 0) {
        throw common::DatabaseException(
            std::format("Column '{}' not found", column_name),
            "PostgreSQL", 0);
    }

    // Cache the result
    column_index_cache_[column_name] = static_cast<size_t>(index);
    return static_cast<size_t>(index);
}

std::any PostgreSQLResultSet::convert_value(const char* value, Oid oid) const {
    if (!value) {
        return std::any{};
    }

    std::string str_value(value);

    switch (oid) {
        case 16:  // boolean
            return str_value == "t" || str_value == "true" || str_value == "1";

        case 20:  // bigint
            return std::stoll(str_value);

        case 21:  // smallint
        case 23:  // integer
            return std::stoi(str_value);

        case 700: // real
        case 701: // double precision
            return std::stod(str_value);

        case 1082: // date
        case 1114: // timestamp
        case 1184: // timestamptz
        {
            // Parse PostgreSQL timestamp format
            std::tm tm = {};
            std::stringstream ss(str_value);
            ss >> std::get_time(&tm, "%Y-%m-%d %H:%M:%S");

            if (ss.fail()) {
                // Try date-only format
                ss.clear();
                ss.str(str_value);
                ss >> std::get_time(&tm, "%Y-%m-%d");
            }

            if (!ss.fail()) {
                return std::chrono::system_clock::from_time_t(std::mktime(&tm));
            }

            // Return as string if parsing fails
            return str_value;
        }

        default:
            // Default to string
            return str_value;
    }
}

// PostgreSQLPreparedStatement implementation

PostgreSQLPreparedStatement::PostgreSQLPreparedStatement(PGconn* connection,
                                                       const std::string& statement_name,
                                                       const std::string& sql)
    : connection_(connection),
      statement_name_(statement_name),
      sql_(sql) {

    if (!connection_) {
        throw common::DatabaseException("Invalid connection", "PostgreSQL", 0);
    }

    // Prepare the statement
    PGresult* result = PQprepare(connection_, statement_name_.c_str(), sql_.c_str(), 0, nullptr);

    if (PQresultStatus(result) != PGRES_COMMAND_OK) {
        std::string error = PQerrorMessage(connection_);
        PQclear(result);
        throw common::DatabaseException(
            std::format("Failed to prepare statement: {}", error),
            "PostgreSQL", 0);
    }

    PQclear(result);
}

PostgreSQLPreparedStatement::~PostgreSQLPreparedStatement() {
    // Deallocate prepared statement
    std::string dealloc_sql = std::format("DEALLOCATE {}", statement_name_);
    PGresult* result = PQexec(connection_, dealloc_sql.c_str());
    if (result) {
        PQclear(result);
    }
}

void PostgreSQLPreparedStatement::bind(size_t index, const std::any& value) {
    if (index == 0) {
        throw common::DatabaseException("Parameter index must be 1-based", "PostgreSQL", 0);
    }

    // Ensure parameters vector is large enough
    if (parameters_.size() < index) {
        parameters_.resize(index);
    }

    // Convert value to string
    parameters_[index - 1] = convert_parameter(value);
}

std::unique_ptr<IResultSet> PostgreSQLPreparedStatement::execute_query() {
    // Prepare parameter arrays
    param_values_.clear();
    param_lengths_.clear();
    param_formats_.clear();

    for (const auto& param : parameters_) {
        param_values_.push_back(param.c_str());
        param_lengths_.push_back(static_cast<int>(param.length()));
        param_formats_.push_back(0); // Text format
    }

    PGresult* result = PQexecPrepared(connection_,
                                     statement_name_.c_str(),
                                     static_cast<int>(parameters_.size()),
                                     param_values_.data(),
                                     param_lengths_.data(),
                                     param_formats_.data(),
                                     0); // Text result format

    if (PQresultStatus(result) != PGRES_TUPLES_OK) {
        std::string error = PQerrorMessage(connection_);
        PQclear(result);
        throw common::DatabaseException(
            std::format("Query execution failed: {}", error),
            "PostgreSQL", 0);
    }

    return std::make_unique<PostgreSQLResultSet>(result);
}

size_t PostgreSQLPreparedStatement::execute_update() {
    // Prepare parameter arrays
    param_values_.clear();
    param_lengths_.clear();
    param_formats_.clear();

    for (const auto& param : parameters_) {
        param_values_.push_back(param.c_str());
        param_lengths_.push_back(static_cast<int>(param.length()));
        param_formats_.push_back(0); // Text format
    }

    PGresult* result = PQexecPrepared(connection_,
                                     statement_name_.c_str(),
                                     static_cast<int>(parameters_.size()),
                                     param_values_.data(),
                                     param_lengths_.data(),
                                     param_formats_.data(),
                                     0); // Text result format

    if (PQresultStatus(result) != PGRES_COMMAND_OK) {
        std::string error = PQerrorMessage(connection_);
        PQclear(result);
        throw common::DatabaseException(
            std::format("Update execution failed: {}", error),
            "PostgreSQL", 0);
    }

    // Get affected rows
    const char* affected = PQcmdTuples(result);
    size_t rows_affected = 0;
    if (affected && strlen(affected) > 0) {
        try {
            rows_affected = std::stoull(affected);
        } catch (const std::exception&) {
            // Some commands like CREATE SCHEMA don't return row counts
            // Just return 0 for affected rows
            rows_affected = 0;
        }
    }

    PQclear(result);
    return rows_affected;
}

void PostgreSQLPreparedStatement::clear_parameters() {
    parameters_.clear();
}

std::string PostgreSQLPreparedStatement::convert_parameter(const std::any& value) const {
    if (!value.has_value()) {
        return "NULL";
    }

    if (value.type() == typeid(bool)) {
        return std::any_cast<bool>(value) ? "true" : "false";
    }
    else if (value.type() == typeid(int)) {
        return std::to_string(std::any_cast<int>(value));
    }
    else if (value.type() == typeid(long)) {
        return std::to_string(std::any_cast<long>(value));
    }
    else if (value.type() == typeid(long long)) {
        return std::to_string(std::any_cast<long long>(value));
    }
    else if (value.type() == typeid(double)) {
        return std::to_string(std::any_cast<double>(value));
    }
    else if (value.type() == typeid(std::string)) {
        return std::any_cast<std::string>(value);
    }
    else if (value.type() == typeid(const char*)) {
        return std::string(std::any_cast<const char*>(value));
    }
    else if (value.type() == typeid(std::chrono::system_clock::time_point)) {
        auto tp = std::any_cast<std::chrono::system_clock::time_point>(value);
        auto time_t = std::chrono::system_clock::to_time_t(tp);
        std::tm tm = *std::localtime(&time_t);
        std::stringstream ss;
        ss << std::put_time(&tm, "%Y-%m-%d %H:%M:%S");
        return ss.str();
    }
    else {
        throw common::DatabaseException(
            "Unsupported parameter type for PostgreSQL", "PostgreSQL", 0);
    }
}

// PostgreSQLConnection implementation

PostgreSQLConnection::PostgreSQLConnection()
    : connection_(nullptr),
      in_transaction_(false),
      query_timeout_(0),
      statement_counter_(0) {
}

PostgreSQLConnection::~PostgreSQLConnection() {
    disconnect();
}

void PostgreSQLConnection::connect(const ConnectionParams& params) {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (connection_) {
        disconnect();
    }

    // Use retry policy for connection
    ConnectionRetryPolicy retry_policy;
    auto logger = common::Logger::get("omop-postgresql");

    bool connected = retry_policy.execute_with_retry([this, &params]() {
        std::string conn_string = build_connection_string(params);
        connection_ = PQconnectdb(conn_string.c_str());

        if (PQstatus(connection_) != CONNECTION_OK) {
            std::string error = PQerrorMessage(connection_);
            PQfinish(connection_);
            connection_ = nullptr;
            throw common::DatabaseException(
                std::format("Failed to connect to PostgreSQL: {}", error),
                "PostgreSQL", 0);
        }
    }, logger);

    if (!connected) {
        throw common::DatabaseException("Failed to connect after all retry attempts",
                                      "PostgreSQL", 0);
    }

    // Set client encoding to UTF8
    if (PQsetClientEncoding(connection_, "UTF8") != 0) {
        auto logger = common::Logger::get("omop-postgresql");
        logger->warn("Failed to set client encoding to UTF8");
    }

    // Apply query timeout if specified
    if (query_timeout_ > 0) {
        set_query_timeout(query_timeout_);
    }
}

void PostgreSQLConnection::disconnect() {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (connection_) {
        PQfinish(connection_);
        connection_ = nullptr;
        in_transaction_ = false;
    }
}

bool PostgreSQLConnection::is_connected() const {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (!connection_) {
        return false;
    }

    return PQstatus(connection_) == CONNECTION_OK;
}

std::unique_ptr<IResultSet> PostgreSQLConnection::execute_query(const std::string& sql) {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (!connection_) {
        throw common::DatabaseException("Not connected to database", "PostgreSQL", 0);
    }

    PGresult* result = PQexec(connection_, sql.c_str());
    check_error(result, "execute_query");

    if (PQresultStatus(result) != PGRES_TUPLES_OK) {
        std::string error = PQerrorMessage(connection_);
        PQclear(result);
        throw common::DatabaseException(
            std::format("Query execution failed: {}", error),
            "PostgreSQL", 0);
    }

    return std::make_unique<PostgreSQLResultSet>(result);
}

size_t PostgreSQLConnection::execute_update(const std::string& sql) {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (!connection_) {
        throw common::DatabaseException("Not connected to database", "PostgreSQL", 0);
    }

    PGresult* result = PQexec(connection_, sql.c_str());
    check_error(result, "execute_update");

    if (PQresultStatus(result) != PGRES_COMMAND_OK) {
        std::string error = PQerrorMessage(connection_);
        PQclear(result);
        throw common::DatabaseException(
            std::format("Update execution failed: {}", error),
            "PostgreSQL", 0);
    }

    const char* affected = PQcmdTuples(result);
    size_t rows_affected = 0;
    if (affected && strlen(affected) > 0) {
        try {
            rows_affected = std::stoull(affected);
        } catch (const std::exception&) {
            // Some commands like CREATE SCHEMA don't return row counts
            // Just return 0 for affected rows
            rows_affected = 0;
        }
    }

    PQclear(result);
    return rows_affected;
}

std::unique_ptr<IPreparedStatement> PostgreSQLConnection::prepare_statement(const std::string& sql) {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (!connection_) {
        throw common::DatabaseException("Not connected to database", "PostgreSQL", 0);
    }

    std::string statement_name = generate_statement_name();
    return std::make_unique<PostgreSQLPreparedStatement>(connection_, statement_name, sql);
}

void PostgreSQLConnection::begin_transaction() {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (in_transaction_) {
        throw common::DatabaseException("Transaction already in progress", "PostgreSQL", 0);
    }

    execute_update("BEGIN");
    in_transaction_ = true;
}

void PostgreSQLConnection::commit() {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (!in_transaction_) {
        throw common::DatabaseException("No transaction in progress", "PostgreSQL", 0);
    }

    execute_update("COMMIT");
    in_transaction_ = false;
}

void PostgreSQLConnection::rollback() {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (!in_transaction_) {
        throw common::DatabaseException("No transaction in progress", "PostgreSQL", 0);
    }

    execute_update("ROLLBACK");
    in_transaction_ = false;
}

std::string PostgreSQLConnection::get_version() const {
    auto result = const_cast<PostgreSQLConnection*>(this)->execute_query("SELECT version()");
    if (result->next()) {
        auto version_any = result->get_value(0);
        if (version_any.has_value() && version_any.type() == typeid(std::string)) {
            return std::any_cast<std::string>(version_any);
        }
    }
    return "Unknown";
}

void PostgreSQLConnection::set_query_timeout(int seconds) {
    query_timeout_ = seconds;

    if (connection_) {
        std::string timeout_sql = std::format("SET statement_timeout = {}", seconds * 1000);
        execute_update(timeout_sql);
    }
}

bool PostgreSQLConnection::table_exists(const std::string& table_name,
                                       const std::string& schema) const {
    std::string query;
    if (schema.empty()) {
        query = std::format(
            "SELECT EXISTS (SELECT 1 FROM information_schema.tables "
            "WHERE table_name = '{}' AND table_schema = current_schema())",
            table_name);
    } else {
        query = std::format(
            "SELECT EXISTS (SELECT 1 FROM information_schema.tables "
            "WHERE table_name = '{}' AND table_schema = '{}')",
            table_name, schema);
    }

    auto result = const_cast<PostgreSQLConnection*>(this)->execute_query(query);
    if (result->next()) {
        auto exists_any = result->get_value(0);
        if (exists_any.has_value() && exists_any.type() == typeid(bool)) {
            return std::any_cast<bool>(exists_any);
        }
    }

    return false;
}

std::string PostgreSQLConnection::build_connection_string(const ConnectionParams& params) const {
    std::stringstream conn_str;

    if (!params.host.empty()) {
        conn_str << "host=" << params.host << " ";
    }

    if (params.port > 0) {
        conn_str << "port=" << params.port << " ";
    }

    if (!params.database.empty()) {
        conn_str << "dbname=" << params.database << " ";
    }

    if (!params.username.empty()) {
        conn_str << "user=" << params.username << " ";
    }

    if (!params.password.empty()) {
        conn_str << "password=" << params.password << " ";
    }

    // Add custom options
    for (const auto& [key, value] : params.options) {
        conn_str << key << "=" << value << " ";
    }

    return conn_str.str();
}

void PostgreSQLConnection::check_error(PGresult* result, const std::string& operation) const {
    if (!result) {
        throw common::DatabaseException(
            std::format("{} failed: NULL result", operation),
            "PostgreSQL", 0);
    }

    ExecStatusType status = PQresultStatus(result);
    if (status == PGRES_BAD_RESPONSE || status == PGRES_FATAL_ERROR) {
        std::string error = PQerrorMessage(connection_);
        PQclear(result);
        throw common::DatabaseException(
            std::format("{} failed: {}", operation, error),
            "PostgreSQL", 0);
    }
}

std::string PostgreSQLConnection::generate_statement_name() {
    int counter = statement_counter_.fetch_add(1);
    return std::format("stmt_{}", counter);
}

// PostgreSQLExtractor implementation

std::string PostgreSQLExtractor::build_query() const {
    // Use parent implementation and add PostgreSQL-specific optimizations
    std::string query = DatabaseExtractor::build_query();
    return apply_query_hints(query);
}

std::string PostgreSQLExtractor::apply_query_hints(const std::string& query) const {
    // Add PostgreSQL-specific query hints for better performance
    // For large tables, use cursor-based fetching

    // This is a simplified example - in production, you might want to
    // analyze the query and table size to determine optimal hints
    return query;
}

} // namespace omop::extract

File src/lib/extract/extractor_factory.cpp:

/**
 * @file extractor_factory_impl.cpp
 * @brief Implementation of extractor factory and registration
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "extractor_factory.h"
#include "extract.h"
#include "csv_extractor.h"
#include "json_extractor.h"
#include "database_connector.h"
#ifdef OMOP_HAS_POSTGRESQL
#include "postgresql_connector.h"
#endif
#ifdef OMOP_HAS_MYSQL
#include "mysql_connector.h"
#endif
#ifdef OMOP_HAS_ODBC
#include "odbc_connector.h"
#endif
#include "common/logging.h"
#include "common/exceptions.h"
#include "common/configuration.h"
#include "core/interfaces.h"
#include <algorithm>
#include <mutex>
#include <atomic>
#include <unordered_map>
#include <string>
#include <vector>
#include <functional>
#include <memory>
#include <any>
#include <format>

namespace omop::extract {

namespace {
    // Use an atomic flag instead of once_flag so we can reset it
    static std::atomic<bool> init_flag{false};
    static std::mutex init_mutex;
}

// Static member definitions for ExtractorFactoryRegistry
std::unordered_map<std::string,
    std::function<std::unique_ptr<core::IExtractor>()>> ExtractorFactoryRegistry::creators_;
std::mutex ExtractorFactoryRegistry::mutex_;

/**
 * @brief Main extractor factory implementation
 */
class ExtractorFactoryImpl {
public:
    /**
     * @brief Register all built-in extractors
     */
    static void register_all_extractors() {
        auto logger = common::Logger::get("omop-extractor-factory");
        logger->info("Registering built-in extractors");

        // Register CSV extractors
        register_csv_extractors();

        // Register JSON extractors
        register_json_extractors();

        // Register database extractors
        register_database_extractors();

        logger->info("Registered {} extractor types",
                    ExtractorFactoryRegistry::get_registered_types().size());
    }

private:
    /**
     * @brief Register CSV extractor types
     */
    static void register_csv_extractors() {
        ExtractorFactoryRegistry::register_type("csv",
            []() { return std::make_unique<CsvExtractor>(); });

        ExtractorFactoryRegistry::register_type("multi_csv",
            []() { return std::make_unique<MultiFileCsvExtractor>(); });

        ExtractorFactoryRegistry::register_type("csv_directory",
            []() { return std::make_unique<CsvDirectoryExtractor>(); });

        ExtractorFactoryRegistry::register_type("compressed_csv",
            []() { return std::make_unique<CompressedCsvExtractor>(); });
    }

    /**
     * @brief Register JSON extractor types
     */
    static void register_json_extractors() {
        ExtractorFactoryRegistry::register_type("json",
            []() { return std::make_unique<JsonExtractor>(); });

        ExtractorFactoryRegistry::register_type("jsonl",
            []() { return std::make_unique<JsonLinesExtractor>("jsonl"); });

        ExtractorFactoryRegistry::register_type("json_lines",
            []() { return std::make_unique<JsonLinesExtractor>("json_lines"); });

        ExtractorFactoryRegistry::register_type("streaming_json",
            []() { return std::make_unique<StreamingJsonExtractor>(); });
    }

    /**
     * @brief Register database extractor types
     */
    static void register_database_extractors() {
#ifdef OMOP_HAS_POSTGRESQL
        // Register PostgreSQL
        ExtractorFactoryRegistry::register_type("postgresql",
            []() {
                auto conn = std::make_unique<PostgreSQLConnection>();
                return std::make_unique<PostgreSQLExtractor>(std::move(conn), "postgresql");
            });

        ExtractorFactoryRegistry::register_type("postgres",
            []() {
                auto conn = std::make_unique<PostgreSQLConnection>();
                return std::make_unique<PostgreSQLExtractor>(std::move(conn), "postgres");
            });
#endif

#ifdef OMOP_HAS_MYSQL
        // Register MySQL
        ExtractorFactoryRegistry::register_type("mysql",
            []() {
                auto conn = std::make_unique<MySQLConnection>();
                return std::make_unique<MySQLExtractor>(std::move(conn));
            });

        ExtractorFactoryRegistry::register_type("mariadb",
            []() {
                auto conn = std::make_unique<MySQLConnection>();
                return std::make_unique<MySQLExtractor>(std::move(conn));
            });
#endif

#ifdef OMOP_HAS_ODBC
        // Register ODBC
        ExtractorFactoryRegistry::register_type("odbc",
            []() {
                auto conn = std::make_unique<OdbcDatabaseConnection>();
                return std::make_unique<OdbcExtractor>(std::move(conn));
            });
#endif

#ifdef OMOP_HAS_POSTGRESQL
        // Register generic database extractor (defaults to PostgreSQL)
        ExtractorFactoryRegistry::register_type("database",
            []() {
                // Default to PostgreSQL for generic database
                auto conn = std::make_unique<PostgreSQLConnection>();
                return std::make_unique<DatabaseExtractor>(std::move(conn));
            });
#endif
    }
};

// ExtractorFactoryRegistry implementation

void ExtractorFactoryRegistry::register_type(const std::string& type,
                                           std::function<std::unique_ptr<core::IExtractor>()> creator) {
    std::lock_guard<std::mutex> lock(mutex_);
    creators_[type] = std::move(creator);
}

std::unique_ptr<core::IExtractor> ExtractorFactoryRegistry::create(const std::string& type) {
    std::lock_guard<std::mutex> lock(mutex_);

    auto it = creators_.find(type);
    if (it == creators_.end()) {
        throw common::ConfigurationException(
            "Unknown extractor type: '" + type + "'");
    }

    return it->second();
}

std::vector<std::string> ExtractorFactoryRegistry::get_registered_types() {
    std::lock_guard<std::mutex> lock(mutex_);

    std::vector<std::string> types;
    types.reserve(creators_.size());

    for (const auto& [type, _] : creators_) {
        types.push_back(type);
    }

    std::sort(types.begin(), types.end());
    return types;
}

bool ExtractorFactoryRegistry::is_type_registered(const std::string& type) {
    std::lock_guard<std::mutex> lock(mutex_);
    return creators_.find(type) != creators_.end();
}

void ExtractorFactoryRegistry::clear() {
    std::lock_guard<std::mutex> lock(mutex_);
    creators_.clear();
}

void reset_initialize_extractors_flag() {
    // Reset the initialization flag
    init_flag.store(false);
}

void initialize_extractors() {
    // Use double-checked locking pattern for thread safety
    if (!init_flag.load()) {
        std::lock_guard<std::mutex> lock(init_mutex);
        if (!init_flag.load()) {
            ExtractorFactoryImpl::register_all_extractors();
            init_flag.store(true);
        }
    }
}

// Extractor creation helper
std::unique_ptr<core::IExtractor> create_extractor(const std::string& type,
                                                  const std::unordered_map<std::string, std::any>& config) {
    // Ensure extractors are registered
    initialize_extractors();

    // Create extractor
    auto extractor = ExtractorFactoryRegistry::create(type);

    // Initialize with configuration if provided
    if (!config.empty()) {
        core::ProcessingContext context;
        extractor->initialize(config, context);
    }

    return extractor;
}

std::vector<ExtractorTypeInfo> get_extractor_info() {
    return {
        {
            "csv",
            "Extracts data from a single CSV file",
            {"filepath"},
            {"delimiter", "quote_char", "has_header", "column_names", "column_types", "encoding"},
            R"({"filepath": "data.csv", "delimiter": ",", "has_header": true})"
        },
        {
            "multi_csv",
            "Extracts data from multiple CSV files",
            {"files"},
            {"skip_headers_after_first"},
            R"({"files": ["data1.csv", "data2.csv"], "skip_headers_after_first": true})"
        },
        {
            "csv_directory",
            "Extracts data from all CSV files in a directory",
            {"directory"},
            {"pattern", "recursive"},
            R"({"directory": "/data", "pattern": ".*\\.csv$", "recursive": true})"
        },
        {
            "json",
            "Extracts data from a JSON file",
            {"filepath"},
            {"root_path", "flatten_nested", "array_delimiter", "parse_dates"},
            R"({"filepath": "data.json", "root_path": "data.records", "flatten_nested": true})"
        },
        {
            "jsonl",
            "Extracts data from a JSON Lines file",
            {"filepath"},
            {"flatten_nested", "parse_dates"},
            R"({"filepath": "data.jsonl", "flatten_nested": true})"
        },
        {
            "streaming_json",
            "Extracts data from large JSON files using streaming",
            {"filepath"},
            {"root_path", "flatten_nested"},
            R"({"filepath": "large_data.json", "root_path": "records"})"
        },
        {
            "postgresql",
            "Extracts data from PostgreSQL database",
            {"host", "port", "database", "username", "password", "table"},
            {"schema", "columns", "filter", "order_by"},
            R"({"host": "localhost", "port": 5432, "database": "omop", "username": "user", "password": "pass", "table": "person"})"
        },
#ifdef OMOP_HAS_MYSQL
        {
            "mysql",
            "Extracts data from MySQL database",
            {"host", "port", "database", "username", "password", "table"},
            {"schema", "columns", "filter", "order_by"},
            R"({"host": "localhost", "port": 3306, "database": "omop", "username": "user", "password": "pass", "table": "person"})"
        },
#endif
#ifdef OMOP_HAS_ODBC
        {
            "odbc",
            "Extracts data from any ODBC-compliant database",
            {"dsn", "table"},
            {"username", "password", "schema", "columns", "filter", "order_by"},
            R"({"dsn": "MyDataSource", "username": "user", "password": "pass", "table": "person"})"
        }
#endif
    };
}

void print_extractor_info(std::ostream& stream) {
    auto info_list = get_extractor_info();
    
    stream << "Available Extractor Types:\n";
    stream << "========================\n\n";
    
    for (const auto& info : info_list) {
        stream << "Type: " << info.type << "\n";
        stream << "Description: " << info.description << "\n";
        
        if (!info.required_params.empty()) {
            stream << "Required Parameters: ";
            for (size_t i = 0; i < info.required_params.size(); ++i) {
                if (i > 0) stream << ", ";
                stream << info.required_params[i];
            }
            stream << "\n";
        }
        
        if (!info.optional_params.empty()) {
            stream << "Optional Parameters: ";
            for (size_t i = 0; i < info.optional_params.size(); ++i) {
                if (i > 0) stream << ", ";
                stream << info.optional_params[i];
            }
            stream << "\n";
        }
        
        if (!info.example_config.empty()) {
            stream << "Example Config: " << info.example_config << "\n";
        }
        
        stream << "\n";
    }
}

} // namespace omop::extract

File src/lib/extract/extract_utils.h:

/**
 * @file extract_utils.h
 * @brief Utility functions and helper classes for data extraction
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#pragma once

#include "core/interfaces.h"
#include "extractor_factory.h"
#include "csv_extractor.h"
#include "json_extractor.h"
#include "database_connector.h"
#include "common/exceptions.h"
#include "common/logging.h"
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <functional>
#include <mutex>
#include <any>
#include <thread>
#include <condition_variable>
#include <queue>
#include <atomic>

namespace omop::extract {

/**
 * @brief Simplified extractor creation with automatic type detection
 * @param source_path Path to data source (file or connection string)
 * @param config Additional configuration parameters
 * @return std::unique_ptr<core::IExtractor> Configured extractor instance
 * @throws ConfigurationException if source type cannot be determined
 */
std::unique_ptr<core::IExtractor> create_extractor_auto(
    const std::string& source_path,
    const std::unordered_map<std::string, std::any>& config = {});

/**
 * @brief Extract data source type from path or configuration
 * @param source_path Path to data source
 * @return std::string Extractor type identifier
 */
std::string detect_source_type(const std::string& source_path);

/**
 * @brief Validate extractor configuration
 * @param type Extractor type
 * @param config Configuration parameters
 * @return std::pair<bool, std::string> Validation result and error message
 */
std::pair<bool, std::string> validate_extractor_config(
    const std::string& type,
    const std::unordered_map<std::string, std::any>& config);

/**
 * @brief Convenience class for batch extraction operations
 *
 * This class provides high-level functionality for extracting data
 * from various sources with automatic error handling and progress tracking.
 */
class BatchExtractor {
public:
    /**
     * @brief Configuration for batch extraction
     */
    struct Config {
        size_t batch_size = 10000;
        size_t max_records = 0;  // 0 = no limit
        bool continue_on_error = true;
        std::function<void(size_t, size_t)> progress_callback;
        std::function<void(const std::string&)> error_callback;
    };

    /**
     * @brief Constructor with default config
     * @param extractor Extractor instance
     */
    explicit BatchExtractor(std::unique_ptr<core::IExtractor> extractor);

    /**
     * @brief Constructor with shared_ptr extractor (for parallel extraction)
     * @param extractor Shared extractor instance
     */
    explicit BatchExtractor(std::shared_ptr<core::IExtractor> extractor);

    /**
     * @brief Constructor with custom config
     * @param extractor Extractor instance
     * @param config Batch extraction configuration
     */
    BatchExtractor(std::unique_ptr<core::IExtractor> extractor,
                  const Config& config);

    /**
     * @brief Constructor with shared_ptr extractor and custom config
     * @param extractor Shared extractor instance
     * @param config Batch extraction configuration
     */
    BatchExtractor(std::shared_ptr<core::IExtractor> extractor,
                  const Config& config);

    /**
     * @brief Extract all records
     * @return std::vector<core::Record> All extracted records
     */
    std::vector<core::Record> extract_all();

    /**
     * @brief Extract records with callback processing
     * @param processor Callback function for processing each batch
     * @return size_t Total number of records processed
     */
    size_t extract_with_callback(
        std::function<void(const core::RecordBatch&)> processor);

    /**
     * @brief Get extraction statistics
     * @return std::unordered_map<std::string, std::any> Statistics
     */
    std::unordered_map<std::string, std::any> get_statistics() const;

    /**
     * @brief Update configuration
     * @param config New configuration
     */
    void set_config(const Config& config);

private:
    std::shared_ptr<core::IExtractor> extractor_;
    Config config_;
    core::ProcessingContext context_;
    mutable std::mutex extraction_mutex_;
};

/**
 * @brief Parallel extraction coordinator
 *
 * This class manages parallel extraction from multiple sources,
 * coordinating thread pools and aggregating results.
 */
class ParallelExtractor {
public:
    /**
     * @brief Configuration for parallel extraction
     */
    struct Config {
        size_t num_threads = 4;
        size_t queue_size = 100;
        bool preserve_order = false;
    };

    /**
     * @brief Constructor with default config
     */
    ParallelExtractor();

    /**
     * @brief Constructor with custom config
     * @param config Parallel extraction configuration
     */
    explicit ParallelExtractor(const Config& config);

    /**
     * @brief Destructor
     */
    ~ParallelExtractor();

    /**
     * @brief Add extractor to parallel processing
     * @param extractor Extractor instance
     * @param name Optional name for identification
     */
    void add_extractor(std::unique_ptr<core::IExtractor> extractor,
                      const std::string& name = "");

    /**
     * @brief Execute parallel extraction
     * @return std::vector<core::Record> Aggregated records from all sources
     */
    std::vector<core::Record> extract_all();

    /**
     * @brief Execute parallel extraction with streaming
     * @param processor Callback for processing record batches
     */
    void extract_streaming(
        std::function<void(const core::RecordBatch&, const std::string&)> processor);

    /**
     * @brief Get statistics for all extractors
     * @return std::unordered_map<std::string, std::unordered_map<std::string, std::any>>
     *         Statistics per extractor
     */
    std::unordered_map<std::string, std::unordered_map<std::string, std::any>>
    get_all_statistics() const;

private:
    Config config_;
    std::vector<std::thread> workers_;
    std::queue<std::function<void()>> task_queue_;
    std::mutex queue_mutex_;
    std::condition_variable queue_cv_;
    std::condition_variable completion_cv_;
    std::atomic<bool> stop_{false};
    std::atomic<size_t> active_tasks_{0};
    std::vector<std::pair<std::string, std::unique_ptr<core::IExtractor>>> extractors_;
    
    void worker_thread();
};

/**
 * @brief Utility functions for common extraction patterns
 */
namespace utils {

/**
 * @brief Extract records from CSV file
 * @param filepath Path to CSV file
 * @param options Additional CSV options
 * @return std::vector<core::Record> Extracted records
 */
std::vector<core::Record> extract_csv(
    const std::string& filepath,
    const CsvOptions& options = {});

/**
 * @brief Extract records from JSON file
 * @param filepath Path to JSON file
 * @param options Additional JSON options
 * @return std::vector<core::Record> Extracted records
 */
std::vector<core::Record> extract_json(
    const std::string& filepath,
    const JsonOptions& options = {});

/**
 * @brief Extract records from database table
 * @param connection Database connection
 * @param table_name Table name
 * @param filter Optional WHERE clause
 * @return std::vector<core::Record> Extracted records
 */
std::vector<core::Record> extract_table(
    std::unique_ptr<IDatabaseConnection> connection,
    const std::string& table_name,
    const std::string& filter = "");

/**
 * @brief Create database connection from URL
 * @param url Database URL (e.g., "postgresql://user:pass@host:port/db")
 * @return std::unique_ptr<IDatabaseConnection> Database connection
 */
std::unique_ptr<IDatabaseConnection> create_connection_from_url(
    const std::string& url);

} // namespace utils

} // namespace omop::extract

File src/lib/extract/mysql_connector.cpp:

/**
 * @file mysql_connector.cpp
 * @brief MySQL database connector implementation
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "mysql_connector.h"
#include "common/logging.h"
#include "common/exceptions.h"
#include <format>
#include <chrono>
#include <ctime>
#include <sstream>
#include <iomanip>
#include <cstring>

namespace omop::extract {

// MySQL type mapping
static const std::unordered_map<enum_field_types, std::string> MYSQL_TYPE_NAMES = {
    {MYSQL_TYPE_DECIMAL, "DECIMAL"},
    {MYSQL_TYPE_TINY, "TINYINT"},
    {MYSQL_TYPE_SHORT, "SMALLINT"},
    {MYSQL_TYPE_LONG, "INT"},
    {MYSQL_TYPE_FLOAT, "FLOAT"},
    {MYSQL_TYPE_DOUBLE, "DOUBLE"},
    {MYSQL_TYPE_NULL, "NULL"},
    {MYSQL_TYPE_TIMESTAMP, "TIMESTAMP"},
    {MYSQL_TYPE_LONGLONG, "BIGINT"},
    {MYSQL_TYPE_INT24, "MEDIUMINT"},
    {MYSQL_TYPE_DATE, "DATE"},
    {MYSQL_TYPE_TIME, "TIME"},
    {MYSQL_TYPE_DATETIME, "DATETIME"},
    {MYSQL_TYPE_YEAR, "YEAR"},
    {MYSQL_TYPE_NEWDATE, "DATE"},
    {MYSQL_TYPE_VARCHAR, "VARCHAR"},
    {MYSQL_TYPE_BIT, "BIT"},
    {MYSQL_TYPE_TIMESTAMP2, "TIMESTAMP"},
    {MYSQL_TYPE_DATETIME2, "DATETIME"},
    {MYSQL_TYPE_TIME2, "TIME"},
    {MYSQL_TYPE_JSON, "JSON"},
    {MYSQL_TYPE_NEWDECIMAL, "DECIMAL"},
    {MYSQL_TYPE_ENUM, "ENUM"},
    {MYSQL_TYPE_SET, "SET"},
    {MYSQL_TYPE_TINY_BLOB, "TINYBLOB"},
    {MYSQL_TYPE_MEDIUM_BLOB, "MEDIUMBLOB"},
    {MYSQL_TYPE_LONG_BLOB, "LONGBLOB"},
    {MYSQL_TYPE_BLOB, "BLOB"},
    {MYSQL_TYPE_VAR_STRING, "VARCHAR"},
    {MYSQL_TYPE_STRING, "CHAR"},
    {MYSQL_TYPE_GEOMETRY, "GEOMETRY"}
};

// MySQLResultSet implementation

MySQLResultSet::MySQLResultSet(std::shared_ptr<MySQLStatement> statement)
    : statement_(statement) {

    if (!statement_) {
        throw common::DatabaseException("Invalid MySQL statement", "MySQL", 0);
    }

    // Store result set
    result_ = mysql_stmt_result_metadata(statement_->get());
    if (!result_) {
        throw common::DatabaseException("No result set available", "MySQL",
                                      mysql_stmt_errno(statement_->get()));
    }

    // Load metadata
    load_metadata();
}

MySQLResultSet::~MySQLResultSet() {
    // Free bind buffers
    for (auto& bind : bind_buffers_) {
        if (bind.buffer) {
            free(bind.buffer);
        }
    }

    if (result_) {
        mysql_free_result(result_);
    }
}

void MySQLResultSet::load_metadata() {
    column_count_ = mysql_num_fields(result_);
    columns_.reserve(column_count_);
    bind_buffers_.resize(column_count_);

    MYSQL_FIELD* fields = mysql_fetch_fields(result_);

    for (unsigned int i = 0; i < column_count_; ++i) {
        ColumnInfo info;
        info.name = fields[i].name;
        info.type = fields[i].type;
        info.length = fields[i].length;
        info.flags = fields[i].flags;
        info.decimals = fields[i].decimals;
        columns_.push_back(info);

        // Set up bind buffers
        memset(&bind_buffers_[i], 0, sizeof(MYSQL_BIND));
        auto& bind = bind_buffers_[i];
        auto& buffer_info = reinterpret_cast<BindBuffer&>(bind);

        bind.buffer_type = fields[i].type;

        // Allocate buffer based on type
        switch (fields[i].type) {
            case MYSQL_TYPE_TINY:
                bind.buffer_length = sizeof(signed char);
                break;
            case MYSQL_TYPE_SHORT:
            case MYSQL_TYPE_YEAR:
                bind.buffer_length = sizeof(short);
                break;
            case MYSQL_TYPE_LONG:
            case MYSQL_TYPE_INT24:
                bind.buffer_length = sizeof(int);
                break;
            case MYSQL_TYPE_LONGLONG:
                bind.buffer_length = sizeof(long long);
                break;
            case MYSQL_TYPE_FLOAT:
                bind.buffer_length = sizeof(float);
                break;
            case MYSQL_TYPE_DOUBLE:
                bind.buffer_length = sizeof(double);
                break;
            case MYSQL_TYPE_TIME:
            case MYSQL_TYPE_DATE:
            case MYSQL_TYPE_DATETIME:
            case MYSQL_TYPE_TIMESTAMP:
                bind.buffer_length = sizeof(MYSQL_TIME);
                break;
            default:
                // String types and others
                bind.buffer_length = fields[i].length + 1;
                break;
        }

        bind.buffer = malloc(bind.buffer_length);
        buffer_info.is_null = &buffer_info.is_null_value;
        buffer_info.length = &buffer_info.length_value;
        buffer_info.error = &buffer_info.error_value;
        bind.is_null = buffer_info.is_null;
        bind.length = buffer_info.length;
        bind.error = buffer_info.error;
    }

    // Bind result buffers
    if (mysql_stmt_bind_result(statement_->get(), bind_buffers_.data()) != 0) {
        throw common::DatabaseException("Failed to bind result buffers", "MySQL",
                                      mysql_stmt_errno(statement_->get()));
    }

    // Store the result set
    if (mysql_stmt_store_result(statement_->get()) != 0) {
        throw common::DatabaseException("Failed to store result set", "MySQL",
                                      mysql_stmt_errno(statement_->get()));
    }

    row_count_ = mysql_stmt_num_rows(statement_->get());
}

bool MySQLResultSet::next() {
    int ret = mysql_stmt_fetch(statement_->get());

    if (ret == 0) {
        current_row_++;
        return true;
    } else if (ret == MYSQL_NO_DATA) {
        return false;
    } else if (ret == MYSQL_DATA_TRUNCATED) {
        // Handle truncated data
        auto logger = common::Logger::get("omop-mysql");
        logger->warn("Data truncated in row {}", current_row_ + 1);
        current_row_++;
        return true;
    } else {
        throw common::DatabaseException(
            std::format("Failed to fetch row: {}", mysql_stmt_error(statement_->get())),
            "MySQL", mysql_stmt_errno(statement_->get()));
    }
}

std::any MySQLResultSet::get_value(size_t index) const {
    if (index >= column_count_) {
        throw common::DatabaseException(
            std::format("Column index {} out of range (0-{})", index, column_count_ - 1),
            "MySQL", 0);
    }

    return convert_value(index);
}

std::any MySQLResultSet::get_value(const std::string& column_name) const {
    size_t index = get_column_index(column_name);
    return get_value(index);
}

bool MySQLResultSet::is_null(size_t index) const {
    if (index >= column_count_) {
        throw common::DatabaseException(
            std::format("Column index {} out of range", index),
            "MySQL", 0);
    }

    const auto& buffer_info = reinterpret_cast<const BindBuffer&>(bind_buffers_[index]);
    return buffer_info.is_null_value;
}

bool MySQLResultSet::is_null(const std::string& column_name) const {
    size_t index = get_column_index(column_name);
    return is_null(index);
}

size_t MySQLResultSet::column_count() const {
    return column_count_;
}

std::string MySQLResultSet::column_name(size_t index) const {
    if (index >= column_count_) {
        throw common::DatabaseException(
            std::format("Column index {} out of range", index),
            "MySQL", 0);
    }

    return columns_[index].name;
}

std::string MySQLResultSet::column_type(size_t index) const {
    if (index >= column_count_) {
        throw common::DatabaseException(
            std::format("Column index {} out of range", index),
            "MySQL", 0);
    }

    auto it = MYSQL_TYPE_NAMES.find(columns_[index].type);
    if (it != MYSQL_TYPE_NAMES.end()) {
        return it->second;
    }

    return std::format("MYSQL_TYPE_{}", static_cast<int>(columns_[index].type));
}

size_t MySQLResultSet::get_column_index(const std::string& column_name) const {
    // Check cache first
    auto it = column_index_cache_.find(column_name);
    if (it != column_index_cache_.end()) {
        return it->second;
    }

    // Search for column
    for (size_t i = 0; i < column_count_; ++i) {
        if (columns_[i].name == column_name) {
            column_index_cache_[column_name] = i;
            return i;
        }
    }

    throw common::DatabaseException(
        std::format("Column '{}' not found", column_name),
        "MySQL", 0);
}

std::any MySQLResultSet::convert_value(size_t index) const {
    const auto& bind = bind_buffers_[index];
    const auto& buffer_info = reinterpret_cast<const BindBuffer&>(bind);

    if (buffer_info.is_null_value) {
        return std::any{};
    }

    switch (columns_[index].type) {
        case MYSQL_TYPE_TINY: {
            signed char value = *reinterpret_cast<signed char*>(bind.buffer);
            return static_cast<int>(value);
        }

        case MYSQL_TYPE_SHORT:
        case MYSQL_TYPE_YEAR: {
            short value = *reinterpret_cast<short*>(bind.buffer);
            return static_cast<int>(value);
        }

        case MYSQL_TYPE_LONG:
        case MYSQL_TYPE_INT24: {
            int value = *reinterpret_cast<int*>(bind.buffer);
            return value;
        }

        case MYSQL_TYPE_LONGLONG: {
            long long value = *reinterpret_cast<long long*>(bind.buffer);
            return value;
        }

        case MYSQL_TYPE_FLOAT: {
            float value = *reinterpret_cast<float*>(bind.buffer);
            return static_cast<double>(value);
        }

        case MYSQL_TYPE_DOUBLE: {
            double value = *reinterpret_cast<double*>(bind.buffer);
            return value;
        }

        case MYSQL_TYPE_DATE:
        case MYSQL_TYPE_TIME:
        case MYSQL_TYPE_DATETIME:
        case MYSQL_TYPE_TIMESTAMP: {
            MYSQL_TIME* time = reinterpret_cast<MYSQL_TIME*>(bind.buffer);

            std::tm tm = {};
            tm.tm_year = time->year - 1900;
            tm.tm_mon = time->month - 1;
            tm.tm_mday = time->day;
            tm.tm_hour = time->hour;
            tm.tm_min = time->minute;
            tm.tm_sec = time->second;

            auto time_point = std::chrono::system_clock::from_time_t(std::mktime(&tm));

            // Add microseconds
            if (time->second_part > 0) {
                auto micros = std::chrono::microseconds(time->second_part);
                time_point += micros;
            }

            return time_point;
        }

        case MYSQL_TYPE_BIT: {
            // Convert BIT to boolean for single bit, or integer for multiple bits
            // Bounds check for buffer access
            if (buffer_info.length_value > 8) {
                throw common::DatabaseException(
                    std::format("BIT field too large: {} bytes", buffer_info.length_value),
                    "MySQL", 0);
            }
            if (columns_[index].length == 1) {
                unsigned char value = *reinterpret_cast<unsigned char*>(bind.buffer);
                return static_cast<bool>(value & 1);
            } else {
                // Return as integer for multi-bit fields
                long long value = 0;
                unsigned char* bytes = reinterpret_cast<unsigned char*>(bind.buffer);
                for (unsigned long i = 0; i < buffer_info.length_value && i < 8; ++i) {
                    value = (value << 8) | bytes[i];
                }
                return value;
            }
        }

        default: {
            // String types and others
            std::string value(reinterpret_cast<char*>(bind.buffer), buffer_info.length_value);
            return value;
        }
    }
}

// MySQLPreparedStatement implementation

MySQLPreparedStatement::MySQLPreparedStatement(MYSQL* mysql, const std::string& sql)
    : sql_(sql) {

    statement_ = std::make_shared<MySQLStatement>(mysql);

    // Prepare the statement
    if (mysql_stmt_prepare(statement_->get(), sql.c_str(), sql.length()) != 0) {
        throw common::DatabaseException(
            std::format("Failed to prepare statement: {}", mysql_stmt_error(statement_->get())),
            "MySQL", mysql_stmt_errno(statement_->get()));
    }

    // Get parameter count
    param_count_ = mysql_stmt_param_count(statement_->get());
    if (param_count_ > 0) {
        parameters_.resize(param_count_);
    }
}

MySQLPreparedStatement::~MySQLPreparedStatement() {
    // Clean up parameter buffers
    for (auto& param : parameters_) {
        if (param.bind.buffer && param.bind.buffer != param.buffer.data()) {
            free(param.bind.buffer);
        }
    }
}

void MySQLPreparedStatement::bind(size_t index, const std::any& value) {
    if (index == 0) {
        throw common::DatabaseException("Parameter index must be 1-based", "MySQL", 0);
    }

    if (index > param_count_) {
        throw common::DatabaseException(
            std::format("Parameter index {} exceeds parameter count {}", index, param_count_),
            "MySQL", 0);
    }

    setup_parameter_binding(parameters_[index - 1], value);
}

void MySQLPreparedStatement::setup_parameter_binding(ParameterBinding& binding, const std::any& value) {
    binding.value = value;
    memset(&binding.bind, 0, sizeof(MYSQL_BIND));

    if (!value.has_value()) {
        binding.is_null = 1;
        binding.bind.is_null = &binding.is_null;
        binding.bind.buffer_type = MYSQL_TYPE_NULL;
    }
    else if (value.type() == typeid(bool)) {
        binding.is_null = 0;
        binding.buffer.resize(sizeof(signed char));
        *reinterpret_cast<signed char*>(binding.buffer.data()) =
            std::any_cast<bool>(value) ? 1 : 0;
        binding.bind.buffer_type = MYSQL_TYPE_TINY;
        binding.bind.buffer = binding.buffer.data();
        binding.bind.is_null = &binding.is_null;
    }
    else if (value.type() == typeid(int)) {
        binding.is_null = 0;
        binding.buffer.resize(sizeof(int));
        *reinterpret_cast<int*>(binding.buffer.data()) = std::any_cast<int>(value);
        binding.bind.buffer_type = MYSQL_TYPE_LONG;
        binding.bind.buffer = binding.buffer.data();
        binding.bind.is_null = &binding.is_null;
    }
    else if (value.type() == typeid(long long)) {
        binding.is_null = 0;
        binding.buffer.resize(sizeof(long long));
        *reinterpret_cast<long long*>(binding.buffer.data()) = std::any_cast<long long>(value);
        binding.bind.buffer_type = MYSQL_TYPE_LONGLONG;
        binding.bind.buffer = binding.buffer.data();
        binding.bind.is_null = &binding.is_null;
    }
    else if (value.type() == typeid(double)) {
        binding.is_null = 0;
        binding.buffer.resize(sizeof(double));
        *reinterpret_cast<double*>(binding.buffer.data()) = std::any_cast<double>(value);
        binding.bind.buffer_type = MYSQL_TYPE_DOUBLE;
        binding.bind.buffer = binding.buffer.data();
        binding.bind.is_null = &binding.is_null;
    }
    else if (value.type() == typeid(std::string)) {
        binding.is_null = 0;
        std::string str_val = std::any_cast<std::string>(value);
        binding.buffer.assign(str_val.begin(), str_val.end());
        binding.length = binding.buffer.size();
        binding.bind.buffer_type = MYSQL_TYPE_STRING;
        binding.bind.buffer = binding.buffer.data();
        binding.bind.buffer_length = binding.buffer.size();
        binding.bind.length = &binding.length;
        binding.bind.is_null = &binding.is_null;
    }
    else if (value.type() == typeid(std::chrono::system_clock::time_point)) {
        binding.is_null = 0;
        binding.bind.buffer_type = MYSQL_TYPE_TIMESTAMP;

        auto tp = std::any_cast<std::chrono::system_clock::time_point>(value);
        auto time_t_val = std::chrono::system_clock::to_time_t(tp);
        std::tm tm = *std::localtime(&time_t_val);

        MYSQL_TIME* mysql_time = reinterpret_cast<MYSQL_TIME*>(malloc(sizeof(MYSQL_TIME)));
        memset(mysql_time, 0, sizeof(MYSQL_TIME));

        mysql_time->year = tm.tm_year + 1900;
        mysql_time->month = tm.tm_mon + 1;
        mysql_time->day = tm.tm_mday;
        mysql_time->hour = tm.tm_hour;
        mysql_time->minute = tm.tm_min;
        mysql_time->second = tm.tm_sec;

        // Add microseconds if available
        auto duration = tp.time_since_epoch();
        auto micros = std::chrono::duration_cast<std::chrono::microseconds>(duration).count() % 1000000;
        mysql_time->second_part = micros;

        binding.bind.buffer = mysql_time;
        binding.bind.buffer_length = sizeof(MYSQL_TIME);
        binding.bind.is_null = &binding.is_null;
    }
    else {
        throw common::DatabaseException(
            "Unsupported parameter type for MySQL", "MySQL", 0);
    }
}

void MySQLPreparedStatement::bind_parameters() {
    if (param_count_ == 0) return;

    std::vector<MYSQL_BIND> binds(param_count_);
    for (size_t i = 0; i < param_count_; ++i) {
        binds[i] = parameters_[i].bind;
    }

    if (mysql_stmt_bind_param(statement_->get(), binds.data()) != 0) {
        throw common::DatabaseException(
            std::format("Failed to bind parameters: {}", mysql_stmt_error(statement_->get())),
            "MySQL", mysql_stmt_errno(statement_->get()));
    }
}

std::unique_ptr<IResultSet> MySQLPreparedStatement::execute_query() {
    bind_parameters();

    if (mysql_stmt_execute(statement_->get()) != 0) {
        throw common::DatabaseException(
            std::format("Query execution failed: {}", mysql_stmt_error(statement_->get())),
            "MySQL", mysql_stmt_errno(statement_->get()));
    }

    return std::make_unique<MySQLResultSet>(statement_);
}

size_t MySQLPreparedStatement::execute_update() {
    bind_parameters();

    if (mysql_stmt_execute(statement_->get()) != 0) {
        throw common::DatabaseException(
            std::format("Update execution failed: {}", mysql_stmt_error(statement_->get())),
            "MySQL", mysql_stmt_errno(statement_->get()));
    }

    return mysql_stmt_affected_rows(statement_->get());
}

void MySQLPreparedStatement::clear_parameters() {
    for (auto& param : parameters_) {
        if (param.bind.buffer && param.bind.buffer != param.buffer.data()) {
            free(param.bind.buffer);
            param.bind.buffer = nullptr;
        }
        param.buffer.clear();
        memset(&param.bind, 0, sizeof(MYSQL_BIND));
    }

    if (mysql_stmt_reset(statement_->get()) != 0) {
        auto logger = common::Logger::get("omop-mysql");
        logger->warn("Failed to reset statement: {}", mysql_stmt_error(statement_->get()));
    }
}

// MySQLConnection implementation

MySQLConnection::MySQLConnection() {
    mysql_ = mysql_init(nullptr);
    if (!mysql_) {
        throw common::DatabaseException("Failed to initialize MySQL connection", "MySQL", 0);
    }
}

MySQLConnection::~MySQLConnection() {
    disconnect();

    if (mysql_) {
        mysql_close(mysql_);
    }
}

void MySQLConnection::connect(const ConnectionParams& params) {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (connected_) {
        disconnect();
    }

    // Set connection options
    set_connection_options(params);

    // Connect
    unsigned int port = params.port > 0 ? params.port : 3306;
    const char* unix_socket = nullptr;
    unsigned long client_flags = CLIENT_MULTI_STATEMENTS;

    // Check for SSL options
    auto ssl_it = params.options.find("ssl_mode");
    if (ssl_it != params.options.end() && ssl_it->second != "DISABLED") {
        client_flags |= CLIENT_SSL;
    }

    if (!mysql_real_connect(mysql_,
                           params.host.c_str(),
                           params.username.c_str(),
                           params.password.c_str(),
                           params.database.c_str(),
                           port,
                           unix_socket,
                           client_flags)) {
        throw common::DatabaseException(
            std::format("Failed to connect to MySQL: {}", mysql_error(mysql_)),
            "MySQL", mysql_errno(mysql_));
    }

    connected_ = true;

    // Set character set to UTF8
    if (mysql_set_character_set(mysql_, "utf8mb4") != 0) {
        auto logger = common::Logger::get("omop-mysql");
        logger->warn("Failed to set character set to utf8mb4: {}", mysql_error(mysql_));
    }

    auto logger = common::Logger::get("omop-mysql");
    logger->info("Connected to MySQL database '{}' on {}:{}",
                params.database, params.host, port);
}

void MySQLConnection::disconnect() {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (connected_) {
        if (in_transaction_) {
            try {
                rollback();
            } catch (...) {
                // Ignore errors during disconnect
            }
        }

        connected_ = false;

        auto logger = common::Logger::get("omop-mysql");
        logger->info("Disconnected from MySQL database");
    }
}

bool MySQLConnection::is_connected() const {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (!connected_ || !mysql_) {
        return false;
    }

    // Ping to check connection
    return mysql_ping(mysql_) == 0;
}

std::unique_ptr<IResultSet> MySQLConnection::execute_query(const std::string& sql) {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (!connected_) {
        throw common::DatabaseException("Not connected to database", "MySQL", 0);
    }

    // For queries that return results, use prepared statements
    auto stmt = std::make_shared<MySQLStatement>(mysql_);

    if (mysql_stmt_prepare(stmt->get(), sql.c_str(), sql.length()) != 0) {
        throw common::DatabaseException(
            std::format("Failed to prepare query: {}", mysql_stmt_error(stmt->get())),
            "MySQL", mysql_stmt_errno(stmt->get()));
    }

    if (mysql_stmt_execute(stmt->get()) != 0) {
        throw common::DatabaseException(
            std::format("Query execution failed: {}", mysql_stmt_error(stmt->get())),
            "MySQL", mysql_stmt_errno(stmt->get()));
    }

    return std::make_unique<MySQLResultSet>(stmt);
}

size_t MySQLConnection::execute_update(const std::string& sql) {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (!connected_) {
        throw common::DatabaseException("Not connected to database", "MySQL", 0);
    }

    if (mysql_query(mysql_, sql.c_str()) != 0) {
        throw common::DatabaseException(
            std::format("Update execution failed: {}", mysql_error(mysql_)),
            "MySQL", mysql_errno(mysql_));
    }

    return mysql_affected_rows(mysql_);
}

std::unique_ptr<IPreparedStatement> MySQLConnection::prepare_statement(const std::string& sql) {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (!connected_) {
        throw common::DatabaseException("Not connected to database", "MySQL", 0);
    }

    return std::make_unique<MySQLPreparedStatement>(mysql_, sql);
}

void MySQLConnection::begin_transaction() {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (in_transaction_) {
        throw common::DatabaseException("Transaction already in progress", "MySQL", 0);
    }

    execute_update("START TRANSACTION");
    in_transaction_ = true;
}

void MySQLConnection::commit() {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (!in_transaction_) {
        throw common::DatabaseException("No transaction in progress", "MySQL", 0);
    }

    execute_update("COMMIT");
    in_transaction_ = false;
}

void MySQLConnection::rollback() {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (!in_transaction_) {
        throw common::DatabaseException("No transaction in progress", "MySQL", 0);
    }

    execute_update("ROLLBACK");
    in_transaction_ = false;
}

std::string MySQLConnection::get_version() const {
    if (!connected_) {
        return "Not connected";
    }

    return mysql_get_server_info(mysql_);
}

void MySQLConnection::set_query_timeout(int seconds) {
    query_timeout_ = seconds;

    if (connected_) {
        std::string timeout_sql = std::format("SET SESSION max_execution_time = {}", seconds * 1000);
        const_cast<MySQLConnection*>(this)->execute_update(timeout_sql);
    }
}

bool MySQLConnection::table_exists(const std::string& table_name,
                                 const std::string& schema) const {
    if (!connected_) {
        return false;
    }

    std::string query;
    if (schema.empty()) {
        query = std::format(
            "SELECT COUNT(*) FROM information_schema.tables "
            "WHERE table_schema = DATABASE() AND table_name = '{}'",
            table_name);
    } else {
        query = std::format(
            "SELECT COUNT(*) FROM information_schema.tables "
            "WHERE table_schema = '{}' AND table_name = '{}'",
            schema, table_name);
    }

    if (mysql_query(mysql_, query.c_str()) != 0) {
        return false;
    }

    MYSQL_RES* result = mysql_store_result(mysql_);
    if (!result) {
        return false;
    }

    bool exists = false;
    MYSQL_ROW row = mysql_fetch_row(result);
    if (row && row[0]) {
        exists = std::stoi(row[0]) > 0;
    }

    mysql_free_result(result);
    return exists;
}

void MySQLConnection::check_error(const std::string& operation) const {
    if (mysql_errno(mysql_) != 0) {
        throw common::DatabaseException(
            std::format("{} failed: {}", operation, mysql_error(mysql_)),
            "MySQL", mysql_errno(mysql_));
    }
}

void MySQLConnection::set_connection_options(const ConnectionParams& params) {
    // Set connection timeout
    unsigned int timeout = 10;  // Default 10 seconds
    auto timeout_it = params.options.find("connect_timeout");
    if (timeout_it != params.options.end()) {
        timeout = std::stoi(timeout_it->second);
    }
    mysql_options(mysql_, MYSQL_OPT_CONNECT_TIMEOUT, &timeout);

    // Set read timeout
    if (query_timeout_ > 0) {
        unsigned int read_timeout = query_timeout_;
        mysql_options(mysql_, MYSQL_OPT_READ_TIMEOUT, &read_timeout);
    }

    // Enable automatic reconnection
    bool reconnect = true;
    mysql_options(mysql_, MYSQL_OPT_RECONNECT, &reconnect);

    // Set SSL options if provided
    auto ssl_mode_it = params.options.find("ssl_mode");
    if (ssl_mode_it != params.options.end()) {
        const char* ssl_mode = ssl_mode_it->second.c_str();
        mysql_options(mysql_, MYSQL_OPT_SSL_MODE, ssl_mode);
    }

    auto ssl_ca_it = params.options.find("ssl_ca");
    if (ssl_ca_it != params.options.end()) {
        mysql_options(mysql_, MYSQL_OPT_SSL_CA, ssl_ca_it->second.c_str());
    }

    auto ssl_cert_it = params.options.find("ssl_cert");
    if (ssl_cert_it != params.options.end()) {
        mysql_options(mysql_, MYSQL_OPT_SSL_CERT, ssl_cert_it->second.c_str());
    }

    auto ssl_key_it = params.options.find("ssl_key");
    if (ssl_key_it != params.options.end()) {
        mysql_options(mysql_, MYSQL_OPT_SSL_KEY, ssl_key_it->second.c_str());
    }
}

// MySQLExtractor implementation

std::string MySQLExtractor::build_query() const {
    // Use parent implementation and add MySQL-specific optimizations
    std::string query = DatabaseExtractor::build_query();

    // Add MySQL-specific query hints if needed
    // For example, adding SQL_BUFFER_RESULT for large result sets
    if (query.find("SELECT") == 0) {
        query.insert(6, " SQL_BUFFER_RESULT");
    }

    return query;
}

// MySQLRegistrar implementation
void MySQLRegistrar::register_components() {
    DatabaseConnectionFactory::instance().register_type(
        "mysql",
        [](const IDatabaseConnection::ConnectionParams& params) {
            auto conn = std::make_unique<MySQLConnection>();
            conn->connect(params);
            return conn;
        }
    );

    DatabaseConnectionFactory::instance().register_type(
        "mariadb",  // Alias for MariaDB
        [](const IDatabaseConnection::ConnectionParams& params) {
            auto conn = std::make_unique<MySQLConnection>();
            conn->connect(params);
            return conn;
        }
    );
}

} // namespace omop::extract

File src/lib/extract/extractor_factory.h:

/**
 * @file extractor_factory.h
 * @brief Extractor factory and registry interface
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This file provides the factory registry for creating and managing
 * different types of data extractors in the OMOP ETL pipeline.
 */

#pragma once

#include "core/interfaces.h"
#include "common/exceptions.h"
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <functional>
#include <mutex>
#include <any>
#include <iostream>

namespace omop::extract {

/**
 * @brief Central registry for extractor types
 *
 * This class maintains a registry of all available extractor types
 * and provides factory methods for creating instances.
 */
class ExtractorFactoryRegistry {
public:
    /**
     * @brief Register an extractor type
     * @param type Extractor type identifier
     * @param creator Factory function
     */
    static void register_type(const std::string& type,
                            std::function<std::unique_ptr<core::IExtractor>()> creator);

    /**
     * @brief Create an extractor instance
     * @param type Extractor type identifier
     * @return std::unique_ptr<core::IExtractor> Extractor instance
     * @throws ConfigurationException if type is not registered
     */
    static std::unique_ptr<core::IExtractor> create(const std::string& type);

    /**
     * @brief Get list of registered extractor types
     * @return std::vector<std::string> Sorted list of type identifiers
     */
    static std::vector<std::string> get_registered_types();

    /**
     * @brief Check if a type is registered
     * @param type Extractor type identifier
     * @return bool True if type is registered
     */
    static bool is_type_registered(const std::string& type);

    /**
     * @brief Clear all registered types (for testing)
     */
    static void clear();

private:
    static std::unordered_map<std::string,
        std::function<std::unique_ptr<core::IExtractor>()>> creators_;
    static std::mutex mutex_;
};

/**
 * @brief Initialize all built-in extractors
 *
 * This function registers all built-in extractor types with the factory.
 * It is called automatically when creating extractors, but can be called
 * manually to ensure all types are available.
 */
void initialize_extractors();

/**
 * @brief Reset the initialization flag (for testing)
 *
 * This function resets the internal initialization flag, allowing
 * extractors to be re-registered. This is primarily used for testing.
 */
void reset_initialize_extractors_flag();

/**
 * @brief Create and initialize an extractor
 * @param type Extractor type identifier
 * @param config Configuration parameters
 * @return std::unique_ptr<core::IExtractor> Initialized extractor
 * @throws ConfigurationException if type is invalid or initialization fails
 */
std::unique_ptr<core::IExtractor> create_extractor(
    const std::string& type,
    const std::unordered_map<std::string, std::any>& config = {});

/**
 * @brief Extractor configuration builder
 *
 * Helper class for building extractor configurations with type safety
 * and validation.
 */
class ExtractorConfigBuilder {
public:
    /**
     * @brief Constructor
     * @param type Extractor type
     */
    explicit ExtractorConfigBuilder(const std::string& type) : type_(type) {}

    /**
     * @brief Set configuration parameter
     * @param key Parameter name
     * @param value Parameter value
     * @return ExtractorConfigBuilder& Builder instance for chaining
     */
    template<typename T>
    ExtractorConfigBuilder& set(const std::string& key, T&& value) {
        config_[key] = std::forward<T>(value);
        return *this;
    }

    /**
     * @brief Set file path
     * @param path File path
     * @return ExtractorConfigBuilder& Builder instance
     */
    ExtractorConfigBuilder& with_file(const std::string& path) {
        return set("filepath", path);
    }

    /**
     * @brief Set multiple files
     * @param files File paths
     * @return ExtractorConfigBuilder& Builder instance
     */
    ExtractorConfigBuilder& with_files(const std::vector<std::string>& files) {
        return set("files", files);
    }

    /**
     * @brief Set directory path
     * @param path Directory path
     * @return ExtractorConfigBuilder& Builder instance
     */
    ExtractorConfigBuilder& with_directory(const std::string& path) {
        return set("directory", path);
    }

    /**
     * @brief Set database connection parameters
     * @param host Database host
     * @param port Database port
     * @param database Database name
     * @param username Username
     * @param password Password
     * @return ExtractorConfigBuilder& Builder instance
     */
    ExtractorConfigBuilder& with_database(const std::string& host,
                                         int port,
                                         const std::string& database,
                                         const std::string& username,
                                         const std::string& password) {
        return set("host", host)
              .set("port", port)
              .set("database", database)
              .set("username", username)
              .set("password", password);
    }

    /**
     * @brief Set table name
     * @param table Table name
     * @param schema Schema name (optional)
     * @return ExtractorConfigBuilder& Builder instance
     */
    ExtractorConfigBuilder& with_table(const std::string& table,
                                      const std::string& schema = "") {
        set("table", table);
        if (!schema.empty()) {
            set("schema", schema);
        }
        return *this;
    }

    /**
     * @brief Set columns to extract
     * @param columns Column names
     * @return ExtractorConfigBuilder& Builder instance
     */
    ExtractorConfigBuilder& with_columns(const std::vector<std::string>& columns) {
        return set("columns", columns);
    }

    /**
     * @brief Set filter condition
     * @param filter SQL WHERE clause or filter expression
     * @return ExtractorConfigBuilder& Builder instance
     */
    ExtractorConfigBuilder& with_filter(const std::string& filter) {
        return set("filter", filter);
    }

    /**
     * @brief Build and create extractor
     * @return std::unique_ptr<core::IExtractor> Configured extractor
     */
    std::unique_ptr<core::IExtractor> build() {
        return create_extractor(type_, config_);
    }

    /**
     * @brief Get configuration map
     * @return std::unordered_map<std::string, std::any> Configuration
     */
    std::unordered_map<std::string, std::any> get_config() const {
        auto config = config_;
        config["type"] = type_;
        return config;
    }

private:
    std::string type_;
    std::unordered_map<std::string, std::any> config_;
};

/**
 * @brief Extractor type information
 */
struct ExtractorTypeInfo {
    std::string type;                              ///< Type identifier
    std::string description;                       ///< Description
    std::vector<std::string> required_params;      ///< Required parameters
    std::vector<std::string> optional_params;      ///< Optional parameters
    std::string example_config;                    ///< Example configuration JSON
};

/**
 * @brief Get information about all extractor types
 * @return std::vector<ExtractorTypeInfo> Type information
 */
std::vector<ExtractorTypeInfo> get_extractor_info();

/**
 * @brief Print extractor type information
 * @param stream Output stream
 */
void print_extractor_info(std::ostream& stream = std::cout);

} // namespace omop::extract

File src/lib/extract/platform/unix_utils.h:

/**
 * @file unix_utils.h
 * @brief Unix/Linux-specific utility functions for the extract module
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This file contains Unix/Linux-specific implementations for file handling,
 * path operations, and system interactions.
 */

#pragma once

#ifndef _WIN32

#include <string>
#include <vector>
#include <memory>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <unistd.h>

namespace omop::extract::platform {

/**
 * @brief Unix file descriptor wrapper with RAII
 */
class UnixFileDescriptor {
public:
    /**
     * @brief Constructor
     * @param fd File descriptor
     */
    explicit UnixFileDescriptor(int fd = -1) : fd_(fd) {}

    /**
     * @brief Destructor
     */
    ~UnixFileDescriptor() {
        if (fd_ >= 0) {
            close(fd_);
        }
    }

    // Delete copy operations
    UnixFileDescriptor(const UnixFileDescriptor&) = delete;
    UnixFileDescriptor& operator=(const UnixFileDescriptor&) = delete;

    // Move operations
    UnixFileDescriptor(UnixFileDescriptor&& other) noexcept : fd_(other.fd_) {
        other.fd_ = -1;
    }

    UnixFileDescriptor& operator=(UnixFileDescriptor&& other) noexcept {
        if (this != &other) {
            if (fd_ >= 0) {
                close(fd_);
            }
            fd_ = other.fd_;
            other.fd_ = -1;
        }
        return *this;
    }

    /**
     * @brief Get raw file descriptor
     * @return int File descriptor
     */
    int get() const { return fd_; }

    /**
     * @brief Check if descriptor is valid
     * @return bool True if valid
     */
    bool is_valid() const { return fd_ >= 0; }

private:
    int fd_;
};

/**
 * @brief Memory mapped file wrapper
 */
class MemoryMappedFile {
public:
    /**
     * @brief Constructor
     */
    MemoryMappedFile() = default;

    /**
     * @brief Destructor
     */
    ~MemoryMappedFile();

    // Delete copy operations
    MemoryMappedFile(const MemoryMappedFile&) = delete;
    MemoryMappedFile& operator=(const MemoryMappedFile&) = delete;

    // Move operations
    MemoryMappedFile(MemoryMappedFile&& other) noexcept;
    MemoryMappedFile& operator=(MemoryMappedFile&& other) noexcept;

    /**
     * @brief Map file into memory
     * @param filepath File path
     * @param read_only Map as read-only
     * @return bool True if successful
     */
    bool map_file(const std::string& filepath, bool read_only = true);

    /**
     * @brief Unmap file from memory
     */
    void unmap();

    /**
     * @brief Get mapped memory pointer
     * @return void* Memory pointer
     */
    void* data() const { return data_; }

    /**
     * @brief Get mapped size
     * @return size_t Mapped size
     */
    size_t size() const { return size_; }

    /**
     * @brief Check if file is mapped
     * @return bool True if mapped
     */
    bool is_mapped() const { return data_ != nullptr; }

private:
    void* data_{nullptr};
    size_t size_{0};
    UnixFileDescriptor fd_;
};

/**
 * @brief Get system error message
 * @param error_code Error code (default = errno)
 * @return std::string Error message
 */
std::string get_system_error_message(int error_code = 0);

/**
 * @brief Get file size
 * @param filepath File path
 * @return size_t File size in bytes
 */
size_t get_file_size(const std::string& filepath);

/**
 * @brief Get file modification time
 * @param filepath File path
 * @return time_t Modification time
 */
time_t get_file_mtime(const std::string& filepath);

/**
 * @brief Check if path is a symbolic link
 * @param path File path
 * @return bool True if symbolic link
 */
bool is_symbolic_link(const std::string& path);

/**
 * @brief Resolve symbolic link
 * @param path Symbolic link path
 * @return std::string Resolved path
 */
std::string resolve_symbolic_link(const std::string& path);

/**
 * @brief Get real path (resolving all symbolic links)
 * @param path File path
 * @return std::string Real path
 */
std::string get_real_path(const std::string& path);

/**
 * @brief Check if path is on a network filesystem
 * @param path File path
 * @return bool True if on network filesystem
 */
bool is_network_path(const std::string& path);

/**
 * @brief Get mounted filesystems
 * @return std::vector<std::string> Mounted filesystem paths
 */
std::vector<std::string> get_mounted_filesystems();

/**
 * @brief Get temporary directory path
 * @return std::string Temporary directory path
 */
std::string get_temp_directory();

/**
 * @brief Create unique temporary file
 * @param prefix File name prefix
 * @param extension File extension
 * @return std::string Temporary file path
 */
std::string create_temp_file(const std::string& prefix = "omop_",
                           const std::string& extension = ".tmp");

/**
 * @brief Set file permissions
 * @param filepath File path
 * @param mode Permission mode (e.g., 0644)
 * @return bool True if successful
 */
bool set_file_permissions(const std::string& filepath, mode_t mode);

/**
 * @brief Get file permissions
 * @param filepath File path
 * @return mode_t Permission mode
 */
mode_t get_file_permissions(const std::string& filepath);

/**
 * @brief High-resolution timer using clock_gettime
 */
class UnixHighResTimer {
public:
    /**
     * @brief Constructor - starts timer
     */
    UnixHighResTimer();

    /**
     * @brief Reset timer
     */
    void reset();

    /**
     * @brief Get elapsed time in seconds
     * @return double Elapsed time
     */
    double elapsed_seconds() const;

    /**
     * @brief Get elapsed time in milliseconds
     * @return double Elapsed time
     */
    double elapsed_milliseconds() const;

private:
    struct timespec start_time_;
};

/**
 * @brief Get system memory information
 */
struct MemoryInfo {
    size_t total_physical;     ///< Total physical memory
    size_t available_physical; ///< Available physical memory
    size_t total_swap;         ///< Total swap space
    size_t available_swap;     ///< Available swap space
};

/**
 * @brief Get system memory information
 * @return MemoryInfo Memory statistics
 */
MemoryInfo get_memory_info();

/**
 * @brief Set process priority (nice value)
 * @param priority Nice value (-20 to 19)
 * @return bool True if successful
 */
bool set_process_priority(int priority);

/**
 * @brief Get current process priority
 * @return int Nice value
 */
int get_process_priority();

/**
 * @brief Lock memory pages to prevent swapping
 * @param addr Memory address
 * @param size Memory size
 * @return bool True if successful
 */
bool lock_memory(void* addr, size_t size);

/**
 * @brief Unlock memory pages
 * @param addr Memory address
 * @param size Memory size
 * @return bool True if successful
 */
bool unlock_memory(void* addr, size_t size);

/**
 * @brief Advise kernel about memory usage pattern
 * @param addr Memory address
 * @param size Memory size
 * @param advice Advice flag (e.g., MADV_SEQUENTIAL)
 * @return bool True if successful
 */
bool advise_memory_usage(void* addr, size_t size, int advice);

/**
 * @brief Get number of CPU cores
 * @return size_t Number of CPU cores
 */
size_t get_cpu_count();

/**
 * @brief Set CPU affinity for current thread
 * @param cpu_set CPU set mask
 * @return bool True if successful
 */
bool set_thread_affinity(const std::vector<int>& cpu_set);

} // namespace omop::extract::platform

#endif // !_WIN32

File src/lib/extract/platform/unix_utils.cpp:

/**
 * @file unix_utils.cpp
 * @brief Unix/Linux-specific utility functions implementation
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#ifndef _WIN32

#include "extract/platform/unix_utils.h"
#include "common/exceptions.h"
#include <cstring>
#include <cerrno>
#include <format>
#include <sys/mman.h>
#include <sys/resource.h>
#ifdef __linux__
#include <sys/sysinfo.h>
#endif
#include <sys/statvfs.h>
#include <fstream>
#include <sstream>
#include <random>
#include <limits.h>
#include <stdlib.h>
#include <time.h>

#ifdef __linux__
#include <sched.h>
#endif

namespace omop::extract::platform {

// MemoryMappedFile implementation

MemoryMappedFile::~MemoryMappedFile() {
    unmap();
}

MemoryMappedFile::MemoryMappedFile(MemoryMappedFile&& other) noexcept
    : data_(other.data_), size_(other.size_), fd_(std::move(other.fd_)) {
    other.data_ = nullptr;
    other.size_ = 0;
}

MemoryMappedFile& MemoryMappedFile::operator=(MemoryMappedFile&& other) noexcept {
    if (this != &other) {
        unmap();
        data_ = other.data_;
        size_ = other.size_;
        fd_ = std::move(other.fd_);
        other.data_ = nullptr;
        other.size_ = 0;
    }
    return *this;
}

bool MemoryMappedFile::map_file(const std::string& filepath, bool read_only) {
    // Clean up any existing mapping
    unmap();

    // Open file
    int flags = read_only ? O_RDONLY : O_RDWR;
    fd_ = UnixFileDescriptor(open(filepath.c_str(), flags));

    if (!fd_.is_valid()) {
        return false;
    }

    // Get file size
    struct stat st;
    if (fstat(fd_.get(), &st) != 0) {
        return false;
    }

    size_ = st.st_size;
    if (size_ == 0) {
        return false;
    }

    // Map file
    int prot = read_only ? PROT_READ : (PROT_READ | PROT_WRITE);
    data_ = mmap(nullptr, size_, prot, MAP_PRIVATE, fd_.get(), 0);

    if (data_ == MAP_FAILED) {
        data_ = nullptr;
        size_ = 0;
        return false;
    }

    // Advise kernel about access pattern
    madvise(data_, size_, MADV_SEQUENTIAL);

    return true;
}

void MemoryMappedFile::unmap() {
    if (data_ != nullptr) {
        munmap(data_, size_);
        data_ = nullptr;
        size_ = 0;
    }
}

// Utility functions

std::string get_system_error_message(int error_code) {
    if (error_code == 0) {
        error_code = errno;
    }

    char buffer[256];
    // Use thread-safe version
    if (strerror_r(error_code, buffer, sizeof(buffer)) == 0) {
        return std::string(buffer);
    }

    return std::format("Unknown error code: {}", error_code);
}

size_t get_file_size(const std::string& filepath) {
    struct stat st;
    if (stat(filepath.c_str(), &st) != 0) {
        throw std::runtime_error(
            std::format("Failed to get file size '{}': {}",
                       filepath, get_system_error_message()));
    }

    return static_cast<size_t>(st.st_size);
}

time_t get_file_mtime(const std::string& filepath) {
    struct stat st;
    if (stat(filepath.c_str(), &st) != 0) {
        throw std::runtime_error(
            std::format("Failed to get file mtime '{}': {}",
                       filepath, get_system_error_message()));
    }

    return st.st_mtime;
}

bool is_symbolic_link(const std::string& path) {
    struct stat st;
    if (lstat(path.c_str(), &st) != 0) {
        return false;
    }

    return S_ISLNK(st.st_mode);
}

std::string resolve_symbolic_link(const std::string& path) {
    char buffer[PATH_MAX];
    ssize_t len = readlink(path.c_str(), buffer, sizeof(buffer) - 1);

    if (len < 0) {
        throw std::runtime_error(
            std::format("Failed to resolve symbolic link '{}': {}",
                       path, get_system_error_message()));
    }

    buffer[len] = '\0';
    return std::string(buffer);
}

std::string get_real_path(const std::string& path) {
    char* resolved = realpath(path.c_str(), nullptr);
    if (!resolved) {
        throw std::runtime_error(
            std::format("Failed to get real path '{}': {}",
                       path, get_system_error_message()));
    }

    std::string result(resolved);
    free(resolved);
    return result;
}

bool is_network_path(const std::string& path) {
    struct statvfs vfs;
    if (statvfs(path.c_str(), &vfs) != 0) {
        return false;
    }

    // Check filesystem type
    // This is a simplified check - in practice, you might want to
    // check the filesystem type more thoroughly
    std::string real_path;
    try {
        real_path = get_real_path(path);
    } catch (...) {
        real_path = path;
    }

    // Check common network filesystem mount points
    return real_path.find("/mnt/") == 0 ||
           real_path.find("/media/") == 0 ||
           real_path.find("/net/") == 0;
}

std::vector<std::string> get_mounted_filesystems() {
    std::vector<std::string> filesystems;

    std::ifstream mounts("/proc/mounts");
    if (!mounts.is_open()) {
        return filesystems;
    }

    std::string line;
    while (std::getline(mounts, line)) {
        std::istringstream iss(line);
        std::string device, mount_point, fs_type;
        iss >> device >> mount_point >> fs_type;

        if (!mount_point.empty() && mount_point[0] == '/') {
            filesystems.push_back(mount_point);
        }
    }

    return filesystems;
}

std::string get_temp_directory() {
    // Try environment variables first
    const char* temp = getenv("TMPDIR");
    if (!temp) temp = getenv("TMP");
    if (!temp) temp = getenv("TEMP");
    if (!temp) temp = "/tmp";

    std::string temp_dir(temp);
    if (!temp_dir.empty() && temp_dir.back() != '/') {
        temp_dir += '/';
    }

    return temp_dir;
}

std::string create_temp_file(const std::string& prefix, const std::string& extension) {
    std::string temp_dir = get_temp_directory();

    // Generate random suffix
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(100000, 999999);

    std::string filename = std::format("{}{}{}", prefix, dis(gen), extension);
    std::string full_path = temp_dir + filename;

    // Create the file
    int fd = open(full_path.c_str(), O_CREAT | O_EXCL | O_WRONLY, 0600);
    if (fd < 0) {
        // Try again with different random number
        filename = std::format("{}{}{}", prefix, dis(gen), extension);
        full_path = temp_dir + filename;

        fd = open(full_path.c_str(), O_CREAT | O_EXCL | O_WRONLY, 0600);
        if (fd < 0) {
            throw std::runtime_error(
                std::format("Failed to create temp file: {}",
                           get_system_error_message()));
        }
    }

    close(fd);
    return full_path;
}

bool set_file_permissions(const std::string& filepath, mode_t mode) {
    return chmod(filepath.c_str(), mode) == 0;
}

mode_t get_file_permissions(const std::string& filepath) {
    struct stat st;
    if (stat(filepath.c_str(), &st) != 0) {
        throw std::runtime_error(
            std::format("Failed to get file permissions '{}': {}",
                       filepath, get_system_error_message()));
    }

    return st.st_mode & 0777;  // Return only permission bits
}

// UnixHighResTimer implementation

UnixHighResTimer::UnixHighResTimer() {
    reset();
}

void UnixHighResTimer::reset() {
    clock_gettime(CLOCK_MONOTONIC, &start_time_);
}

double UnixHighResTimer::elapsed_seconds() const {
    struct timespec current_time;
    clock_gettime(CLOCK_MONOTONIC, &current_time);

    double elapsed = (current_time.tv_sec - start_time_.tv_sec) +
                    (current_time.tv_nsec - start_time_.tv_nsec) / 1e9;
    return elapsed;
}

double UnixHighResTimer::elapsed_milliseconds() const {
    return elapsed_seconds() * 1000.0;
}

// Memory and process functions

MemoryInfo get_memory_info() {
    MemoryInfo info{};

#ifdef __linux__
    struct sysinfo si;
    if (sysinfo(&si) == 0) {
        info.total_physical = si.totalram * si.mem_unit;
        info.available_physical = si.freeram * si.mem_unit;
        info.total_swap = si.totalswap * si.mem_unit;
        info.available_swap = si.freeswap * si.mem_unit;
    }
#else
    // For other Unix systems, try to parse from system files or commands
    // This is a simplified implementation
    std::ifstream meminfo("/proc/meminfo");
    if (meminfo.is_open()) {
        std::string line;
        while (std::getline(meminfo, line)) {
            std::istringstream iss(line);
            std::string key;
            size_t value;
            std::string unit;

            iss >> key >> value >> unit;

            if (key == "MemTotal:") {
                info.total_physical = value * 1024;  // Convert from KB
            } else if (key == "MemAvailable:") {
                info.available_physical = value * 1024;
            } else if (key == "SwapTotal:") {
                info.total_swap = value * 1024;
            } else if (key == "SwapFree:") {
                info.available_swap = value * 1024;
            }
        }
    }
#endif

    return info;
}

bool set_process_priority(int priority) {
    return setpriority(PRIO_PROCESS, 0, priority) == 0;
}

int get_process_priority() {
    errno = 0;
    int priority = getpriority(PRIO_PROCESS, 0);

    if (priority == -1 && errno != 0) {
        throw std::runtime_error(
            std::format("Failed to get process priority: {}",
                       get_system_error_message()));
    }

    return priority;
}

bool lock_memory(void* addr, size_t size) {
    return mlock(addr, size) == 0;
}

bool unlock_memory(void* addr, size_t size) {
    return munlock(addr, size) == 0;
}

bool advise_memory_usage(void* addr, size_t size, int advice) {
    return madvise(addr, size, advice) == 0;
}

size_t get_cpu_count() {
    long count = sysconf(_SC_NPROCESSORS_ONLN);
    return (count > 0) ? static_cast<size_t>(count) : 1;
}

bool set_thread_affinity(const std::vector<int>& cpu_set) {
#ifdef __linux__
    cpu_set_t cpuset;
    CPU_ZERO(&cpuset);

    for (int cpu : cpu_set) {
        CPU_SET(cpu, &cpuset);
    }

    return pthread_setaffinity_np(pthread_self(), sizeof(cpuset), &cpuset) == 0;
#else
    // Not supported on non-Linux Unix systems
    return false;
#endif
}

} // namespace omop::extract::platform

#endif // !_WIN32

File src/lib/extract/platform/windows_utils.cpp:

/**
 * @file windows_utils.cpp
 * @brief Windows-specific utility functions implementation
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#ifdef _WIN32

#include "extract/platform/windows_utils.h"
#include "common/exceptions.h"
#include <format>
#include <vector>
#include <random>

namespace omop::extract::platform {

std::wstring utf8_to_wide(const std::string& utf8_str) {
    if (utf8_str.empty()) {
        return std::wstring();
    }

    int required_size = MultiByteToWideChar(CP_UTF8, 0,
                                           utf8_str.c_str(),
                                           static_cast<int>(utf8_str.length()),
                                           nullptr, 0);

    if (required_size == 0) {
        throw common::PlatformException(
            std::format("Failed to convert UTF-8 to wide string: {}",
                       get_windows_error_message()));
    }

    std::wstring wide_str(required_size, L'\0');
    int result = MultiByteToWideChar(CP_UTF8, 0,
                                    utf8_str.c_str(),
                                    static_cast<int>(utf8_str.length()),
                                    wide_str.data(),
                                    required_size);

    if (result == 0) {
        throw common::PlatformException(
            std::format("Failed to convert UTF-8 to wide string: {}",
                       get_windows_error_message()));
    }

    return wide_str;
}

std::string wide_to_utf8(const std::wstring& wide_str) {
    if (wide_str.empty()) {
        return std::string();
    }

    int required_size = WideCharToMultiByte(CP_UTF8, 0,
                                           wide_str.c_str(),
                                           static_cast<int>(wide_str.length()),
                                           nullptr, 0, nullptr, nullptr);

    if (required_size == 0) {
        throw common::PlatformException(
            std::format("Failed to convert wide string to UTF-8: {}",
                       get_windows_error_message()));
    }

    std::string utf8_str(required_size, '\0');
    int result = WideCharToMultiByte(CP_UTF8, 0,
                                    wide_str.c_str(),
                                    static_cast<int>(wide_str.length()),
                                    utf8_str.data(),
                                    required_size,
                                    nullptr, nullptr);

    if (result == 0) {
        throw common::PlatformException(
            std::format("Failed to convert wide string to UTF-8: {}",
                       get_windows_error_message()));
    }

    return utf8_str;
}

std::string get_windows_error_message(DWORD error_code) {
    if (error_code == 0) {
        error_code = GetLastError();
    }

    LPWSTR message_buffer = nullptr;
    DWORD size = FormatMessageW(
        FORMAT_MESSAGE_ALLOCATE_BUFFER |
        FORMAT_MESSAGE_FROM_SYSTEM |
        FORMAT_MESSAGE_IGNORE_INSERTS,
        nullptr,
        error_code,
        MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
        reinterpret_cast<LPWSTR>(&message_buffer),
        0,
        nullptr);

    if (size == 0) {
        return std::format("Unknown error code: {}", error_code);
    }

    std::wstring message(message_buffer, size);
    LocalFree(message_buffer);

    // Remove trailing newline characters
    while (!message.empty() &&
           (message.back() == L'\n' || message.back() == L'\r')) {
        message.pop_back();
    }

    return wide_to_utf8(message);
}

std::pair<WindowsFileHandle, WindowsFileHandle> create_file_mapping(const std::string& filepath) {
    std::wstring wide_path = utf8_to_wide(filepath);

    // Open file for reading
    WindowsFileHandle file_handle(CreateFileW(
        wide_path.c_str(),
        GENERIC_READ,
        FILE_SHARE_READ,
        nullptr,
        OPEN_EXISTING,
        FILE_ATTRIBUTE_NORMAL | FILE_FLAG_SEQUENTIAL_SCAN,
        nullptr));

    if (!file_handle.is_valid()) {
        throw common::PlatformException(
            std::format("Failed to open file '{}': {}",
                       filepath, get_windows_error_message()));
    }

    // Get file size
    LARGE_INTEGER file_size;
    if (!GetFileSizeEx(file_handle.get(), &file_size)) {
        throw common::PlatformException(
            std::format("Failed to get file size '{}': {}",
                       filepath, get_windows_error_message()));
    }

    // Create file mapping
    WindowsFileHandle mapping_handle(CreateFileMappingW(
        file_handle.get(),
        nullptr,
        PAGE_READONLY,
        file_size.HighPart,
        file_size.LowPart,
        nullptr));

    if (!mapping_handle.is_valid()) {
        throw common::PlatformException(
            std::format("Failed to create file mapping '{}': {}",
                       filepath, get_windows_error_message()));
    }

    return {std::move(file_handle), std::move(mapping_handle)};
}

void* map_view_of_file(HANDLE mapping_handle, size_t offset, size_t size) {
    LARGE_INTEGER offset_li;
    offset_li.QuadPart = offset;

    void* view = MapViewOfFile(
        mapping_handle,
        FILE_MAP_READ,
        offset_li.HighPart,
        offset_li.LowPart,
        size);

    if (!view) {
        throw common::PlatformException(
            std::format("Failed to map view of file: {}",
                       get_windows_error_message()));
    }

    return view;
}

bool unmap_view_of_file(void* view) {
    return UnmapViewOfFile(view) != 0;
}

size_t get_file_size(const std::string& filepath) {
    std::wstring wide_path = utf8_to_wide(filepath);

    WIN32_FILE_ATTRIBUTE_DATA file_info;
    if (!GetFileAttributesExW(wide_path.c_str(),
                             GetFileExInfoStandard,
                             &file_info)) {
        throw common::PlatformException(
            std::format("Failed to get file attributes '{}': {}",
                       filepath, get_windows_error_message()));
    }

    LARGE_INTEGER size;
    size.HighPart = file_info.nFileSizeHigh;
    size.LowPart = file_info.nFileSizeLow;

    return static_cast<size_t>(size.QuadPart);
}

bool is_network_path(const std::string& path) {
    // UNC paths start with \\
    if (path.length() >= 2 && path[0] == '\\' && path[1] == '\\') {
        return true;
    }

    // Check if drive is network mapped
    if (path.length() >= 2 && path[1] == ':') {
        std::string drive = path.substr(0, 2) + "\\";
        UINT drive_type = GetDriveTypeA(drive.c_str());
        return drive_type == DRIVE_REMOTE;
    }

    return false;
}

std::vector<char> get_available_drives() {
    std::vector<char> drives;
    DWORD drive_mask = GetLogicalDrives();

    for (char drive = 'A'; drive <= 'Z'; ++drive) {
        if (drive_mask & 1) {
            drives.push_back(drive);
        }
        drive_mask >>= 1;
    }

    return drives;
}

std::string get_temp_directory() {
    wchar_t temp_path[MAX_PATH + 1];
    DWORD result = GetTempPathW(MAX_PATH, temp_path);

    if (result == 0 || result > MAX_PATH) {
        throw common::PlatformException(
            std::format("Failed to get temp directory: {}",
                       get_windows_error_message()));
    }

    return wide_to_utf8(temp_path);
}

std::string create_temp_file(const std::string& prefix, const std::string& extension) {
    std::string temp_dir = get_temp_directory();

    // Generate random suffix
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(100000, 999999);

    std::string filename = std::format("{}{}{}", prefix, dis(gen), extension);
    std::string full_path = temp_dir + filename;

    // Create the file
    std::wstring wide_path = utf8_to_wide(full_path);
    WindowsFileHandle file(CreateFileW(
        wide_path.c_str(),
        GENERIC_WRITE,
        0,
        nullptr,
        CREATE_NEW,
        FILE_ATTRIBUTE_NORMAL,
        nullptr));

    if (!file.is_valid()) {
        // Try again with different random number
        filename = std::format("{}{}{}", prefix, dis(gen), extension);
        full_path = temp_dir + filename;
        wide_path = utf8_to_wide(full_path);

        file = WindowsFileHandle(CreateFileW(
            wide_path.c_str(),
            GENERIC_WRITE,
            0,
            nullptr,
            CREATE_NEW,
            FILE_ATTRIBUTE_NORMAL,
            nullptr));

        if (!file.is_valid()) {
            throw common::PlatformException(
                std::format("Failed to create temp file: {}",
                           get_windows_error_message()));
        }
    }

    return full_path;
}

bool set_file_attributes(const std::string& filepath, DWORD attributes) {
    std::wstring wide_path = utf8_to_wide(filepath);
    return SetFileAttributesW(wide_path.c_str(), attributes) != 0;
}

DWORD get_file_attributes(const std::string& filepath) {
    std::wstring wide_path = utf8_to_wide(filepath);
    return GetFileAttributesW(wide_path.c_str());
}

// WindowsHighResTimer implementation

WindowsHighResTimer::WindowsHighResTimer() {
    QueryPerformanceFrequency(&frequency_);
    reset();
}

void WindowsHighResTimer::reset() {
    QueryPerformanceCounter(&start_time_);
}

double WindowsHighResTimer::elapsed_seconds() const {
    LARGE_INTEGER current_time;
    QueryPerformanceCounter(&current_time);

    LONGLONG elapsed = current_time.QuadPart - start_time_.QuadPart;
    return static_cast<double>(elapsed) / frequency_.QuadPart;
}

double WindowsHighResTimer::elapsed_milliseconds() const {
    return elapsed_seconds() * 1000.0;
}

// Memory and process functions

MemoryInfo get_memory_info() {
    MEMORYSTATUSEX mem_status;
    mem_status.dwLength = sizeof(mem_status);

    if (!GlobalMemoryStatusEx(&mem_status)) {
        throw common::PlatformException(
            std::format("Failed to get memory info: {}",
                       get_windows_error_message()));
    }

    MemoryInfo info;
    info.total_physical = mem_status.ullTotalPhys;
    info.available_physical = mem_status.ullAvailPhys;
    info.total_virtual = mem_status.ullTotalVirtual;
    info.available_virtual = mem_status.ullAvailVirtual;

    return info;
}

bool set_process_priority(DWORD priority) {
    HANDLE process = GetCurrentProcess();
    return SetPriorityClass(process, priority) != 0;
}

bool enable_large_pages() {
    // Check if we have the required privilege
    HANDLE token;
    if (!OpenProcessToken(GetCurrentProcess(),
                         TOKEN_ADJUST_PRIVILEGES | TOKEN_QUERY,
                         &token)) {
        return false;
    }

    TOKEN_PRIVILEGES tp;
    tp.PrivilegeCount = 1;
    tp.Privileges[0].Attributes = SE_PRIVILEGE_ENABLED;

    if (!LookupPrivilegeValueW(nullptr, SE_LOCK_MEMORY_NAME,
                              &tp.Privileges[0].Luid)) {
        CloseHandle(token);
        return false;
    }

    BOOL result = AdjustTokenPrivileges(token, FALSE, &tp, 0, nullptr, nullptr);
    DWORD error = GetLastError();
    CloseHandle(token);

    return result && error == ERROR_SUCCESS;
}

} // namespace omop::extract::platform

#endif // _WIN32

File src/lib/extract/platform/windows_utils.h:

/**
 * @file windows_utils.h
 * @brief Windows-specific utility functions for the extract module
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This file contains Windows-specific implementations for file handling,
 * path operations, and system interactions.
 */

#pragma once

#ifdef _WIN32

#include <string>
#include <vector>
#include <memory>
#include <windows.h>

namespace omop::extract::platform {

/**
 * @brief Windows file handle wrapper with RAII
 */
class WindowsFileHandle {
public:
    /**
     * @brief Constructor
     * @param handle Windows file handle
     */
    explicit WindowsFileHandle(HANDLE handle = INVALID_HANDLE_VALUE) : handle_(handle) {}

    /**
     * @brief Destructor
     */
    ~WindowsFileHandle() {
        if (handle_ != INVALID_HANDLE_VALUE) {
            CloseHandle(handle_);
        }
    }

    // Delete copy operations
    WindowsFileHandle(const WindowsFileHandle&) = delete;
    WindowsFileHandle& operator=(const WindowsFileHandle&) = delete;

    // Move operations
    WindowsFileHandle(WindowsFileHandle&& other) noexcept : handle_(other.handle_) {
        other.handle_ = INVALID_HANDLE_VALUE;
    }

    WindowsFileHandle& operator=(WindowsFileHandle&& other) noexcept {
        if (this != &other) {
            if (handle_ != INVALID_HANDLE_VALUE) {
                CloseHandle(handle_);
            }
            handle_ = other.handle_;
            other.handle_ = INVALID_HANDLE_VALUE;
        }
        return *this;
    }

    /**
     * @brief Get raw handle
     * @return HANDLE Windows file handle
     */
    HANDLE get() const { return handle_; }

    /**
     * @brief Check if handle is valid
     * @return bool True if valid
     */
    bool is_valid() const { return handle_ != INVALID_HANDLE_VALUE; }

private:
    HANDLE handle_;
};

/**
 * @brief Convert UTF-8 string to wide string
 * @param utf8_str UTF-8 encoded string
 * @return std::wstring Wide string
 */
std::wstring utf8_to_wide(const std::string& utf8_str);

/**
 * @brief Convert wide string to UTF-8 string
 * @param wide_str Wide string
 * @return std::string UTF-8 encoded string
 */
std::string wide_to_utf8(const std::wstring& wide_str);

/**
 * @brief Get Windows error message
 * @param error_code Windows error code (default = GetLastError())
 * @return std::string Error message
 */
std::string get_windows_error_message(DWORD error_code = 0);

/**
 * @brief Create a memory-mapped file for reading
 * @param filepath File path
 * @return std::pair<WindowsFileHandle, WindowsFileHandle> File and mapping handles
 */
std::pair<WindowsFileHandle, WindowsFileHandle> create_file_mapping(const std::string& filepath);

/**
 * @brief Map file view into memory
 * @param mapping_handle Mapping handle
 * @param offset File offset
 * @param size Size to map (0 = entire file)
 * @return void* Mapped memory pointer
 */
void* map_view_of_file(HANDLE mapping_handle, size_t offset = 0, size_t size = 0);

/**
 * @brief Unmap file view from memory
 * @param view Mapped memory pointer
 * @return bool True if successful
 */
bool unmap_view_of_file(void* view);

/**
 * @brief Get file size
 * @param filepath File path
 * @return size_t File size in bytes
 */
size_t get_file_size(const std::string& filepath);

/**
 * @brief Check if path is a network path
 * @param path File path
 * @return bool True if network path
 */
bool is_network_path(const std::string& path);

/**
 * @brief Get available drive letters
 * @return std::vector<char> Available drive letters
 */
std::vector<char> get_available_drives();

/**
 * @brief Get temporary directory path
 * @return std::string Temporary directory path
 */
std::string get_temp_directory();

/**
 * @brief Create unique temporary file
 * @param prefix File name prefix
 * @param extension File extension
 * @return std::string Temporary file path
 */
std::string create_temp_file(const std::string& prefix = "omop_",
                           const std::string& extension = ".tmp");

/**
 * @brief Set file attributes
 * @param filepath File path
 * @param attributes File attributes (FILE_ATTRIBUTE_*)
 * @return bool True if successful
 */
bool set_file_attributes(const std::string& filepath, DWORD attributes);

/**
 * @brief Get file attributes
 * @param filepath File path
 * @return DWORD File attributes (INVALID_FILE_ATTRIBUTES on error)
 */
DWORD get_file_attributes(const std::string& filepath);

/**
 * @brief High-resolution timer using Windows performance counter
 */
class WindowsHighResTimer {
public:
    /**
     * @brief Constructor - starts timer
     */
    WindowsHighResTimer();

    /**
     * @brief Reset timer
     */
    void reset();

    /**
     * @brief Get elapsed time in seconds
     * @return double Elapsed time
     */
    double elapsed_seconds() const;

    /**
     * @brief Get elapsed time in milliseconds
     * @return double Elapsed time
     */
    double elapsed_milliseconds() const;

private:
    LARGE_INTEGER start_time_;
    LARGE_INTEGER frequency_;
};

/**
 * @brief Get system memory information
 */
struct MemoryInfo {
    size_t total_physical;     ///< Total physical memory
    size_t available_physical; ///< Available physical memory
    size_t total_virtual;      ///< Total virtual memory
    size_t available_virtual;  ///< Available virtual memory
};

/**
 * @brief Get system memory information
 * @return MemoryInfo Memory statistics
 */
MemoryInfo get_memory_info();

/**
 * @brief Set process priority
 * @param priority Priority class (e.g., NORMAL_PRIORITY_CLASS)
 * @return bool True if successful
 */
bool set_process_priority(DWORD priority);

/**
 * @brief Enable large page support for process
 * @return bool True if successful
 */
bool enable_large_pages();

} // namespace omop::extract::platform

#endif // _WIN32

File src/lib/extract/odbc_connector.cpp:

/**
 * @file odbc_connector.cpp
 * @brief ODBC database connector implementation
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "odbc_connector.h"
#include "common/logging.h"
#include "common/exceptions.h"
#include <format>
#include <chrono>
#include <ctime>
#include <sstream>
#include <iomanip>
#include <cstring>

namespace omop::extract {

// ODBC type mapping
static const std::unordered_map<SQLSMALLINT, std::string> SQL_TYPE_NAMES = {
    {SQL_CHAR, "CHAR"},
    {SQL_VARCHAR, "VARCHAR"},
    {SQL_LONGVARCHAR, "LONGVARCHAR"},
    {SQL_WCHAR, "WCHAR"},
    {SQL_WVARCHAR, "WVARCHAR"},
    {SQL_WLONGVARCHAR, "WLONGVARCHAR"},
    {SQL_DECIMAL, "DECIMAL"},
    {SQL_NUMERIC, "NUMERIC"},
    {SQL_SMALLINT, "SMALLINT"},
    {SQL_INTEGER, "INTEGER"},
    {SQL_REAL, "REAL"},
    {SQL_FLOAT, "FLOAT"},
    {SQL_DOUBLE, "DOUBLE"},
    {SQL_BIT, "BIT"},
    {SQL_TINYINT, "TINYINT"},
    {SQL_BIGINT, "BIGINT"},
    {SQL_BINARY, "BINARY"},
    {SQL_VARBINARY, "VARBINARY"},
    {SQL_LONGVARBINARY, "LONGVARBINARY"},
    {SQL_TYPE_DATE, "DATE"},
    {SQL_TYPE_TIME, "TIME"},
    {SQL_TYPE_TIMESTAMP, "TIMESTAMP"}
};

// OdbcResultSet implementation

OdbcResultSet::OdbcResultSet(std::shared_ptr<OdbcStatement> statement)
    : statement_(statement) {

    if (!statement_) {
        throw common::DatabaseException("Invalid ODBC statement", "ODBC", 0);
    }

    load_metadata();
    indicators_.resize(columns_.size(), SQL_NULL_DATA);
}

OdbcResultSet::~OdbcResultSet() {
    // Statement will be freed by shared_ptr
}

void OdbcResultSet::load_metadata() {
    SQLSMALLINT column_count;
    SQLRETURN ret = SQLNumResultCols(statement_->get(), &column_count);

    if (!SQL_SUCCEEDED(ret)) {
        throw common::DatabaseException("Failed to get column count", "ODBC", ret);
    }

    columns_.reserve(column_count);

    for (SQLSMALLINT i = 1; i <= column_count; ++i) {
        ColumnInfo info;
        SQLCHAR column_name[256];
        SQLSMALLINT name_length;

        ret = SQLDescribeCol(statement_->get(), i,
                            column_name, sizeof(column_name), &name_length,
                            &info.sql_type, &info.size,
                            &info.decimal_digits, &info.nullable);

        if (!SQL_SUCCEEDED(ret)) {
            throw common::DatabaseException(
                std::format("Failed to describe column {}", i), "ODBC", ret);
        }

        info.name = std::string(reinterpret_cast<char*>(column_name), name_length);
        columns_.push_back(info);
    }

    metadata_loaded_ = true;
}

bool OdbcResultSet::next() {
    SQLRETURN ret = SQLFetch(statement_->get());

    if (ret == SQL_NO_DATA) {
        return false;
    }

    if (!SQL_SUCCEEDED(ret)) {
        auto errors = OdbcDatabaseConnection::get_odbc_errors(SQL_HANDLE_STMT, statement_->get());
        std::string error_msg = "Failed to fetch row";
        if (!errors.empty()) {
            error_msg += ": " + errors[0].message;
        }
        throw common::DatabaseException(error_msg, "ODBC", ret);
    }

    return true;
}

std::any OdbcResultSet::get_value(size_t index) const {
    if (index >= columns_.size()) {
        throw common::DatabaseException(
            std::format("Column index {} out of range (0-{})", index, columns_.size() - 1),
            "ODBC", 0);
    }

    return convert_value(index);
}

std::any OdbcResultSet::get_value(const std::string& column_name) const {
    size_t index = get_column_index(column_name);
    return get_value(index);
}

bool OdbcResultSet::is_null(size_t index) const {
    if (index >= columns_.size()) {
        throw common::DatabaseException(
            std::format("Column index {} out of range", index),
            "ODBC", 0);
    }

    SQLLEN indicator;
    char dummy[1];
    SQLRETURN ret = SQLGetData(statement_->get(),
                              static_cast<SQLSMALLINT>(index + 1),
                              SQL_C_CHAR, dummy, 0, &indicator);

    if (!SQL_SUCCEEDED(ret) && ret != SQL_SUCCESS_WITH_INFO) {
        throw common::DatabaseException(
            std::format("Failed to check NULL status for column {}", index),
            "ODBC", ret);
    }

    return indicator == SQL_NULL_DATA;
}

bool OdbcResultSet::is_null(const std::string& column_name) const {
    size_t index = get_column_index(column_name);
    return is_null(index);
}

size_t OdbcResultSet::column_count() const {
    return columns_.size();
}

std::string OdbcResultSet::column_name(size_t index) const {
    if (index >= columns_.size()) {
        throw common::DatabaseException(
            std::format("Column index {} out of range", index),
            "ODBC", 0);
    }

    return columns_[index].name;
}

std::string OdbcResultSet::column_type(size_t index) const {
    if (index >= columns_.size()) {
        throw common::DatabaseException(
            std::format("Column index {} out of range", index),
            "ODBC", 0);
    }

    return OdbcDatabaseConnection::get_sql_type_name(columns_[index].sql_type);
}

size_t OdbcResultSet::get_column_index(const std::string& column_name) const {
    // Check cache first
    auto it = column_index_cache_.find(column_name);
    if (it != column_index_cache_.end()) {
        return it->second;
    }

    // Search for column
    for (size_t i = 0; i < columns_.size(); ++i) {
        if (columns_[i].name == column_name) {
            column_index_cache_[column_name] = i;
            return i;
        }
    }

    throw common::DatabaseException(
        std::format("Column '{}' not found", column_name),
        "ODBC", 0);
}

std::any OdbcResultSet::convert_value(size_t index) const {
    const ColumnInfo& col = columns_[index];
    SQLLEN indicator;

    // Check for NULL first
    if (is_null(index)) {
        return std::any{};
    }

    switch (col.sql_type) {
        case SQL_BIT:
        case SQL_TINYINT:
        case SQL_SMALLINT:
        case SQL_INTEGER: {
            SQLINTEGER value;
            SQLGetData(statement_->get(), static_cast<SQLSMALLINT>(index + 1),
                      SQL_C_SLONG, &value, sizeof(value), &indicator);
            return static_cast<int>(value);
        }

        case SQL_BIGINT: {
            SQLBIGINT value;
            SQLGetData(statement_->get(), static_cast<SQLSMALLINT>(index + 1),
                      SQL_C_SBIGINT, &value, sizeof(value), &indicator);
            return static_cast<long long>(value);
        }

        case SQL_REAL:
        case SQL_FLOAT:
        case SQL_DOUBLE: {
            SQLDOUBLE value;
            SQLGetData(statement_->get(), static_cast<SQLSMALLINT>(index + 1),
                      SQL_C_DOUBLE, &value, sizeof(value), &indicator);
            return static_cast<double>(value);
        }

        case SQL_TYPE_DATE: {
            SQL_DATE_STRUCT date;
            SQLGetData(statement_->get(), static_cast<SQLSMALLINT>(index + 1),
                      SQL_C_TYPE_DATE, &date, sizeof(date), &indicator);

            std::tm tm = {};
            tm.tm_year = date.year - 1900;
            tm.tm_mon = date.month - 1;
            tm.tm_mday = date.day;

            return std::chrono::system_clock::from_time_t(std::mktime(&tm));
        }

        case SQL_TYPE_TIME: {
            SQL_TIME_STRUCT time;
            SQLGetData(statement_->get(), static_cast<SQLSMALLINT>(index + 1),
                      SQL_C_TYPE_TIME, &time, sizeof(time), &indicator);

            std::tm tm = {};
            tm.tm_hour = time.hour;
            tm.tm_min = time.minute;
            tm.tm_sec = time.second;

            return std::chrono::system_clock::from_time_t(std::mktime(&tm));
        }

        case SQL_TYPE_TIMESTAMP: {
            SQL_TIMESTAMP_STRUCT timestamp;
            SQLGetData(statement_->get(), static_cast<SQLSMALLINT>(index + 1),
                      SQL_C_TYPE_TIMESTAMP, &timestamp, sizeof(timestamp), &indicator);

            std::tm tm = {};
            tm.tm_year = timestamp.year - 1900;
            tm.tm_mon = timestamp.month - 1;
            tm.tm_mday = timestamp.day;
            tm.tm_hour = timestamp.hour;
            tm.tm_min = timestamp.minute;
            tm.tm_sec = timestamp.second;

            auto time_point = std::chrono::system_clock::from_time_t(std::mktime(&tm));

            // Add fractional seconds
            if (timestamp.fraction > 0) {
                auto nanos = std::chrono::nanoseconds(timestamp.fraction);
                time_point += std::chrono::duration_cast<std::chrono::system_clock::duration>(nanos);
            }

            return time_point;
        }

        default: {
            // Default to string for all other types
            std::vector<char> buffer(col.size + 1);
            SQLGetData(statement_->get(), static_cast<SQLSMALLINT>(index + 1),
                      SQL_C_CHAR, buffer.data(), buffer.size(), &indicator);
            return std::string(buffer.data());
        }
    }
}

// OdbcPreparedStatement implementation

OdbcPreparedStatement::OdbcPreparedStatement(std::shared_ptr<OdbcConnection> connection,
                                           const std::string& sql)
    : connection_(connection), sql_(sql) {

    if (!connection_) {
        throw common::DatabaseException("Invalid connection", "ODBC", 0);
    }

    // Allocate statement handle
    statement_ = std::make_shared<OdbcStatement>(SQL_HANDLE_STMT, connection_->get());

    // Prepare statement
    SQLRETURN ret = SQLPrepare(statement_->get(),
                              reinterpret_cast<SQLCHAR*>(const_cast<char*>(sql_.c_str())),
                              SQL_NTS);

    if (!SQL_SUCCEEDED(ret)) {
        auto errors = OdbcDatabaseConnection::get_odbc_errors(SQL_HANDLE_STMT, statement_->get());
        std::string error_msg = "Failed to prepare statement";
        if (!errors.empty()) {
            error_msg += ": " + errors[0].message;
        }
        throw common::DatabaseException(error_msg, "ODBC", ret);
    }
}

OdbcPreparedStatement::~OdbcPreparedStatement() {
    // Statement will be freed by shared_ptr
}

void OdbcPreparedStatement::bind(size_t index, const std::any& value) {
    if (index == 0) {
        throw common::DatabaseException("Parameter index must be 1-based", "ODBC", 0);
    }

    ParameterBinding binding;
    binding.value = value;

    // Determine C and SQL types based on value type
    if (!value.has_value()) {
        binding.c_type = SQL_C_CHAR;
        binding.sql_type = SQL_VARCHAR;
        binding.indicator = SQL_NULL_DATA;
    }
    else if (value.type() == typeid(bool)) {
        binding.c_type = SQL_C_BIT;
        binding.sql_type = SQL_BIT;
        binding.buffer.resize(sizeof(unsigned char));
        *reinterpret_cast<unsigned char*>(binding.buffer.data()) =
            std::any_cast<bool>(value) ? 1 : 0;
        binding.indicator = sizeof(unsigned char);
    }
    else if (value.type() == typeid(int)) {
        binding.c_type = SQL_C_SLONG;
        binding.sql_type = SQL_INTEGER;
        binding.buffer.resize(sizeof(SQLINTEGER));
        *reinterpret_cast<SQLINTEGER*>(binding.buffer.data()) =
            std::any_cast<int>(value);
        binding.indicator = sizeof(SQLINTEGER);
    }
    else if (value.type() == typeid(long long)) {
        binding.c_type = SQL_C_SBIGINT;
        binding.sql_type = SQL_BIGINT;
        binding.buffer.resize(sizeof(SQLBIGINT));
        *reinterpret_cast<SQLBIGINT*>(binding.buffer.data()) =
            std::any_cast<long long>(value);
        binding.indicator = sizeof(SQLBIGINT);
    }
    else if (value.type() == typeid(double)) {
        binding.c_type = SQL_C_DOUBLE;
        binding.sql_type = SQL_DOUBLE;
        binding.buffer.resize(sizeof(SQLDOUBLE));
        *reinterpret_cast<SQLDOUBLE*>(binding.buffer.data()) =
            std::any_cast<double>(value);
        binding.indicator = sizeof(SQLDOUBLE);
    }
    else if (value.type() == typeid(std::string)) {
        binding.c_type = SQL_C_CHAR;
        binding.sql_type = SQL_VARCHAR;
        std::string str_val = std::any_cast<std::string>(value);
        binding.buffer.resize(str_val.length() + 1);
        std::strcpy(binding.buffer.data(), str_val.c_str());
        binding.indicator = SQL_NTS;
    }
    else if (value.type() == typeid(std::chrono::system_clock::time_point)) {
        binding.c_type = SQL_C_TYPE_TIMESTAMP;
        binding.sql_type = SQL_TYPE_TIMESTAMP;
        binding.buffer.resize(sizeof(SQL_TIMESTAMP_STRUCT));

        auto tp = std::any_cast<std::chrono::system_clock::time_point>(value);
        auto time_t_val = std::chrono::system_clock::to_time_t(tp);
        std::tm tm = *std::localtime(&time_t_val);

        auto timestamp = reinterpret_cast<SQL_TIMESTAMP_STRUCT*>(binding.buffer.data());
        timestamp->year = tm.tm_year + 1900;
        timestamp->month = tm.tm_mon + 1;
        timestamp->day = tm.tm_mday;
        timestamp->hour = tm.tm_hour;
        timestamp->minute = tm.tm_min;
        timestamp->second = tm.tm_sec;
        timestamp->fraction = 0;

        binding.indicator = sizeof(SQL_TIMESTAMP_STRUCT);
    }
    else {
        throw common::DatabaseException(
            "Unsupported parameter type for ODBC", "ODBC", 0);
    }

    parameters_[index] = std::move(binding);
    bind_parameter(index, parameters_[index]);
}

void OdbcPreparedStatement::bind_parameter(size_t index, ParameterBinding& binding) {
    SQLRETURN ret = SQLBindParameter(
        statement_->get(),
        static_cast<SQLSMALLINT>(index),
        SQL_PARAM_INPUT,
        binding.c_type,
        binding.sql_type,
        0,  // Column size - use default
        0,  // Decimal digits
        binding.indicator == SQL_NULL_DATA ? nullptr : binding.buffer.data(),
        binding.buffer.size(),
        &binding.indicator
    );

    if (!SQL_SUCCEEDED(ret)) {
        auto errors = OdbcDatabaseConnection::get_odbc_errors(SQL_HANDLE_STMT, statement_->get());
        std::string error_msg = std::format("Failed to bind parameter {}", index);
        if (!errors.empty()) {
            error_msg += ": " + errors[0].message;
        }
        throw common::DatabaseException(error_msg, "ODBC", ret);
    }
}

std::unique_ptr<IResultSet> OdbcPreparedStatement::execute_query() {
    // Bind all parameters
    for (auto& [index, binding] : parameters_) {
        bind_parameter(index, binding);
    }

    // Execute statement
    SQLRETURN ret = SQLExecute(statement_->get());

    if (!SQL_SUCCEEDED(ret)) {
        auto errors = OdbcDatabaseConnection::get_odbc_errors(SQL_HANDLE_STMT, statement_->get());
        std::string error_msg = "Query execution failed";
        if (!errors.empty()) {
            error_msg += ": " + errors[0].message;
        }
        throw common::DatabaseException(error_msg, "ODBC", ret);
    }

    return std::make_unique<OdbcResultSet>(statement_);
}

size_t OdbcPreparedStatement::execute_update() {
    // Bind all parameters
    for (auto& [index, binding] : parameters_) {
        bind_parameter(index, binding);
    }

    // Execute statement
    SQLRETURN ret = SQLExecute(statement_->get());

    if (!SQL_SUCCEEDED(ret)) {
        auto errors = OdbcDatabaseConnection::get_odbc_errors(SQL_HANDLE_STMT, statement_->get());
        std::string error_msg = "Update execution failed";
        if (!errors.empty()) {
            error_msg += ": " + errors[0].message;
        }
        throw common::DatabaseException(error_msg, "ODBC", ret);
    }

    // Get row count
    SQLLEN row_count;
    ret = SQLRowCount(statement_->get(), &row_count);

    if (!SQL_SUCCEEDED(ret)) {
        return 0;
    }

    return static_cast<size_t>(row_count);
}

void OdbcPreparedStatement::clear_parameters() {
    parameters_.clear();

    // Reset all parameter bindings
    SQLRETURN ret = SQLFreeStmt(statement_->get(), SQL_RESET_PARAMS);

    if (!SQL_SUCCEEDED(ret)) {
        auto logger = common::Logger::get("omop-odbc");
        logger->warn("Failed to reset parameters");
    }
}

// OdbcDatabaseConnection implementation

OdbcDatabaseConnection::OdbcDatabaseConnection() {
    // Allocate environment handle
    environment_ = std::make_shared<OdbcEnvironment>(SQL_HANDLE_ENV, static_cast<SQLHANDLE>(SQL_NULL_HANDLE));

    // Set ODBC version
    SQLRETURN ret = SQLSetEnvAttr(environment_->get(), SQL_ATTR_ODBC_VERSION,
                                  reinterpret_cast<SQLPOINTER>(SQL_OV_ODBC3), 0);

    if (!SQL_SUCCEEDED(ret)) {
        throw common::DatabaseException("Failed to set ODBC version", "ODBC", ret);
    }
}

OdbcDatabaseConnection::~OdbcDatabaseConnection() {
    disconnect();
}

void OdbcDatabaseConnection::connect(const ConnectionParams& params) {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (connected_) {
        disconnect();
    }

    // Allocate connection handle
    connection_ = std::make_shared<OdbcConnection>(SQL_HANDLE_DBC, environment_->get());

    // Build connection string
    std::string conn_str = build_connection_string(params);

    // Connect
    SQLCHAR out_conn_str[1024];
    SQLSMALLINT out_conn_str_len;

    SQLRETURN ret = SQLDriverConnect(
        connection_->get(),
        SQL_NULL_HANDLE,
        reinterpret_cast<SQLCHAR*>(const_cast<char*>(conn_str.c_str())),
        SQL_NTS,
        out_conn_str,
        sizeof(out_conn_str),
        &out_conn_str_len,
        SQL_DRIVER_NOPROMPT
    );

    if (!SQL_SUCCEEDED(ret)) {
        auto errors = get_odbc_errors(SQL_HANDLE_DBC, connection_->get());
        std::string error_msg = "Failed to connect to database";
        if (!errors.empty()) {
            error_msg += ": " + errors[0].message;
        }
        throw common::DatabaseException(error_msg, "ODBC", ret);
    }

    connected_ = true;
    database_name_ = params.database;

    // Get driver name
    SQLCHAR driver_name[256];
    SQLSMALLINT driver_name_len;
    SQLGetInfo(connection_->get(), SQL_DRIVER_NAME, driver_name,
              sizeof(driver_name), &driver_name_len);
    driver_name_ = std::string(reinterpret_cast<char*>(driver_name));

    auto logger = common::Logger::get("omop-odbc");
    logger->info("Connected to database '{}' using driver '{}'", database_name_, driver_name_);
}

void OdbcDatabaseConnection::disconnect() {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (connected_) {
        // Rollback any pending transaction
        if (in_transaction_) {
            try {
                rollback();
            } catch (...) {
                // Ignore errors during disconnect
            }
        }

        SQLDisconnect(connection_->get());
        connection_.reset();
        connected_ = false;

        auto logger = common::Logger::get("omop-odbc");
        logger->info("Disconnected from database '{}'", database_name_);
    }
}

bool OdbcDatabaseConnection::is_connected() const {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (!connected_ || !connection_) {
        return false;
    }

    // Check connection status
    SQLINTEGER dead;
    SQLRETURN ret = SQLGetConnectAttr(connection_->get(), SQL_ATTR_CONNECTION_DEAD,
                                     &dead, sizeof(dead), nullptr);

    return SQL_SUCCEEDED(ret) && dead == SQL_CD_FALSE;
}

std::unique_ptr<IResultSet> OdbcDatabaseConnection::execute_query(const std::string& sql) {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (!connected_) {
        throw common::DatabaseException("Not connected to database", "ODBC", 0);
    }

    auto statement = std::make_shared<OdbcStatement>(SQL_HANDLE_STMT, connection_->get());

    SQLRETURN ret = SQLExecDirect(statement->get(),
                                 reinterpret_cast<SQLCHAR*>(const_cast<char*>(sql.c_str())),
                                 SQL_NTS);

    check_error(ret, "execute_query", SQL_HANDLE_STMT, statement->get());

    return std::make_unique<OdbcResultSet>(statement);
}

size_t OdbcDatabaseConnection::execute_update(const std::string& sql) {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (!connected_) {
        throw common::DatabaseException("Not connected to database", "ODBC", 0);
    }

    auto statement = std::make_shared<OdbcStatement>(SQL_HANDLE_STMT, connection_->get());

    SQLRETURN ret = SQLExecDirect(statement->get(),
                                 reinterpret_cast<SQLCHAR*>(const_cast<char*>(sql.c_str())),
                                 SQL_NTS);

    check_error(ret, "execute_update", SQL_HANDLE_STMT, statement->get());

    // Get row count
    SQLLEN row_count;
    ret = SQLRowCount(statement->get(), &row_count);

    if (!SQL_SUCCEEDED(ret)) {
        return 0;
    }

    return static_cast<size_t>(row_count);
}

std::unique_ptr<IPreparedStatement> OdbcDatabaseConnection::prepare_statement(const std::string& sql) {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (!connected_) {
        throw common::DatabaseException("Not connected to database", "ODBC", 0);
    }

    return std::make_unique<OdbcPreparedStatement>(connection_, sql);
}

void OdbcDatabaseConnection::begin_transaction() {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (in_transaction_) {
        throw common::DatabaseException("Transaction already in progress", "ODBC", 0);
    }

    // Disable auto-commit
    SQLRETURN ret = SQLSetConnectAttr(connection_->get(), SQL_ATTR_AUTOCOMMIT,
                                     reinterpret_cast<SQLPOINTER>(SQL_AUTOCOMMIT_OFF), 0);

    check_error(ret, "begin_transaction", SQL_HANDLE_DBC, connection_->get());

    in_transaction_ = true;
}

void OdbcDatabaseConnection::commit() {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (!in_transaction_) {
        throw common::DatabaseException("No transaction in progress", "ODBC", 0);
    }

    SQLRETURN ret = SQLEndTran(SQL_HANDLE_DBC, connection_->get(), SQL_COMMIT);

    check_error(ret, "commit", SQL_HANDLE_DBC, connection_->get());

    // Re-enable auto-commit
    SQLSetConnectAttr(connection_->get(), SQL_ATTR_AUTOCOMMIT,
                     reinterpret_cast<SQLPOINTER>(SQL_AUTOCOMMIT_ON), 0);

    in_transaction_ = false;
}

void OdbcDatabaseConnection::rollback() {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (!in_transaction_) {
        throw common::DatabaseException("No transaction in progress", "ODBC", 0);
    }

    SQLRETURN ret = SQLEndTran(SQL_HANDLE_DBC, connection_->get(), SQL_ROLLBACK);

    check_error(ret, "rollback", SQL_HANDLE_DBC, connection_->get());

    // Re-enable auto-commit
    SQLSetConnectAttr(connection_->get(), SQL_ATTR_AUTOCOMMIT,
                     reinterpret_cast<SQLPOINTER>(SQL_AUTOCOMMIT_ON), 0);

    in_transaction_ = false;
}

std::string OdbcDatabaseConnection::get_database_type() const {
    if (!driver_name_.empty()) {
        // Identify common database types from driver name
        std::string lower_driver = driver_name_;
        std::transform(lower_driver.begin(), lower_driver.end(),
                      lower_driver.begin(), ::tolower);

        if (lower_driver.find("mysql") != std::string::npos) return "MySQL";
        if (lower_driver.find("postgres") != std::string::npos) return "PostgreSQL";
        if (lower_driver.find("sqlserver") != std::string::npos) return "SQL Server";
        if (lower_driver.find("oracle") != std::string::npos) return "Oracle";
        if (lower_driver.find("sqlite") != std::string::npos) return "SQLite";
    }

    return "ODBC";
}

std::string OdbcDatabaseConnection::get_version() const {
    if (!connected_) {
        return "Not connected";
    }

    SQLCHAR version[256];
    SQLSMALLINT version_len;

    SQLRETURN ret = SQLGetInfo(connection_->get(), SQL_DBMS_VER, version,
                              sizeof(version), &version_len);

    if (SQL_SUCCEEDED(ret)) {
        return std::string(reinterpret_cast<char*>(version));
    }

    return "Unknown";
}

void OdbcDatabaseConnection::set_query_timeout(int seconds) {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (connected_) {
        SQLRETURN ret = SQLSetConnectAttr(connection_->get(), SQL_ATTR_QUERY_TIMEOUT,
                                         reinterpret_cast<SQLPOINTER>(static_cast<SQLULEN>(seconds)), 0);

        if (!SQL_SUCCEEDED(ret)) {
            auto logger = common::Logger::get("omop-odbc");
            logger->warn("Failed to set query timeout");
        }
    }
}

bool OdbcDatabaseConnection::table_exists(const std::string& table_name,
                                        const std::string& schema) const {
    if (!connected_) {
        return false;
    }

    auto statement = std::make_shared<OdbcStatement>(SQL_HANDLE_STMT, connection_->get());

    SQLRETURN ret = SQLTables(statement->get(),
                            nullptr, 0,  // Catalog
                            schema.empty() ? nullptr : reinterpret_cast<SQLCHAR*>(const_cast<char*>(schema.c_str())),
                            schema.empty() ? 0 : SQL_NTS,
                            reinterpret_cast<SQLCHAR*>(const_cast<char*>(table_name.c_str())), SQL_NTS,
                            reinterpret_cast<SQLCHAR*>(const_cast<char*>("TABLE")), SQL_NTS);

    if (!SQL_SUCCEEDED(ret)) {
        return false;
    }

    // Check if any rows returned
    ret = SQLFetch(statement->get());
    return ret == SQL_SUCCESS || ret == SQL_SUCCESS_WITH_INFO;
}

std::vector<OdbcError> OdbcDatabaseConnection::get_odbc_errors(SQLSMALLINT handle_type,
                                                              SQLHANDLE handle) {
    std::vector<OdbcError> errors;
    SQLSMALLINT rec_num = 1;

    while (true) {
        OdbcError error;
        SQLCHAR state[6];
        SQLCHAR message[1024];
        SQLSMALLINT message_len;

        SQLRETURN ret = SQLGetDiagRec(handle_type, handle, rec_num,
                                     state, &error.native_error,
                                     message, sizeof(message), &message_len);

        if (ret == SQL_NO_DATA) {
            break;
        }

        if (SQL_SUCCEEDED(ret)) {
            error.state = std::string(reinterpret_cast<char*>(state));
            error.message = std::string(reinterpret_cast<char*>(message));
            errors.push_back(error);
        }

        rec_num++;
    }

    return errors;
}

std::string OdbcDatabaseConnection::build_connection_string(const ConnectionParams& params) const {
    // Check if DSN is provided
    auto dsn_it = params.options.find("DSN");
    if (dsn_it != params.options.end()) {
        return build_dsn_string(params);
    }

    // Build driver-based connection string
    std::stringstream conn_str;

    // Driver
    auto driver_it = params.options.find("Driver");
    if (driver_it != params.options.end()) {
        conn_str << "Driver={" << driver_it->second << "};";
    } else {
        throw common::DatabaseException("No Driver or DSN specified", "ODBC", 0);
    }

    // Server/Host
    if (!params.host.empty()) {
        conn_str << "Server=" << params.host << ";";
    }

    // Port
    if (params.port > 0) {
        conn_str << "Port=" << params.port << ";";
    }

    // Database
    if (!params.database.empty()) {
        conn_str << "Database=" << params.database << ";";
    }

    // Username
    if (!params.username.empty()) {
        conn_str << "UID=" << params.username << ";";
    }

    // Password
    if (!params.password.empty()) {
        conn_str << "PWD=" << params.password << ";";
    }

    // Additional options
    for (const auto& [key, value] : params.options) {
        if (key != "Driver") {  // Skip Driver as it's already added
            conn_str << key << "=" << value << ";";
        }
    }

    return conn_str.str();
}

std::string OdbcDatabaseConnection::build_dsn_string(const ConnectionParams& params) const {
    std::stringstream conn_str;

    // DSN
    auto dsn_it = params.options.find("DSN");
    conn_str << "DSN=" << dsn_it->second << ";";

    // Username
    if (!params.username.empty()) {
        conn_str << "UID=" << params.username << ";";
    }

    // Password
    if (!params.password.empty()) {
        conn_str << "PWD=" << params.password << ";";
    }

    return conn_str.str();
}

void OdbcDatabaseConnection::check_error(SQLRETURN ret, const std::string& operation,
                                       SQLSMALLINT handle_type, SQLHANDLE handle) const {
    if (!SQL_SUCCEEDED(ret)) {
        auto errors = get_odbc_errors(handle_type, handle);
        std::string error_msg = std::format("{} failed", operation);

        if (!errors.empty()) {
            error_msg += ": " + errors[0].message;
            error_msg += " (SQLSTATE: " + errors[0].state + ")";
        }

        throw common::DatabaseException(error_msg, "ODBC", ret);
    }
}

std::string OdbcDatabaseConnection::get_sql_type_name(SQLSMALLINT sql_type) {
    auto it = SQL_TYPE_NAMES.find(sql_type);
    if (it != SQL_TYPE_NAMES.end()) {
        return it->second;
    }
    return std::format("SQL_TYPE_{}", sql_type);
}

// OdbcExtractor implementation

std::string OdbcExtractor::build_query() const {
    // Use parent implementation and add ODBC-specific optimizations
    std::string query = DatabaseExtractor::build_query();

    // Add ODBC-specific optimizations based on database type
    auto db_type = connection_->get_database_type();

    if (db_type == "SQL Server") {
        // Add NOLOCK hint for read-only queries
        size_t from_pos = query.find(" FROM ");
        if (from_pos != std::string::npos) {
            size_t table_end = query.find(" ", from_pos + 6);
            if (table_end != std::string::npos) {
                query.insert(table_end, " WITH (NOLOCK)");
            }
        }
    }

    return query;
}

// OdbcDriverManager implementation

std::vector<OdbcDriverManager::DriverInfo> OdbcDriverManager::get_available_drivers() {
    std::vector<DriverInfo> drivers;

    OdbcEnvironment env(SQL_HANDLE_ENV, static_cast<SQLHANDLE>(SQL_NULL_HANDLE));
    SQLSetEnvAttr(env.get(), SQL_ATTR_ODBC_VERSION,
                 reinterpret_cast<SQLPOINTER>(SQL_OV_ODBC3), 0);

    SQLCHAR driver[256];
    SQLCHAR attrs[256];
    SQLSMALLINT driver_len, attrs_len;
    SQLUSMALLINT direction = SQL_FETCH_FIRST;

    while (SQL_SUCCEEDED(SQLDrivers(env.get(), direction, driver, sizeof(driver),
                                   &driver_len, attrs, sizeof(attrs), &attrs_len))) {
        DriverInfo info;
        info.name = std::string(reinterpret_cast<char*>(driver));

        // Parse attributes (key=value pairs separated by null)
        std::string attr_str(reinterpret_cast<char*>(attrs), attrs_len);
        size_t pos = 0;
        while (pos < attr_str.length()) {
            size_t end = attr_str.find('\0', pos);
            if (end == std::string::npos) end = attr_str.length();

            std::string pair = attr_str.substr(pos, end - pos);
            size_t eq_pos = pair.find('=');
            if (eq_pos != std::string::npos) {
                info.attributes[pair.substr(0, eq_pos)] = pair.substr(eq_pos + 1);
            }

            pos = end + 1;
        }

        drivers.push_back(info);
        direction = SQL_FETCH_NEXT;
    }

    return drivers;
}

std::vector<OdbcDriverManager::DataSourceInfo> OdbcDriverManager::get_data_sources() {
    std::vector<DataSourceInfo> sources;

    OdbcEnvironment env(SQL_HANDLE_ENV, static_cast<SQLHANDLE>(SQL_NULL_HANDLE));
    SQLSetEnvAttr(env.get(), SQL_ATTR_ODBC_VERSION,
                 reinterpret_cast<SQLPOINTER>(SQL_OV_ODBC3), 0);

    SQLCHAR dsn[256];
    SQLCHAR desc[256];
    SQLSMALLINT dsn_len, desc_len;
    SQLUSMALLINT direction = SQL_FETCH_FIRST;

    while (SQL_SUCCEEDED(SQLDataSources(env.get(), direction, dsn, sizeof(dsn),
                                       &dsn_len, desc, sizeof(desc), &desc_len))) {
        DataSourceInfo info;
        info.name = std::string(reinterpret_cast<char*>(dsn));
        info.description = std::string(reinterpret_cast<char*>(desc));

        sources.push_back(info);
        direction = SQL_FETCH_NEXT;
    }

    return sources;
}

std::pair<bool, std::string> OdbcDriverManager::test_connection(const std::string& connection_string) {
    try {
        OdbcEnvironment env(SQL_HANDLE_ENV, static_cast<SQLHANDLE>(SQL_NULL_HANDLE));
        SQLSetEnvAttr(env.get(), SQL_ATTR_ODBC_VERSION,
                     reinterpret_cast<SQLPOINTER>(SQL_OV_ODBC3), 0);

        OdbcConnection conn(SQL_HANDLE_DBC, env.get());

        SQLCHAR out_conn_str[1024];
        SQLSMALLINT out_conn_str_len;

        SQLRETURN ret = SQLDriverConnect(
            conn.get(),
            SQL_NULL_HANDLE,
            reinterpret_cast<SQLCHAR*>(const_cast<char*>(connection_string.c_str())),
            SQL_NTS,
            out_conn_str,
            sizeof(out_conn_str),
            &out_conn_str_len,
            SQL_DRIVER_NOPROMPT
        );

        if (SQL_SUCCEEDED(ret)) {
            SQLDisconnect(conn.get());
            return {true, "Connection successful"};
        } else {
            auto errors = OdbcDatabaseConnection::get_odbc_errors(SQL_HANDLE_DBC, conn.get());
            std::string error_msg = "Connection failed";
            if (!errors.empty()) {
                error_msg += ": " + errors[0].message;
            }
            return {false, error_msg};
        }

    } catch (const std::exception& e) {
        return {false, std::string("Exception: ") + e.what()};
    }
}

} // namespace omop::extract

File src/lib/extract/extract.h:

/**
 * @file extract.h
 * @brief Comprehensive header for the OMOP ETL extract module
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This header provides convenient access to all extraction functionality
 * within the OMOP ETL pipeline, including various data source extractors
 * and supporting utilities.
 */

#pragma once

// Core interfaces
#include "core/interfaces.h"

// Base extractor class (includes schema definitions)
#include "extract/extractor_base.h"

// Extractor factory functionality
#include "extract/extractor_factory.h"

// File-based extractors
#include "extract/csv_extractor.h"
#include "extract/json_extractor.h"

// Database extractors
#include "extract/database_connector.h"
#include "extract/postgresql_connector.h"
#ifdef OMOP_HAS_MYSQL
#include "extract/mysql_connector.h"
#endif
#ifdef OMOP_HAS_ODBC
#include "extract/odbc_connector.h"
#endif

// Extraction utilities
#include "extract/extract_utils.h"

// Common utilities
#include "common/exceptions.h"
#include "common/logging.h"
#include <mutex>

/**
 * @namespace omop::extract
 * @brief Data extraction functionality for the OMOP ETL pipeline
 *
 * The extract namespace contains all components responsible for extracting
 * data from various sources including files (CSV, JSON) and databases
 * (PostgreSQL, MySQL, ODBC). The module provides a unified interface for
 * data extraction with support for batch processing, error handling, and
 * progress monitoring.
 */
namespace omop::extract {

// All utility functions and classes are now defined in extract_utils.h

} // namespace omop::extract

File src/lib/extract/connection_pool.cpp:

/**
 * @file connection_pool.cpp
 * @brief Implementation of database connection pooling
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "extract/database_connector.h"
#include "common/logging.h"
#include "common/exceptions.h"
#include <chrono>
#include <thread>
#include <queue>
#include <condition_variable>
#include <shared_mutex>
#include <format>

namespace omop::extract {

/**
 * @brief RAII wrapper for connection pool connections
 *
 * Automatically returns connection to pool when destroyed
 */
class PooledConnection {
public:
    /**
     * @brief Constructor
     * @param connection Database connection
     * @param pool Connection pool reference
     */
    PooledConnection(std::unique_ptr<IDatabaseConnection> connection,
                     ConnectionPool* pool)
        : connection_(std::move(connection)), pool_(pool) {}

    /**
     * @brief Destructor - returns connection to pool
     */
    ~PooledConnection() {
        if (connection_ && pool_) {
            pool_->release(std::move(connection_));
        }
    }

    /**
     * @brief Move constructor
     */
    PooledConnection(PooledConnection&& other) noexcept
        : connection_(std::move(other.connection_)), pool_(other.pool_) {
        other.pool_ = nullptr;
    }

    /**
     * @brief Move assignment operator
     */
    PooledConnection& operator=(PooledConnection&& other) noexcept {
        if (this != &other) {
            if (connection_ && pool_) {
                pool_->release(std::move(connection_));
            }
            connection_ = std::move(other.connection_);
            pool_ = other.pool_;
            other.pool_ = nullptr;
        }
        return *this;
    }

    // Delete copy operations
    PooledConnection(const PooledConnection&) = delete;
    PooledConnection& operator=(const PooledConnection&) = delete;

    /**
     * @brief Get connection pointer
     * @return IDatabaseConnection* Connection pointer
     */
    IDatabaseConnection* get() { return connection_.get(); }

    /**
     * @brief Dereference operator
     * @return IDatabaseConnection& Connection reference
     */
    IDatabaseConnection& operator*() { return *connection_; }

    /**
     * @brief Arrow operator
     * @return IDatabaseConnection* Connection pointer
     */
    IDatabaseConnection* operator->() { return connection_.get(); }

    /**
     * @brief Check if connection is valid
     * @return bool True if connection exists
     */
    explicit operator bool() const { return connection_ != nullptr; }

private:
    std::unique_ptr<IDatabaseConnection> connection_;
    ConnectionPool* pool_;
};

/**
 * @brief Connection health checker
 *
 * Periodically validates idle connections and removes invalid ones
 */
class ConnectionHealthChecker {
public:
    /**
     * @brief Constructor
     * @param pool Connection pool to monitor
     * @param check_interval Interval between health checks (seconds)
     */
    ConnectionHealthChecker(ConnectionPool* pool, int check_interval = 60)
        : pool_(pool), check_interval_(check_interval), running_(true) {

        health_check_thread_ = std::thread(&ConnectionHealthChecker::run, this);
    }

    /**
     * @brief Destructor
     */
    ~ConnectionHealthChecker() {
        running_ = false;
        cv_.notify_all();

        if (health_check_thread_.joinable()) {
            health_check_thread_.join();
        }
    }

    /**
     * @brief Stop health checking
     */
    void stop() {
        running_ = false;
        cv_.notify_all();
    }

private:
    /**
     * @brief Health check thread function
     */
    void run() {
        auto logger = common::Logger::get("omop-connection-health");

        while (running_) {
            std::unique_lock<std::mutex> lock(mutex_);
            cv_.wait_for(lock, std::chrono::seconds(check_interval_),
                        [this] { return !running_; });

            if (!running_) break;

            try {
                size_t invalid_count = pool_->validate_connections();
                if (invalid_count > 0) {
                    logger->info("Removed {} invalid connections from pool", invalid_count);
                }
            } catch (const std::exception& e) {
                logger->error("Error during connection validation: {}", e.what());
            }
        }
    }

    ConnectionPool* pool_;
    int check_interval_;
    std::atomic<bool> running_;
    std::thread health_check_thread_;
    std::mutex mutex_;
    std::condition_variable cv_;
};

/**
 * @brief Advanced connection pool with monitoring and auto-scaling
 */
class AdvancedConnectionPool : public ConnectionPool {
public:
    /**
     * @brief Pool configuration
     */
    struct PoolConfig {
        size_t min_connections{5};
        size_t max_connections{50};
        std::function<std::unique_ptr<IDatabaseConnection>()> connection_factory;
        bool enable_health_checks{true};
        int health_check_interval{60};  // seconds
        double scale_up_threshold{0.8};
        double scale_down_threshold{0.2};
        double scale_factor{1.5};
        int connection_timeout_ms{5000};
        int max_retry_attempts{3};
    };

    /**
     * @brief Constructor
     * @param config Pool configuration
     */
    explicit AdvancedConnectionPool(const PoolConfig& config)
        : ConnectionPool(config.min_connections, config.max_connections,
                        config.connection_factory),
          config_(config) {

        if (config.enable_health_checks) {
            health_checker_ = std::make_unique<ConnectionHealthChecker>(
                this, config.health_check_interval);
        }

        start_time_ = std::chrono::steady_clock::now();
    }

    /**
     * @brief Get detailed pool metrics
     * @return PoolMetrics Detailed metrics
     */
    struct PoolMetrics {
        ConnectionPool::PoolStats basic_stats;
        double avg_connection_lifetime_seconds;
        double avg_acquisition_rate_per_minute;
        size_t connection_errors;
        size_t timeout_errors;
        std::chrono::seconds uptime;
    };

    PoolMetrics get_metrics() const {
        PoolMetrics metrics;
        metrics.basic_stats = get_statistics();

        auto current_time = std::chrono::steady_clock::now();
        metrics.uptime = std::chrono::duration_cast<std::chrono::seconds>(
            current_time - start_time_);

        if (metrics.uptime.count() > 0) {
            double minutes = metrics.uptime.count() / 60.0;
            metrics.avg_acquisition_rate_per_minute =
                metrics.basic_stats.total_acquisitions / minutes;
        }

        metrics.connection_errors = connection_errors_.load();
        metrics.timeout_errors = timeout_errors_.load();

        return metrics;
    }

    /**
     * @brief Auto-scale pool based on usage patterns
     */
    void auto_scale() {
        auto stats = get_statistics();

        // Calculate utilization
        double utilization = 0.0;
        if (stats.total_connections > 0) {
            utilization = static_cast<double>(stats.active_connections) /
                         stats.total_connections;
        }

        // Scale up if high utilization
        if (utilization > config_.scale_up_threshold) {
            size_t new_size = std::min(
                static_cast<size_t>(stats.total_connections * config_.scale_factor),
                config_.max_connections
            );
            resize_pool(new_size);
        }
        // Scale down if low utilization
        else if (utilization < config_.scale_down_threshold) {
            size_t new_size = std::max(
                static_cast<size_t>(stats.total_connections / config_.scale_factor),
                config_.min_connections
            );
            resize_pool(new_size);
        }
    }



private:
    /**
     * @brief Resize connection pool
     * @param new_size Target pool size
     */
    void resize_pool(size_t new_size) {
        auto logger = common::Logger::get("omop-connection-pool");
        auto current_stats = get_statistics();

        if (new_size == current_stats.total_connections) {
            return;
        }

        logger->info("Resizing connection pool from {} to {} connections",
                    current_stats.total_connections, new_size);

        if (new_size > current_stats.total_connections) {
            // Add connections
            size_t to_add = new_size - current_stats.total_connections;
            for (size_t i = 0; i < to_add; ++i) {
                try {
                    release(config_.connection_factory());
                } catch (const std::exception& e) {
                    logger->error("Failed to create connection: {}", e.what());
                    connection_errors_++;
                }
            }
        } else {
            // Remove idle connections
            size_t to_remove = current_stats.total_connections - new_size;
            for (size_t i = 0; i < to_remove && current_stats.idle_connections > 0; ++i) {
                clear_idle_connections();
            }
        }
    }

    PoolConfig config_;
    std::unique_ptr<ConnectionHealthChecker> health_checker_;
    std::chrono::steady_clock::time_point start_time_;
    std::atomic<size_t> connection_errors_{0};
    std::atomic<size_t> timeout_errors_{0};
};

/**
 * @brief Connection pool manager for multiple databases
 *
 * Manages connection pools for different database instances
 */
class ConnectionPoolManager {
public:
    /**
     * @brief Get singleton instance
     * @return ConnectionPoolManager& Instance reference
     */
    static ConnectionPoolManager& instance() {
        static ConnectionPoolManager instance;
        return instance;
    }

    /**
     * @brief Create or get connection pool
     * @param name Pool name
     * @param config Pool configuration
     * @return std::shared_ptr<ConnectionPool> Connection pool
     */
    std::shared_ptr<ConnectionPool> get_pool(
        const std::string& name,
        const AdvancedConnectionPool::PoolConfig& config) {

        std::unique_lock<std::shared_mutex> lock(mutex_);

        auto it = pools_.find(name);
        if (it != pools_.end()) {
            return it->second;
        }

        auto pool = std::make_shared<AdvancedConnectionPool>(config);
        pools_[name] = pool;

        auto logger = common::Logger::get("omop-pool-manager");
        logger->info("Created connection pool '{}'", name);

        return pool;
    }

    /**
     * @brief Get existing pool
     * @param name Pool name
     * @return std::shared_ptr<ConnectionPool> Connection pool or nullptr
     */
    std::shared_ptr<ConnectionPool> get_pool(const std::string& name) const {
        std::shared_lock<std::shared_mutex> lock(mutex_);

        auto it = pools_.find(name);
        return (it != pools_.end()) ? it->second : nullptr;
    }

    /**
     * @brief Remove connection pool
     * @param name Pool name
     */
    void remove_pool(const std::string& name) {
        std::unique_lock<std::shared_mutex> lock(mutex_);

        auto it = pools_.find(name);
        if (it != pools_.end()) {
            pools_.erase(it);

            auto logger = common::Logger::get("omop-pool-manager");
            logger->info("Removed connection pool '{}'", name);
        }
    }

    /**
     * @brief Get all pool names
     * @return std::vector<std::string> Pool names
     */
    std::vector<std::string> get_pool_names() const {
        std::shared_lock<std::shared_mutex> lock(mutex_);

        std::vector<std::string> names;
        names.reserve(pools_.size());

        for (const auto& [name, _] : pools_) {
            names.push_back(name);
        }

        return names;
    }

    /**
     * @brief Get metrics for all pools
     * @return std::unordered_map<std::string, AdvancedConnectionPool::PoolMetrics> Metrics
     */
    std::unordered_map<std::string, AdvancedConnectionPool::PoolMetrics>
    get_all_metrics() const {
        std::shared_lock<std::shared_mutex> lock(mutex_);

        std::unordered_map<std::string, AdvancedConnectionPool::PoolMetrics> metrics;

        for (const auto& [name, pool] : pools_) {
            if (auto advanced_pool =
                std::dynamic_pointer_cast<AdvancedConnectionPool>(pool)) {
                metrics[name] = advanced_pool->get_metrics();
            }
        }

        return metrics;
    }

    /**
     * @brief Clear all pools
     */
    void clear_all() {
        std::unique_lock<std::shared_mutex> lock(mutex_);
        pools_.clear();

        auto logger = common::Logger::get("omop-pool-manager");
        logger->info("Cleared all connection pools");
    }

private:
    ConnectionPoolManager() = default;
    ~ConnectionPoolManager() = default;

    // Delete copy/move operations
    ConnectionPoolManager(const ConnectionPoolManager&) = delete;
    ConnectionPoolManager& operator=(const ConnectionPoolManager&) = delete;
    ConnectionPoolManager(ConnectionPoolManager&&) = delete;
    ConnectionPoolManager& operator=(ConnectionPoolManager&&) = delete;

    mutable std::shared_mutex mutex_;
    std::unordered_map<std::string, std::shared_ptr<ConnectionPool>> pools_;
};

} // namespace omop::extract

File src/lib/extract/database_connector.h:

#pragma once

#include <memory>
#include <string>
#include <vector>
#include <optional>
#include <any>
#include <functional>
#include <format>
#include <unordered_map>
#include <random>
#include <chrono>
#include <thread>
#include <condition_variable>
#include <queue>
#include <format>
#include "core/interfaces.h"
#include "common/exceptions.h"

namespace omop::extract {

/**
 * @brief Result set interface for database queries
 *
 * This interface provides access to query results in a database-agnostic manner.
 * Implementations handle the specifics of each database system.
 */
class IResultSet {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~IResultSet() = default;

    /**
     * @brief Move to next row
     * @return bool True if successful, false if no more rows
     */
    virtual bool next() = 0;

    /**
     * @brief Get column value by index
     * @param index Column index (0-based)
     * @return std::any Column value
     */
    virtual std::any get_value(size_t index) const = 0;

    /**
     * @brief Get column value by name
     * @param column_name Column name
     * @return std::any Column value
     */
    virtual std::any get_value(const std::string& column_name) const = 0;

    /**
     * @brief Check if column value is NULL
     * @param index Column index
     * @return bool True if NULL
     */
    virtual bool is_null(size_t index) const = 0;

    /**
     * @brief Check if column value is NULL
     * @param column_name Column name
     * @return bool True if NULL
     */
    virtual bool is_null(const std::string& column_name) const = 0;

    /**
     * @brief Get column count
     * @return size_t Number of columns
     */
    virtual size_t column_count() const = 0;

    /**
     * @brief Get column name by index
     * @param index Column index
     * @return std::string Column name
     */
    virtual std::string column_name(size_t index) const = 0;

    /**
     * @brief Get column type by index
     * @param index Column index
     * @return std::string Column type name
     */
    virtual std::string column_type(size_t index) const = 0;

    /**
     * @brief Convert current row to Record
     * @return core::Record Record representation of current row
     */
    virtual core::Record to_record() const = 0;
};

/**
 * @brief Prepared statement interface
 *
 * This interface provides parameterized query execution capabilities
 * for safe and efficient database operations.
 */
class IPreparedStatement {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~IPreparedStatement() = default;

    /**
     * @brief Bind parameter by index
     * @param index Parameter index (1-based)
     * @param value Parameter value
     */
    virtual void bind(size_t index, const std::any& value) = 0;

    /**
     * @brief Execute query and return result set
     * @return std::unique_ptr<IResultSet> Query results
     */
    virtual std::unique_ptr<IResultSet> execute_query() = 0;

    /**
     * @brief Execute update/insert/delete
     * @return size_t Number of affected rows
     */
    virtual size_t execute_update() = 0;

    /**
     * @brief Clear all bound parameters
     */
    virtual void clear_parameters() = 0;
};

/**
 * @brief Database connection interface
 *
 * This interface defines the contract for database connections,
 * providing a unified API for different database systems.
 */
class IDatabaseConnection {
public:
    /**
     * @brief Connection parameters
     */
    struct ConnectionParams {
        std::string host;
        int port;
        std::string database;
        std::string username;
        std::string password;
        std::unordered_map<std::string, std::string> options;
    };

    /**
     * @brief Virtual destructor
     */
    virtual ~IDatabaseConnection() = default;

    /**
     * @brief Connect to database
     * @param params Connection parameters
     */
    virtual void connect(const ConnectionParams& params) = 0;

    /**
     * @brief Disconnect from database
     */
    virtual void disconnect() = 0;

    /**
     * @brief Check if connected
     * @return bool True if connected
     */
    virtual bool is_connected() const = 0;

    /**
     * @brief Execute SQL query
     * @param sql SQL query string
     * @return std::unique_ptr<IResultSet> Query results
     */
    virtual std::unique_ptr<IResultSet> execute_query(const std::string& sql) = 0;

    /**
     * @brief Execute SQL update/insert/delete
     * @param sql SQL statement
     * @return size_t Number of affected rows
     */
    virtual size_t execute_update(const std::string& sql) = 0;

    /**
     * @brief Prepare SQL statement
     * @param sql SQL statement with parameter placeholders
     * @return std::unique_ptr<IPreparedStatement> Prepared statement
     */
    virtual std::unique_ptr<IPreparedStatement> prepare_statement(const std::string& sql) = 0;

    /**
     * @brief Begin transaction
     */
    virtual void begin_transaction() = 0;

    /**
     * @brief Commit transaction
     */
    virtual void commit() = 0;

    /**
     * @brief Rollback transaction
     */
    virtual void rollback() = 0;

    /**
     * @brief Get database type name
     * @return std::string Database type
     */
    virtual std::string get_database_type() const = 0;

    /**
     * @brief Get database version
     * @return std::string Database version string
     */
    virtual std::string get_version() const = 0;

    /**
     * @brief Set query timeout
     * @param seconds Timeout in seconds
     */
    virtual void set_query_timeout(int seconds) = 0;

    /**
     * @brief Check if table exists
     * @param table_name Table name
     * @param schema Schema name (optional)
     * @return bool True if table exists
     */
    virtual bool table_exists(const std::string& table_name,
                            const std::string& schema = "") const = 0;

    /**
     * @brief Check if in transaction
     * @return bool True if in transaction
     */
    virtual bool in_transaction() const = 0;
};

/**
 * @brief Base implementation of IResultSet with common functionality
 */
class ResultSetBase : public IResultSet {
public:
    /**
     * @brief Convert current row to Record
     * @return core::Record Record representation
     */
    core::Record to_record() const override {
        core::Record record;

        for (size_t i = 0; i < column_count(); ++i) {
            if (!is_null(i)) {
                std::string col_name = column_name(i);
                record.setField(col_name, get_value(i));
            }
        }

        return record;
    }
};

/**
 * @brief Database extractor implementation
 *
 * This class implements the IExtractor interface for database sources,
 * providing efficient batch extraction from SQL databases.
 */
class DatabaseExtractor : public core::IExtractor {
public:
    /**
     * @brief Constructor
     * @param connection Database connection
     */
    explicit DatabaseExtractor(std::unique_ptr<IDatabaseConnection> connection)
        : connection_(std::move(connection)) {}

    /**
     * @brief Initialize the extractor
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Extract next batch of records
     * @param batch_size Maximum number of records to extract
     * @param context Processing context
     * @return core::RecordBatch Extracted records
     */
    core::RecordBatch extract_batch(size_t batch_size,
                                   core::ProcessingContext& context) override;

    /**
     * @brief Check if more data is available
     * @return bool True if more data can be extracted
     */
    bool has_more_data() const override { return has_more_data_; }

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "database"; }

    /**
     * @brief Finalize extraction
     * @param context Processing context
     */
    void finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get extraction statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

protected:
    /**
     * @brief Build extraction query
     * @return std::string SQL query
     */
    virtual std::string build_query() const;

    /**
     * @brief Apply filters to query
     * @param base_query Base SQL query
     * @return std::string Query with filters applied
     */
    virtual std::string apply_filters(const std::string& base_query) const;

protected:
    std::unique_ptr<IDatabaseConnection> connection_;
    std::unique_ptr<IResultSet> current_result_set_;
    std::string table_name_;
    std::string schema_name_;
    std::vector<std::string> columns_;
    std::string filter_condition_;
    std::string order_by_;
    bool has_more_data_{true};
    size_t total_extracted_{0};
    size_t batch_count_{0};
    std::chrono::steady_clock::time_point start_time_;
};

/**
 * @brief Connection pool for database connections
 *
 * This class manages a pool of database connections for improved performance
 * and resource management in multi-threaded environments.
 */
class ConnectionPool {
public:
    /**
     * @brief Constructor
     * @param min_connections Minimum number of connections
     * @param max_connections Maximum number of connections
     * @param connection_factory Factory function for creating connections
     */
    ConnectionPool(size_t min_connections,
                  size_t max_connections,
                  std::function<std::unique_ptr<IDatabaseConnection>()> connection_factory);

    /**
     * @brief Virtual destructor
     */
    virtual ~ConnectionPool();

    /**
     * @brief Acquire connection from pool
     * @param timeout_ms Timeout in milliseconds
     * @return std::unique_ptr<IDatabaseConnection> Database connection
     */
    std::unique_ptr<IDatabaseConnection> acquire(int timeout_ms = -1);

    /**
     * @brief Return connection to pool
     * @param connection Connection to return
     */
    void release(std::unique_ptr<IDatabaseConnection> connection);

    /**
     * @brief Get pool statistics
     * @return Statistics including active connections, idle connections, etc.
     */
    struct PoolStats {
        size_t total_connections;
        size_t active_connections;
        size_t idle_connections;
        size_t total_acquisitions;
        size_t total_releases;
        size_t wait_count;
        std::chrono::milliseconds avg_wait_time;
    };

    [[nodiscard]] PoolStats get_statistics() const;

    /**
     * @brief Clear all idle connections
     */
    void clear_idle_connections();

    /**
     * @brief Validate all connections
     * @return size_t Number of invalid connections removed
     */
    size_t validate_connections();

private:
    size_t min_connections_;
    size_t max_connections_;
    std::function<std::unique_ptr<IDatabaseConnection>()> connection_factory_;
    
    mutable std::mutex mutex_;
    std::condition_variable cv_;
    std::queue<std::unique_ptr<IDatabaseConnection>> idle_connections_;
    size_t active_connections_{0};
    bool shutdown_{false};
    
    // Statistics
    std::atomic<size_t> total_acquisitions_;
    std::atomic<size_t> total_releases_;
    std::atomic<size_t> wait_count_;
    std::atomic<int64_t> total_wait_time_; // milliseconds
};

/**
 * @brief Factory for creating database connections
 */
class DatabaseConnectionFactory {
public:
    using Creator = std::function<std::unique_ptr<IDatabaseConnection>(const IDatabaseConnection::ConnectionParams&)>;

    /**
     * @brief Get factory instance
     * @return DatabaseConnectionFactory& Singleton instance
     */
    static DatabaseConnectionFactory& instance() {
        static DatabaseConnectionFactory instance;
        return instance;
    }

    /**
     * @brief Register database type
     * @param type Database type name
     * @param creator Creator function
     */
    void register_type(const std::string& type, Creator creator) {
        creators_[type] = std::move(creator);
    }

    /**
     * @brief Create connection by type
     * @param type Database type
     * @param params Connection parameters
     * @return std::unique_ptr<IDatabaseConnection> Database connection
     */
    [[nodiscard]] std::unique_ptr<IDatabaseConnection> create(const std::string& type, const IDatabaseConnection::ConnectionParams& params) {
        auto it = creators_.find(type);
        if (it != creators_.end()) {
            return it->second(params);
        }
        throw common::DatabaseException(
            std::format("Unknown database type: '{}'", type), type, 0);
    }

    /**
     * @brief Create connection from configuration
     * @param config Database configuration
     * @return std::unique_ptr<IDatabaseConnection> Database connection
     */
    [[nodiscard]] std::unique_ptr<IDatabaseConnection> create_from_config(
        const std::unordered_map<std::string, std::any>& config);

private:
    DatabaseConnectionFactory() = default;
    std::unordered_map<std::string, Creator> creators_;
};

/**
 * @brief Connection retry policy with exponential backoff
 */
class ConnectionRetryPolicy {
public:
    struct Config {
        size_t max_attempts;
        std::chrono::milliseconds initial_delay;
        double backoff_multiplier;
        std::chrono::milliseconds max_delay;
        bool add_jitter;
        
        Config() : max_attempts(3), initial_delay(1000), backoff_multiplier(2.0), 
                   max_delay(30000), add_jitter(true) {}
    };

    explicit ConnectionRetryPolicy(const Config& config = Config{})
        : config_(config) {}

    /**
     * @brief Execute connection with retry logic
     * @param connection_func Function that attempts connection
     * @param logger Logger for retry messages
     * @return bool True if connection successful
     */
    template<typename ConnectionFunc>
    bool execute_with_retry(ConnectionFunc connection_func,
                          std::shared_ptr<common::Logger> logger) {
        size_t attempt = 0;
        std::chrono::milliseconds delay = config_.initial_delay;

        while (attempt < config_.max_attempts) {
            try {
                connection_func();
                return true;
            } catch (const std::exception& e) {
                attempt++;
                if (attempt >= config_.max_attempts) {
                    logger->error("Connection failed after {} attempts: {}",
                                config_.max_attempts, e.what());
                    throw;
                }

                // Add jitter to prevent thundering herd
                auto actual_delay = delay;
                if (config_.add_jitter) {
                    std::random_device rd;
                    std::mt19937 gen(rd());
                    std::uniform_int_distribution<> dis(0, delay.count() / 4);
                    actual_delay += std::chrono::milliseconds(dis(gen));
                }

                logger->warn("Connection attempt {} failed: {}. Retrying in {}ms...",
                           attempt, e.what(), actual_delay.count());

                std::this_thread::sleep_for(actual_delay);

                // Exponential backoff
                delay = std::chrono::milliseconds(
                    static_cast<long>(delay.count() * config_.backoff_multiplier));
                if (delay > config_.max_delay) {
                    delay = config_.max_delay;
                }
            }
        }
        return false;
    }

private:
    Config config_;
};

} // namespace omop::extract

File src/lib/extract/extract_utils.cpp:

/**
 * @file extract_utils.cpp
 * @brief Implementation of utility functions for the extract module
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "extract_utils.h"
#include <filesystem>
#include <regex>
#include <thread>
#include <future>
#include <queue>

namespace omop::extract {

// Utility function implementations

std::unique_ptr<core::IExtractor> create_extractor_auto(
    const std::string& source_path,
    const std::unordered_map<std::string, std::any>& config) {

    auto logger = common::Logger::get("omop-extractor-auto");
    logger->info("Auto-detecting extractor type for: {}", source_path);

    std::string type = detect_source_type(source_path);

    if (type.empty()) {
        throw common::ConfigurationException(
            "Could not determine extractor type for: " + source_path);
    }

    logger->info("Detected extractor type: {}", type);

    // Create configuration with source path
    auto full_config = config;
    if (type == "csv" || type == "compressed_csv" || type == "json" || type == "jsonl") {
        full_config["filepath"] = source_path;
    } else if (type == "csv_directory") {
        full_config["directory"] = source_path;
    }

    return create_extractor(type, full_config);
}

std::string detect_source_type(const std::string& source_path) {
    // Check if it's a file or directory
    if (std::filesystem::exists(source_path)) {
        if (std::filesystem::is_directory(source_path)) {
            // Check if directory contains CSV files
            bool has_csv = false, has_json = false;
            for (const auto& entry : std::filesystem::directory_iterator(source_path)) {
                if (entry.is_regular_file()) {
                    std::string filename = entry.path().filename().string();
                    std::transform(filename.begin(), filename.end(), filename.begin(), ::tolower);
                    if (filename.find(".csv") != std::string::npos) {
                        has_csv = true;
                    } else if (filename.find(".json") != std::string::npos) {
                        has_json = true;
                    }
                }
            }
            if (has_csv) return "csv_directory";
            if (has_json) return "json_directory";
            return "";
        }
    }

    // Check file extension (works for both existing and non-existing files)
    std::filesystem::path path(source_path);
    std::string extension = path.extension().string();
    std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);

    // Check for compressed CSV
    std::string filename = path.filename().string();
    std::transform(filename.begin(), filename.end(), filename.begin(), ::tolower);

    if (filename.find(".csv.gz") != std::string::npos ||
        filename.find(".csv.zip") != std::string::npos ||
        filename.find(".csv.bz2") != std::string::npos ||
        filename.find(".csv.xz") != std::string::npos) {
        return "compressed_csv";
    }

    // Check regular extensions
    if (extension == ".csv") {
        return "csv";
    } else if (extension == ".json") {
        // If file exists, peek at content to distinguish JSON from JSONL
        if (std::filesystem::exists(path) && std::filesystem::file_size(path) > 0) {
            std::ifstream file(path);
            std::string line;
            if (std::getline(file, line)) {
                // Simple heuristic: if first line contains array bracket, it's JSON
                // Otherwise, assume JSONL
                if (line.find('[') != std::string::npos) {
                    return "json";
                } else {
                    return "jsonl";
                }
            }
        }
        return "json"; // Default to JSON if we can't determine
    } else if (extension == ".jsonl" || extension == ".ndjson") {
        return "jsonl";
    } else if (extension == ".gz" || extension == ".zip" ||
               extension == ".bz2" || extension == ".xz") {
        // Enhanced heuristic for compressed files
        std::string stem = path.stem().string();
        std::transform(stem.begin(), stem.end(), stem.begin(), ::tolower);
        if (stem.find(".csv") != std::string::npos) {
            return "compressed_csv";
        } else if (stem.find(".json") != std::string::npos) {
            return "compressed_json";
        }
        // Default to compressed CSV for unknown compressed files
        return "compressed_csv";
    }

    // Check if it's a database connection string
    std::regex db_regex("^(postgresql|postgres|mysql|mariadb|sqlite|odbc)://", std::regex::icase);
    if (std::regex_search(source_path, db_regex)) {
        // Extract database type from URL
        size_t pos = source_path.find("://");
        if (pos != std::string::npos) {
            std::string type = source_path.substr(0, pos);
            std::transform(type.begin(), type.end(), type.begin(), ::tolower);
            return type;
        }
    }

    // Check for SQL files (could be database scripts)
    if (extension == ".sql") {
        return "sql_script";
    }

    return "";
}

std::pair<bool, std::string> validate_extractor_config(
    const std::string& type,
    const std::unordered_map<std::string, std::any>& config) {

    // Get extractor type info
    auto type_info = get_extractor_info();

    // Find the type
    auto it = std::find_if(type_info.begin(), type_info.end(),
        [&type](const ExtractorTypeInfo& info) { return info.type == type; });

    if (it == type_info.end()) {
        return {false, "Unknown extractor type: '" + type + "'"};
    }

    // Check required parameters
    for (const auto& param : it->required_params) {
        if (config.find(param) == config.end()) {
            return {false, "Missing required parameter: '" + param + "'"};
        }
    }

    // Type-specific validation
    if (type == "csv" || type == "compressed_csv" || type == "json" || type == "jsonl") {
        if (config.find("filepath") != config.end()) {
            std::string filepath;
            try {
                filepath = std::any_cast<std::string>(config.at("filepath"));
            } catch (const std::bad_any_cast&) {
                return {false, "Invalid filepath parameter type"};
            }
            if (!std::filesystem::exists(filepath)) {
                return {false, "File not found: '" + filepath + "'"};
            }
        }
    } else if (type == "csv_directory") {
        if (config.find("directory") != config.end()) {
            std::string directory;
            try {
                directory = std::any_cast<std::string>(config.at("directory"));
            } catch (const std::bad_any_cast&) {
                return {false, "Invalid directory parameter type"};
            }
            if (!std::filesystem::exists(directory) || !std::filesystem::is_directory(directory)) {
                return {false, "Directory not found: '" + directory + "'"};
            }
        }
    }

    return {true, ""};
}

// BatchExtractor implementation - Stub implementations for now

BatchExtractor::BatchExtractor(std::unique_ptr<core::IExtractor> extractor)
    : extractor_(std::move(extractor)), config_() {

    if (!extractor_) {
        throw std::runtime_error("BatchExtractor requires a valid extractor");
    }
}

BatchExtractor::BatchExtractor(std::shared_ptr<core::IExtractor> extractor)
    : extractor_(std::move(extractor)), config_() {

    if (!extractor_) {
        throw std::runtime_error("BatchExtractor requires a valid extractor");
    }
}

BatchExtractor::BatchExtractor(std::unique_ptr<core::IExtractor> extractor,
                             const Config& config)
    : extractor_(std::move(extractor)), config_(config) {

    if (!extractor_) {
        throw std::runtime_error("BatchExtractor requires a valid extractor");
    }
}

BatchExtractor::BatchExtractor(std::shared_ptr<core::IExtractor> extractor,
                             const Config& config)
    : extractor_(std::move(extractor)), config_(config) {

    if (!extractor_) {
        throw std::runtime_error("BatchExtractor requires a valid extractor");
    }
}

std::vector<core::Record> BatchExtractor::extract_all() {
    std::lock_guard<std::mutex> lock(extraction_mutex_);
    
    std::vector<core::Record> all_records;
    size_t total_extracted = 0;

    auto logger = common::Logger::get("omop-batch-extractor");
    logger->info("Starting batch extraction with batch size: {}", config_.batch_size);

    try {
        while (extractor_->has_more_data() &&
               (config_.max_records == 0 || total_extracted < config_.max_records)) {

            size_t batch_size = config_.batch_size;
            if (config_.max_records > 0) {
                size_t remaining = config_.max_records - total_extracted;
                batch_size = std::min(batch_size, remaining);
            }

            auto batch = extractor_->extract_batch(batch_size, context_);

            if (batch.empty()) {
                break;
            }

            // Only add up to the remaining number of records needed
            size_t to_add = batch.size();
            if (config_.max_records > 0 && total_extracted + to_add > config_.max_records) {
                to_add = config_.max_records - total_extracted;
            }
            const auto& batch_records = batch.getRecords();
            all_records.insert(all_records.end(), batch_records.begin(), batch_records.begin() + to_add);
            total_extracted += to_add;

            if (config_.progress_callback) {
                config_.progress_callback(total_extracted, config_.max_records);
            }
            // Stop if we've reached the max_records limit
            if (config_.max_records > 0 && total_extracted >= config_.max_records) {
                break;
            }
        }
    } catch (const std::exception& e) {
        if (config_.error_callback) {
            config_.error_callback(e.what());
        }
        
        // Always re-throw extractor-level exceptions as they indicate infrastructure failures
        throw;
    }

    // Final progress update with actual totals
    if (config_.progress_callback) {
        config_.progress_callback(total_extracted, total_extracted);
    }

    logger->info("Batch extraction completed: {} records extracted", total_extracted);
    return all_records;
}

size_t BatchExtractor::extract_with_callback(
    std::function<void(const core::RecordBatch&)> processor) {

    size_t total_extracted = 0;
    auto logger = common::Logger::get("omop-batch-extractor");

    try {
        while (extractor_->has_more_data() &&
               (config_.max_records == 0 || total_extracted < config_.max_records)) {

            size_t batch_size = config_.batch_size;
            if (config_.max_records > 0) {
                size_t remaining = config_.max_records - total_extracted;
                batch_size = std::min(batch_size, remaining);
            }

            auto batch = extractor_->extract_batch(batch_size, context_);

            if (batch.empty()) {
                break;
            }

            processor(batch);
            total_extracted += batch.size();

            if (config_.progress_callback) {
                config_.progress_callback(total_extracted, config_.max_records);
            }
        }
    } catch (const std::exception& e) {
        if (config_.error_callback) {
            config_.error_callback(e.what());
        }
        
        // Always re-throw extractor-level exceptions as they indicate infrastructure failures
        throw;
    }

    return total_extracted;
}

std::unordered_map<std::string, std::any> BatchExtractor::get_statistics() const {
    auto stats = extractor_->get_statistics();
    stats["batch_size"] = config_.batch_size;
    stats["max_records"] = config_.max_records;
    stats["continue_on_error"] = config_.continue_on_error;
    return stats;
}

void BatchExtractor::set_config(const Config& config) {
    config_ = config;
}

// ParallelExtractor implementation

ParallelExtractor::ParallelExtractor()
    : ParallelExtractor(Config{}) {
}

ParallelExtractor::ParallelExtractor(const Config& config)
    : config_(config) {
    
    auto logger = common::Logger::get("omop-parallel-extractor");
    logger->info("Initializing parallel extractor with {} workers", config.num_workers);
    
    // Start worker threads
    for (size_t i = 0; i < config.num_workers; ++i) {
        workers_.emplace_back(&ParallelExtractor::worker_thread, this);
    }
}

ParallelExtractor::~ParallelExtractor() {
    {
        std::unique_lock<std::mutex> lock(queue_mutex_);
        stop_ = true;
        queue_cv_.notify_all();
    }
    
    // Wait for all workers to finish
    for (auto& worker : workers_) {
        if (worker.joinable()) {
            worker.join();
        }
    }
}

void ParallelExtractor::add_task(std::function<void()> task) {
    {
        std::unique_lock<std::mutex> lock(queue_mutex_);
        task_queue_.push(std::move(task));
    }
    queue_cv_.notify_one();
}

void ParallelExtractor::wait_for_completion() {
    std::unique_lock<std::mutex> lock(queue_mutex_);
    completion_cv_.wait(lock, [this]() { return active_tasks_ == 0 && task_queue_.empty(); });
}

void ParallelExtractor::add_extractor(std::unique_ptr<core::IExtractor> extractor,
                                    const std::string& name) {
    std::string extractor_name = name.empty() ?
        "extractor_" + std::to_string(extractors_.size()) : name;

    extractors_.emplace_back(extractor_name, std::move(extractor));
}

std::vector<core::Record> ParallelExtractor::extract_all() {
    std::vector<core::Record> all_records;
    std::mutex records_mutex;

    auto logger = common::Logger::get("omop-parallel-extractor");
    logger->info("Starting parallel extraction with {} extractors", extractors_.size());

    // Create tasks for each extractor
    for (auto& [name, extractor] : extractors_) {
        auto extractor_ptr = std::shared_ptr<core::IExtractor>(std::move(extractor));
        add_task([name, extractor_ptr, &all_records, &records_mutex]() {
            try {
                BatchExtractor batch_extractor(extractor_ptr);
                auto records = batch_extractor.extract_all();

                std::unique_lock<std::mutex> lock(records_mutex);
                all_records.insert(all_records.end(), records.begin(), records.end());

            } catch (const std::exception& e) {
                auto logger = common::Logger::get("omop-parallel-extractor");
                logger->error("Extractor '{}' failed: {}", name, e.what());
            }
        });
    }

    wait_for_completion();

    logger->info("Parallel extraction completed: {} total records", all_records.size());
    return all_records;
}

void ParallelExtractor::extract_streaming(
    std::function<void(const core::RecordBatch&, const std::string&)> processor) {

    auto logger = common::Logger::get("omop-parallel-extractor");
    logger->info("Starting parallel streaming extraction");

    // Create tasks for each extractor
    for (auto& [name, extractor] : extractors_) {
        add_task([this, &name, &extractor, &processor]() {
            try {
                core::ProcessingContext context;

                while (extractor->has_more_data()) {
                    auto batch = extractor->extract_batch(10000, context);
                    if (!batch.empty()) {
                        processor(batch, name);
                    }
                }

            } catch (const std::exception& e) {
                auto logger = common::Logger::get("omop-parallel-extractor");
                logger->error("Streaming extractor '{}' failed: {}", name, e.what());
            }
        });
    }

    wait_for_completion();
}

std::unordered_map<std::string, std::unordered_map<std::string, std::any>>
ParallelExtractor::get_all_statistics() const {
    std::unordered_map<std::string, std::unordered_map<std::string, std::any>> stats;

    for (const auto& [name, extractor] : extractors_) {
        stats[name] = extractor->get_statistics();
    }

    return stats;
}

// Private helper methods
void ParallelExtractor::worker_thread() {
    auto logger = common::Logger::get("omop-parallel-extractor");
    
    while (true) {
        std::function<void()> task;
        
        {
            std::unique_lock<std::mutex> lock(queue_mutex_);
            queue_cv_.wait(lock, [this]() { return stop_ || !task_queue_.empty(); });
            
            if (stop_ && task_queue_.empty()) {
                break;
            }
            
            if (!task_queue_.empty()) {
                task = std::move(task_queue_.front());
                task_queue_.pop();
                active_tasks_++;
            }
        }
        
        if (task) {
            try {
                task();
            } catch (const std::exception& e) {
                logger->error("Error in worker thread: {}", e.what());
            }
            
            {
                std::unique_lock<std::mutex> lock(queue_mutex_);
                active_tasks_--;
                if (active_tasks_ == 0 && task_queue_.empty()) {
                    completion_cv_.notify_all();
                }
            }
        }
    }
}

// Utility namespace implementations

namespace utils {

std::vector<core::Record> extract_csv(const std::string& filepath,
                                    const CsvOptions& options) {
    auto logger = common::Logger::get("omop-csv-utils");
    logger->info("Extracting CSV data from: {}", filepath);

    // Create CSV extractor with provided options
    auto extractor = std::make_unique<CsvExtractor>();
    
    // Configure extractor
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    config["delimiter"] = std::string(1, options.delimiter);
    config["quote_char"] = std::string(1, options.quote_char);
    config["escape_char"] = std::string(1, options.escape_char);
    config["has_header"] = options.has_header;
    config["encoding"] = options.encoding;
    config["skip_empty_lines"] = options.skip_empty_lines;
    config["trim_fields"] = options.trim_fields;
    config["skip_lines"] = options.skip_lines;
    if (options.max_lines.has_value()) {
        config["max_lines"] = options.max_lines.value();
    }
    config["null_string"] = options.null_string;
    config["date_format"] = options.date_format;
    config["datetime_format"] = options.datetime_format;
    
    if (!options.column_names.empty()) {
        config["column_names"] = options.column_names;
    }
    if (!options.column_types.empty()) {
        config["column_types"] = options.column_types;
    }

    // Initialize extractor
    core::ProcessingContext context;
    extractor->initialize(config, context);

    // Extract all records using batch processing
    std::unique_ptr<core::IExtractor> extractor_ptr = std::move(extractor);
    BatchExtractor batch_extractor(std::move(extractor_ptr));
    auto records = batch_extractor.extract_all();

    logger->info("Successfully extracted {} records from CSV file", records.size());
    return records;
}

std::vector<core::Record> extract_json(const std::string& filepath,
                                     const JsonOptions& options) {
    auto logger = common::Logger::get("omop-json-utils");
    logger->info("Extracting JSON data from: {}", filepath);

    // Create JSON extractor
    auto extractor = create_extractor("json", {{"filepath", filepath}});
    
    if (!extractor) {
        throw common::ConfigurationException(
            "Failed to create JSON extractor for file: " + filepath);
    }

    // Configure extractor with JSON-specific options
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    
    // JSON extractor specific configuration
    if (!options.root_path.empty()) {
        config["root_path"] = options.root_path;
    }
    config["flatten_nested"] = options.flatten_nested;
    config["array_delimiter"] = options.array_delimiter;
    config["parse_dates"] = options.parse_dates;
    config["date_formats"] = options.date_formats;
    config["ignore_null"] = options.ignore_null;
    config["max_depth"] = options.max_depth;

    // Initialize extractor
    core::ProcessingContext context;
    extractor->initialize(config, context);

    // Extract all records using batch processing
    std::unique_ptr<core::IExtractor> extractor_ptr = std::move(extractor);
    BatchExtractor batch_extractor(std::move(extractor_ptr));
    auto records = batch_extractor.extract_all();

    logger->info("Successfully extracted {} records from JSON file", records.size());
    return records;
}

std::vector<core::Record> extract_table(std::unique_ptr<IDatabaseConnection> connection,
                                      const std::string& table_name,
                                      const std::string& filter) {
    auto logger = common::Logger::get("omop-db-utils");
    logger->info("Extracting table data from: {}", table_name);

    if (!connection || !connection->is_connected()) {
        throw common::DatabaseException(
            "Database connection is not available or not connected", 
            connection ? connection->get_database_type() : "unknown", 0);
    }

    // Create database extractor
    auto extractor = std::make_unique<DatabaseExtractor>(std::move(connection));
    
    // Configure extractor
    std::unordered_map<std::string, std::any> config;
    config["table"] = table_name;
    
    // Parse table name for schema if provided (schema.table format)
    size_t dot_pos = table_name.find('.');
    if (dot_pos != std::string::npos) {
        config["schema"] = table_name.substr(0, dot_pos);
        config["table"] = table_name.substr(dot_pos + 1);
    }
    
    // Add filter condition if provided
    if (!filter.empty()) {
        config["filter"] = filter;
    }

    // Initialize extractor
    core::ProcessingContext context;
    extractor->initialize(config, context);

    // Extract all records using batch processing
    std::unique_ptr<core::IExtractor> extractor_ptr = std::move(extractor);
    BatchExtractor batch_extractor(std::move(extractor_ptr));
    auto records = batch_extractor.extract_all();

    logger->info("Successfully extracted {} records from table {}", records.size(), table_name);
    return records;
}

std::unique_ptr<IDatabaseConnection> create_connection_from_url(const std::string& url) {
    // Parse database URL format: type://username:password@host:port/database
    std::regex url_regex(R"((\w+)://(?:([^:]+):([^@]+)@)?([^:/]+)(?::(\d+))?/(.+))");
    std::smatch matches;

    if (!std::regex_match(url, matches, url_regex)) {
        throw common::ConfigurationException(
            "Invalid database URL format: '" + url + "'");
    }

    std::string type = matches[1].str();
    std::string username = matches[2].str();
    std::string password = matches[3].str();
    std::string host = matches[4].str();
    std::string port_str = matches[5].str();
    std::string database = matches[6].str();

    int port = 0;
    if (!port_str.empty()) {
        port = std::stoi(port_str);
    } else {
        // Default ports
        if (type == "postgresql" || type == "postgres") {
            port = 5432;
        } else if (type == "mysql" || type == "mariadb") {
            port = 3306;
        }
    }

    IDatabaseConnection::ConnectionParams params;
    params.host = host;
    params.port = port;
    params.database = database;
    params.username = username;
    params.password = password;

    auto connection = DatabaseConnectionFactory::instance().create(type, params);

    return connection;
}

} // namespace utils

} // namespace omop::extract

File src/lib/extract/json_extractor.h:

#pragma once

#include "core/interfaces.h"
#include <nlohmann/json.hpp>
#include <fstream>
#include <filesystem>
#include <queue>
#include <stack>
#include <thread>
#include <condition_variable>
#include <string>
#include <any>
#include <unordered_map>
#include <optional>
#include <chrono>

namespace omop::extract {

// Forward declarations
class JsonLinesExtractor;

using json = nlohmann::json;

/**
 * @brief JSON parsing options
 */
struct JsonOptions {
    std::string root_path;              ///< JSON path to data array (e.g., "data.patients")
    bool flatten_nested{true};          ///< Flatten nested objects
    std::string array_delimiter{"_"};   ///< Delimiter for flattened array indices
    bool parse_dates{true};             ///< Automatically parse date strings
    std::vector<std::string> date_formats{
        "%Y-%m-%d",
        "%Y-%m-%dT%H:%M:%S",
        "%Y-%m-%d %H:%M:%S"
    };
    bool ignore_null{true};             ///< Skip null values
    size_t max_depth{10};               ///< Maximum nesting depth
};

/**
 * @brief JSON extractor for single JSON files
 *
 * Extracts data from JSON files, supporting both array and object formats,
 * with automatic flattening of nested structures.
 */
class JsonExtractor : public core::IExtractor {
public:
    /**
     * @brief Constructor
     */
    JsonExtractor() = default;

    /**
     * @brief Initialize the extractor
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   [[maybe_unused]] core::ProcessingContext& context) override;

    /**
     * @brief Extract next batch of records
     * @param batch_size Maximum number of records to extract
     * @param context Processing context
     * @return core::RecordBatch Extracted records
     */
    core::RecordBatch extract_batch(size_t batch_size,
                                   core::ProcessingContext& context) override;

    /**
     * @brief Check if more data is available
     * @return bool True if more data can be extracted
     */
    bool has_more_data() const override;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "json"; }

    /**
     * @brief Finalize extraction
     * @param context Processing context
     */
    void finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get extraction statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

protected:
    /**
     * @brief Load JSON file
     * @param filepath File path
     */
    void load_file(const std::string& filepath);

    /**
     * @brief Navigate to data array using JSON path
     * @param root Root JSON object
     * @param path Dot-separated path (e.g., "data.patients")
     * @return json::const_iterator Iterator to array
     */
    json::const_iterator navigate_to_data(const json& root, const std::string& path);

public:
    /**
     * @brief Flatten JSON object to record
     * @param obj JSON object
     * @param prefix Field name prefix
     * @param depth Current nesting depth
     * @return core::Record Flattened record
     */
    core::Record flatten_json_object(const json& obj,
                                    const std::string& prefix = "",
                                    size_t depth = 0);

protected:

    /**
     * @brief Convert JSON value to std::any
     * @param value JSON value
     * @return std::any Converted value
     */
    std::any json_to_any(const json& value);

    /**
     * @brief Parse date string
     * @param date_str Date string
     * @return std::optional<std::chrono::system_clock::time_point> Parsed date
     */
    std::optional<std::chrono::system_clock::time_point> parse_date(const std::string& date_str);

public:
    JsonOptions options_;

protected:
    std::string filepath_;
    json json_data_;
    json::const_iterator current_iterator_;
    json::const_iterator end_iterator_;
    size_t total_records_{0};
    size_t extracted_count_{0};
    bool data_loaded_{false};
    std::chrono::steady_clock::time_point start_time_;
    std::unique_ptr<JsonLinesExtractor> jsonl_extractor_;  // For delegation
};

/**
 * @brief JSON Lines (JSONL) extractor
 *
 * Extracts data from JSON Lines files where each line is a separate JSON object.
 * More memory-efficient for large files.
 */
class JsonLinesExtractor : public core::IExtractor {
public:
    /**
     * @brief Constructor
     */
    JsonLinesExtractor() = default;

    /**
     * @brief Constructor with type
     * @param type The type string used to create this extractor
     */
    explicit JsonLinesExtractor(const std::string& type) : type_(type) {}

    /**
     * @brief Initialize the extractor
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Extract next batch of records
     * @param batch_size Maximum number of records to extract
     * @param context Processing context
     * @return core::RecordBatch Extracted records
     */
    core::RecordBatch extract_batch(size_t batch_size,
                                   core::ProcessingContext& context) override;

    /**
     * @brief Check if more data is available
     * @return bool True if more data can be extracted
     */
    bool has_more_data() const override;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return type_.empty() ? "jsonl" : type_; }

    /**
     * @brief Finalize extraction
     * @param context Processing context
     */
    void finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get extraction statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

protected:
    /**
     * @brief Open JSONL file
     * @param filepath File path
     */
    void open_file(const std::string& filepath);

    /**
     * @brief Read next line and parse JSON
     * @return std::optional<json> Parsed JSON object
     */
    std::optional<json> read_next_line();

    /**
     * @brief Convert JSON object to record
     * @param obj JSON object
     * @return core::Record Converted record
     */
    core::Record json_to_record(const json& obj);

private:
    std::ifstream file_stream_;
    std::string filepath_;
    JsonOptions options_;
    size_t current_line_{0};
    size_t extracted_count_{0};
    size_t error_count_{0};
    bool has_more_{true};
    std::chrono::steady_clock::time_point start_time_;
    std::string type_{"jsonl"};  // Default type
};

/**
 * @brief Streaming JSON extractor for very large files
 *
 * Uses a SAX-style parser to handle JSON files that don't fit in memory.
 */
class StreamingJsonExtractor : public core::IExtractor {
public:
    /**
     * @brief SAX event handler for JSON parsing
     */
    class JsonHandler : public nlohmann::json_sax<json> {
    public:
        JsonHandler(std::queue<core::Record>& record_queue,
                   const JsonOptions& options)
            : record_queue_(record_queue), options_(options) {}

        // SAX interface implementation
        bool null() override;
        bool boolean(bool val) override;
        bool number_integer(number_integer_t val) override;
        bool number_unsigned(number_unsigned_t val) override;
        bool number_float(number_float_t val, const string_t& s) override;
        bool string(string_t& val) override;
        bool binary(binary_t& val) override;
        bool start_object(std::size_t elements) override;
        bool end_object() override;
        bool start_array(std::size_t elements) override;
        bool end_array() override;
        bool key(string_t& val) override;
        bool parse_error(std::size_t position, const std::string& last_token,
                        const nlohmann::detail::exception& ex) override;

    private:
        std::queue<core::Record>& record_queue_;
        const JsonOptions& options_;
        std::stack<std::string> path_stack_;
        std::stack<json> object_stack_;
        std::string current_key_;
        bool in_data_array_{false};
    };

    /**
     * @brief Constructor
     */
    StreamingJsonExtractor() = default;

    // IExtractor interface implementation
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;
    core::RecordBatch extract_batch(size_t batch_size,
                                   core::ProcessingContext& context) override;
    bool has_more_data() const override;
    std::string get_type() const override { return "streaming_json"; }
    void finalize(core::ProcessingContext& context) override;
    std::unordered_map<std::string, std::any> get_statistics() const override;

private:
    std::ifstream file_stream_;
    std::string filepath_;
    JsonOptions options_;
    std::queue<core::Record> record_queue_;
    std::unique_ptr<std::thread> parser_thread_;
    mutable std::mutex queue_mutex_;
    std::condition_variable queue_cv_;
    std::atomic<bool> parsing_complete_{false};
    std::atomic<bool> has_error_{false};
    std::string error_message_;
    size_t extracted_count_{0};
    std::chrono::steady_clock::time_point start_time_;

    void parse_file_async();
};

/**
 * @brief Factory for JSON extractors
 */
class JsonExtractorFactory {
public:
    /**
     * @brief Create JSON extractor
     * @param type Extractor type (json, jsonl, streaming_json)
     * @return std::unique_ptr<core::IExtractor> Extractor instance
     */
    static std::unique_ptr<core::IExtractor> create(const std::string& type);

    /**
     * @brief Register JSON extractors with the main factory
     */
    static void register_extractors();
};

// Implementation helpers

inline void JsonExtractor::initialize(const std::unordered_map<std::string, std::any>& config,
                                     [[maybe_unused]] core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-json-extractor");
    logger->info("Initializing JSON extractor");

    // Check if this should be delegated to JsonLinesExtractor
    if (config.find("format") != config.end()) {
        std::string format = std::any_cast<std::string>(config.at("format"));
        if (format == "jsonl" || format == "json-lines") {
            // Delegate to JsonLinesExtractor
            jsonl_extractor_ = std::make_unique<JsonLinesExtractor>();
            jsonl_extractor_->initialize(config, context);
            return;
        }
    }

    // Get filepath
    if (config.find("filepath") == config.end()) {
        throw common::ExtractionException("JSON extractor requires 'filepath' parameter", "json");
    }
    filepath_ = std::any_cast<std::string>(config.at("filepath"));

    // Configure options
    if (config.find("root_path") != config.end()) {
        options_.root_path = std::any_cast<std::string>(config.at("root_path"));
    }
    if (config.find("flatten_nested") != config.end()) {
        options_.flatten_nested = std::any_cast<bool>(config.at("flatten_nested"));
    }
    if (config.find("array_delimiter") != config.end()) {
        options_.array_delimiter = std::any_cast<std::string>(config.at("array_delimiter"));
    }
    if (config.find("ignore_null") != config.end()) {
        options_.ignore_null = std::any_cast<bool>(config.at("ignore_null"));
    }
    if (config.find("max_depth") != config.end()) {
        options_.max_depth = std::any_cast<size_t>(config.at("max_depth"));
    }

    start_time_ = std::chrono::steady_clock::now();

    // Load JSON file
    load_file(filepath_);
}

inline void JsonExtractor::load_file(const std::string& filepath) {
    auto logger = common::Logger::get("omop-json-extractor");

    if (!std::filesystem::exists(filepath)) {
        throw common::ExtractionException(
            "JSON file not found: '" + filepath + "'", "json");
    }

    try {
        std::ifstream file(filepath);
        if (!file.is_open()) {
            throw common::ExtractionException(
                "Failed to open JSON file: '" + filepath + "'", "json");
        }

        // Parse JSON
        file >> json_data_;
        data_loaded_ = true;

        // Navigate to data array
        if (!options_.root_path.empty()) {
            current_iterator_ = navigate_to_data(json_data_, options_.root_path);

            // Find the parent array
            std::vector<std::string> path_parts;
            std::stringstream ss(options_.root_path);
            std::string part;
            while (std::getline(ss, part, '.')) {
                path_parts.push_back(part);
            }

            json* current = &json_data_;
            for (const auto& p : path_parts) {
                if (current->contains(p)) {
                    current = &(*current)[p];
                }
            }

            if (current->is_array()) {
                current_iterator_ = current->begin();
                end_iterator_ = current->end();
                total_records_ = current->size();
            } else {
                throw common::ExtractionException(
                    "Path '" + options_.root_path + "' does not point to an array", "json");
            }
        } else if (json_data_.is_array()) {
            current_iterator_ = json_data_.begin();
            end_iterator_ = json_data_.end();
            total_records_ = json_data_.size();
        } else {
            // Single object
            total_records_ = 1;
        }

        logger->info("Loaded JSON file with {} records", total_records_);

    } catch (const json::exception& e) {
        throw common::ExtractionException(
            "Failed to parse JSON file: " + std::string(e.what()), "json");
    }
}

inline core::RecordBatch JsonExtractor::extract_batch(size_t batch_size,
                                                     core::ProcessingContext& context) {
    // Delegate to JsonLinesExtractor if using JSON Lines format
    if (jsonl_extractor_) {
        return jsonl_extractor_->extract_batch(batch_size, context);
    }

    core::RecordBatch batch(batch_size);
    batch.reserve(batch_size);

    if (!data_loaded_) {
        return batch;
    }

    size_t count = 0;

    // Handle array of objects
    if (json_data_.is_array() || !options_.root_path.empty()) {
        while (current_iterator_ != end_iterator_ && count < batch_size) {
            try {
                if (current_iterator_->is_object()) {
                    auto record = flatten_json_object(*current_iterator_);
                    batch.addRecord(std::move(record));
                    count++;
                    extracted_count_++;
                }
            } catch (const std::exception& e) {
                context.log("warning",
                    "Failed to extract record: " + std::string(e.what()));
                context.increment_errors();
            }

            ++current_iterator_;
        }
    } else if (json_data_.is_object() && extracted_count_ == 0) {
        // Single object
        try {
            auto record = flatten_json_object(json_data_);
            batch.addRecord(std::move(record));
            extracted_count_++;
        } catch (const std::exception& e) {
            context.log("error",
                "Failed to extract single object: " + std::string(e.what()));
            context.increment_errors();
        }
    }

    return batch;
}

inline core::Record JsonExtractor::flatten_json_object(const json& obj,
                                                      const std::string& prefix,
                                                      size_t depth) {
    core::Record record;

    if (depth >= options_.max_depth) {
        return record;
    }

    for (auto& [key, value] : obj.items()) {
        // Use move semantics and reserve to reduce allocations
        std::string field_name = prefix.empty() ? std::move(key) :
                                prefix + options_.array_delimiter + std::move(key);

        if (value.is_null() && options_.ignore_null) {
            continue;
        }

        if (value.is_object() && options_.flatten_nested) {
            // Recursively flatten nested object
            auto nested_record = flatten_json_object(value, field_name, depth + 1);
            for (const auto& nested_field : nested_record.getFieldNames()) {
                try {
                    auto nested_value = nested_record.getField(nested_field);
                    record.setField(nested_field, nested_value);
                } catch (const std::exception&) {
                    // Field doesn't exist, skip it
                }
            }
        } else if (value.is_array() && options_.flatten_nested) {
            // Flatten array
            // Limit array size to prevent memory exhaustion
            const size_t MAX_ARRAY_SIZE = 1000;
            size_t array_size = value.size();
            if (array_size > MAX_ARRAY_SIZE) {
                auto logger = common::Logger::get("omop-json-extractor");
                logger->warn("Array '{}' has {} elements, limiting to {}",
                           field_name, array_size, MAX_ARRAY_SIZE);
                array_size = MAX_ARRAY_SIZE;
            }

            for (size_t i = 0; i < array_size; ++i) {
                // Pre-allocate string capacity
                std::string array_field;
                array_field.reserve(field_name.length() + options_.array_delimiter.length() + 10);
                array_field = field_name + options_.array_delimiter + std::to_string(i);
                if (value[i].is_object()) {
                    auto nested_record = flatten_json_object(value[i], array_field, depth + 1);
                    for (const auto& nested_field : nested_record.getFieldNames()) {
                        try {
                            auto nested_value = nested_record.getField(nested_field);
                            record.setField(nested_field, nested_value);
                        } catch (const std::exception&) {
                            // Field doesn't exist, skip it
                        }
                    }
                } else {
                    record.setField(array_field, json_to_any(value[i]));
                }
            }
        } else {
            // Direct value
            record.setField(field_name, json_to_any(value));
        }
    }

    return record;
}

inline std::any JsonExtractor::json_to_any(const json& value) {
    if (value.is_null()) {
        return std::any(std::nullptr_t{});
    } else if (value.is_boolean()) {
        return value.get<bool>();
    } else if (value.is_number_integer()) {
        return value.get<int64_t>();
    } else if (value.is_number_float()) {
        return value.get<double>();
    } else if (value.is_string()) {
        std::string str_val = value.get<std::string>();

        // Try to parse as date if enabled
        if (options_.parse_dates) {
            auto date = parse_date(str_val);
            if (date) {
                return *date;
            }
        }

        return str_val;
    } else {
        // Complex type, convert to string representation
        return value.dump();
    }
}

inline bool JsonExtractor::has_more_data() const {
    // Delegate to JsonLinesExtractor if using JSON Lines format
    if (jsonl_extractor_) {
        return jsonl_extractor_->has_more_data();
    }

    if (!data_loaded_) return false;

    if (json_data_.is_array() || !options_.root_path.empty()) {
        return current_iterator_ != end_iterator_;
    } else {
        return extracted_count_ == 0;
    }
}

} // namespace omop::extract

File src/lib/extract/json_extractor.cpp:

/**
 * @file json_extractor.cpp
 * @brief Implementation of JSON data extractors
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "json_extractor.h"
#include "common/logging.h"
#include "common/exceptions.h"
#include "extractor_factory.h"
#include <thread>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <stack>

namespace omop::extract {

// Helper functions

json::const_iterator JsonExtractor::navigate_to_data(const json& root, const std::string& path) {
    std::vector<std::string> path_parts;
    std::stringstream ss(path);
    std::string part;

    while (std::getline(ss, part, '.')) {
        path_parts.push_back(part);
    }

    const json* current = &root;

    for (const auto& p : path_parts) {
        if (current->contains(p)) {
            current = &(*current)[p];
        } else {
            throw common::ExtractionException(
                "Path '" + path + "' not found in JSON structure", "json");
        }
    }

    if (!current->is_array()) {
        throw common::ExtractionException(
            "Path '" + path + "' does not point to an array", "json");
    }

    return current->begin();
}

std::optional<std::chrono::system_clock::time_point> JsonExtractor::parse_date(const std::string& date_str) {
    for (const auto& format : options_.date_formats) {
        std::tm tm = {};
        std::stringstream ss(date_str);
        ss >> std::get_time(&tm, format.c_str());

        if (!ss.fail()) {
            return std::chrono::system_clock::from_time_t(std::mktime(&tm));
        }
    }

    return std::nullopt;
}

void JsonExtractor::finalize(core::ProcessingContext& context) {
    // Delegate to JsonLinesExtractor if using JSON Lines format
    if (jsonl_extractor_) {
        jsonl_extractor_->finalize(context);
        return;
    }

    auto logger = common::Logger::get("omop-json-extractor");

    auto end_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(
        end_time - start_time_).count();

    logger->info("JSON extraction completed: {} records extracted in {} seconds",
                extracted_count_, duration);
}

std::unordered_map<std::string, std::any> JsonExtractor::get_statistics() const {
    // Delegate to JsonLinesExtractor if using JSON Lines format
    if (jsonl_extractor_) {
        auto stats = jsonl_extractor_->get_statistics();
        // Add aliases for test compatibility
        if (stats.find("extracted_count") != stats.end()) {
            stats["successful_records"] = stats["extracted_count"];
        }
        if (stats.find("error_count") != stats.end()) {
            stats["failed_records"] = stats["error_count"];
        } else {
            stats["failed_records"] = size_t{0};
            stats["error_count"] = size_t{0};
        }
        return stats;
    }

    std::unordered_map<std::string, std::any> stats;

    stats["filepath"] = filepath_;
    stats["total_records"] = static_cast<size_t>(total_records_);
    stats["extracted_count"] = static_cast<size_t>(extracted_count_);
    stats["successful_records"] = static_cast<size_t>(extracted_count_);  // Alias for test compatibility
    stats["failed_records"] = size_t{0};             // JSON extractor doesn't track failures separately
    stats["error_count"] = size_t{0};                // Add error_count for consistency
    stats["data_loaded"] = data_loaded_;

    auto current_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(
        current_time - start_time_).count();
    stats["extraction_time_seconds"] = duration;

    if (duration > 0) {
        stats["records_per_second"] = static_cast<double>(extracted_count_) / duration;
    }

    return stats;
}

// JsonLinesExtractor implementation

void JsonLinesExtractor::initialize(const std::unordered_map<std::string, std::any>& config,
                                   [[maybe_unused]] core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-jsonl-extractor");
    logger->info("Initializing JSON Lines extractor");

    // Get filepath
    if (config.find("filepath") == config.end()) {
        throw common::ExtractionException("JSONL extractor requires 'filepath' parameter", "jsonl");
    }
    filepath_ = std::any_cast<std::string>(config.at("filepath"));

    // Configure options
    if (config.find("flatten_nested") != config.end()) {
        options_.flatten_nested = std::any_cast<bool>(config.at("flatten_nested"));
    }
    if (config.find("array_delimiter") != config.end()) {
        options_.array_delimiter = std::any_cast<std::string>(config.at("array_delimiter"));
    }
    if (config.find("parse_dates") != config.end()) {
        options_.parse_dates = std::any_cast<bool>(config.at("parse_dates"));
    }
    if (config.find("date_formats") != config.end()) {
        options_.date_formats = std::any_cast<std::vector<std::string>>(config.at("date_formats"));
    }
    if (config.find("ignore_null") != config.end()) {
        options_.ignore_null = std::any_cast<bool>(config.at("ignore_null"));
    }
    if (config.find("max_depth") != config.end()) {
        options_.max_depth = std::any_cast<size_t>(config.at("max_depth"));
    }

    start_time_ = std::chrono::steady_clock::now();

    // Open file
    open_file(filepath_);
}

void JsonLinesExtractor::open_file(const std::string& filepath) {
    auto logger = common::Logger::get("omop-jsonl-extractor");

    if (!std::filesystem::exists(filepath)) {
        throw common::ExtractionException(
            "JSONL file not found: '" + filepath + "'", "jsonl");
    }

    file_stream_.open(filepath, std::ios::in);
    if (!file_stream_.is_open()) {
        throw common::ExtractionException(
            "Failed to open JSONL file: '" + filepath + "'", "jsonl");
    }

    logger->info("Opened JSONL file: {}", filepath);
}

std::optional<json> JsonLinesExtractor::read_next_line() {
    if (!file_stream_.good()) {
        return std::nullopt;
    }

    std::string line;
    std::getline(file_stream_, line);
    current_line_++;

    if (line.empty()) {
        return std::nullopt;
    }

    try {
        return json::parse(line);
    } catch (const json::parse_error& e) {
        auto logger = common::Logger::get("omop-jsonl-extractor");
        logger->warn("Failed to parse JSON at line {}: {}", current_line_, e.what());
        error_count_++;
        return std::nullopt;
    }
}

core::Record JsonLinesExtractor::json_to_record(const json& obj) {
    core::Record record;

    // Use JsonExtractor's flattening logic
    JsonExtractor temp_extractor;
    temp_extractor.options_ = options_;

    return temp_extractor.flatten_json_object(obj);
}

core::RecordBatch JsonLinesExtractor::extract_batch(size_t batch_size,
                                                   core::ProcessingContext& context) {
    core::RecordBatch batch(batch_size);
    batch.reserve(batch_size);

    size_t count = 0;

    while (has_more_ && count < batch_size) {
        auto json_obj = read_next_line();

        if (!json_obj) {
            if (!file_stream_.good()) {
                has_more_ = false;
            }
            continue;
        }

        try {
            auto record = json_to_record(*json_obj);

            // Add metadata
            // Set metadata using the Record metadata structure
            core::Record::RecordMetadata metadata;
            metadata.custom["source_file"] = filepath_;
            metadata.custom["line_number"] = current_line_;
            metadata.extraction_time = std::chrono::system_clock::now();
            record.setMetadata(metadata);

            batch.addRecord(std::move(record));
            count++;
            extracted_count_++;

        } catch (const std::exception& e) {
            context.log("warning",
                "Failed to extract record at line " + std::to_string(current_line_) + ": " + std::string(e.what()));
            context.increment_errors();
            error_count_++;

            // Continue processing on error by default
        }
    }

    return batch;
}

bool JsonLinesExtractor::has_more_data() const {
    return has_more_ && file_stream_.good();
}

void JsonLinesExtractor::finalize([[maybe_unused]] core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-jsonl-extractor");

    if (file_stream_.is_open()) {
        file_stream_.close();
    }

    auto end_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(
        end_time - start_time_).count();

    logger->info("JSONL extraction completed: {} records extracted, {} errors in {} seconds",
                extracted_count_, error_count_, duration);
}

std::unordered_map<std::string, std::any> JsonLinesExtractor::get_statistics() const {
    std::unordered_map<std::string, std::any> stats;

    stats["filepath"] = filepath_;
    stats["lines_processed"] = static_cast<size_t>(current_line_);
    stats["extracted_count"] = static_cast<size_t>(extracted_count_);
    stats["error_count"] = static_cast<size_t>(error_count_);

    auto current_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(
        current_time - start_time_).count();
    stats["extraction_time_seconds"] = duration;

    if (duration > 0) {
        stats["records_per_second"] = static_cast<double>(extracted_count_) / duration;
    }

    return stats;
}

// StreamingJsonExtractor::JsonHandler implementation

bool StreamingJsonExtractor::JsonHandler::null() {
    if (in_data_array_ && !object_stack_.empty()) {
        object_stack_.top()[current_key_] = nullptr;
    }
    return true;
}

bool StreamingJsonExtractor::JsonHandler::boolean(bool val) {
    if (in_data_array_ && !object_stack_.empty()) {
        object_stack_.top()[current_key_] = val;
    }
    return true;
}

bool StreamingJsonExtractor::JsonHandler::number_integer(number_integer_t val) {
    if (in_data_array_ && !object_stack_.empty()) {
        object_stack_.top()[current_key_] = val;
    }
    return true;
}

bool StreamingJsonExtractor::JsonHandler::number_unsigned(number_unsigned_t val) {
    if (in_data_array_ && !object_stack_.empty()) {
        object_stack_.top()[current_key_] = val;
    }
    return true;
}

bool StreamingJsonExtractor::JsonHandler::number_float(number_float_t val, [[maybe_unused]] const string_t& s) {
    if (in_data_array_ && !object_stack_.empty()) {
        object_stack_.top()[current_key_] = val;
    }
    return true;
}

bool StreamingJsonExtractor::JsonHandler::string(string_t& val) {
    if (in_data_array_ && !object_stack_.empty()) {
        object_stack_.top()[current_key_] = val;
    }
    return true;
}

bool StreamingJsonExtractor::JsonHandler::binary([[maybe_unused]] binary_t& val) {
    // Binary data not typically used in JSON extraction
    return true;
}

bool StreamingJsonExtractor::JsonHandler::start_object([[maybe_unused]] std::size_t elements) {
    if (in_data_array_ ||
        (!options_.root_path.empty() &&
         !options_.root_path.empty())) {
        object_stack_.push(json::object());
    }
    return true;
}

bool StreamingJsonExtractor::JsonHandler::end_object() {
    if (!object_stack_.empty()) {
        json obj = object_stack_.top();
        object_stack_.pop();

        if (object_stack_.empty() && in_data_array_) {
            // This is a complete record object
            JsonExtractor temp_extractor;
            temp_extractor.options_ = options_;

            core::Record record = temp_extractor.flatten_json_object(obj);
            record_queue_.push(std::move(record));
        } else if (!object_stack_.empty() && !current_key_.empty()) {
            // Nested object
            object_stack_.top()[current_key_] = obj;
        }
    }

    if (!path_stack_.empty()) {
        path_stack_.pop();
    }

    return true;
}

bool StreamingJsonExtractor::JsonHandler::start_array([[maybe_unused]] std::size_t elements) {
    if (!options_.root_path.empty()) {
        std::string current_path;
        // Convert stack to vector for iteration
        std::vector<std::string> path_vector;
        std::stack<std::string> temp_stack = path_stack_;
        while (!temp_stack.empty()) {
            path_vector.push_back(temp_stack.top());
            temp_stack.pop();
        }
        std::reverse(path_vector.begin(), path_vector.end());

        for (const auto& p : path_vector) {
            if (!current_path.empty()) current_path += ".";
            current_path += p;
        }

        if (current_path == options_.root_path) {
            in_data_array_ = true;
        }
    } else if (path_stack_.empty()) {
        // Root level array
        in_data_array_ = true;
    }

    return true;
}

bool StreamingJsonExtractor::JsonHandler::end_array() {
    if (in_data_array_ && path_stack_.size() <= 1) {
        in_data_array_ = false;
    }

    if (!path_stack_.empty()) {
        path_stack_.pop();
    }

    return true;
}

bool StreamingJsonExtractor::JsonHandler::key(string_t& val) {
    current_key_ = val;
    path_stack_.push(val);
    return true;
}

bool StreamingJsonExtractor::JsonHandler::parse_error(std::size_t position,
                                                      const std::string& last_token,
                                                      const nlohmann::detail::exception& ex) {
    auto logger = common::Logger::get("omop-streaming-json");
    logger->error("JSON parse error at position {}: {} (last token: '{}')",
                 position, ex.what(), last_token);
    return false;
}

// StreamingJsonExtractor implementation

void StreamingJsonExtractor::initialize(const std::unordered_map<std::string, std::any>& config,
                                       [[maybe_unused]] core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-streaming-json");
    logger->info("Initializing streaming JSON extractor");

    // Get filepath
    if (config.find("filepath") == config.end()) {
        throw common::ExtractionException(
            "Streaming JSON extractor requires 'filepath' parameter", "streaming_json");
    }
    filepath_ = std::any_cast<std::string>(config.at("filepath"));

    // Configure options
    if (config.find("root_path") != config.end()) {
        options_.root_path = std::any_cast<std::string>(config.at("root_path"));
    }
    if (config.find("flatten_nested") != config.end()) {
        options_.flatten_nested = std::any_cast<bool>(config.at("flatten_nested"));
    }

    start_time_ = std::chrono::steady_clock::now();

    // Open file
    if (!std::filesystem::exists(filepath_)) {
        throw common::ExtractionException(
            "JSON file not found: '" + filepath_ + "'", "streaming_json");
    }

    file_stream_.open(filepath_, std::ios::in);
    if (!file_stream_.is_open()) {
        throw common::ExtractionException(
            "Failed to open JSON file: '" + filepath_ + "'", "streaming_json");
    }

    // Start async parsing thread
    parser_thread_ = std::make_unique<std::thread>(&StreamingJsonExtractor::parse_file_async, this);

    logger->info("Started streaming extraction for file: {}", filepath_);
}

void StreamingJsonExtractor::parse_file_async() {
    auto logger = common::Logger::get("omop-streaming-json");

    try {
        JsonHandler handler(record_queue_, options_);
        json::sax_parse(file_stream_, &handler);

        parsing_complete_ = true;
        queue_cv_.notify_all();

    } catch (const std::exception& e) {
        logger->error("Error during JSON parsing: {}", e.what());
        error_message_ = e.what();
        has_error_ = true;
        parsing_complete_ = true;
        queue_cv_.notify_all();
    }
}

core::RecordBatch StreamingJsonExtractor::extract_batch(size_t batch_size,
                                                       [[maybe_unused]] core::ProcessingContext& context) {
    core::RecordBatch batch(batch_size);
    batch.reserve(batch_size);

    size_t count = 0;

    while (count < batch_size) {
        std::unique_lock<std::mutex> lock(queue_mutex_);

        // Wait for records or completion
        queue_cv_.wait(lock, [this] {
            return !record_queue_.empty() || parsing_complete_;
        });

        // Check for errors
        if (has_error_) {
            throw common::ExtractionException(
                "Streaming parse error: " + error_message_, "streaming_json");
        }

        // Extract records from queue
        while (!record_queue_.empty() && count < batch_size) {
            batch.addRecord(std::move(record_queue_.front()));
            record_queue_.pop();
            count++;
            extracted_count_++;
        }

        // Check if parsing is complete and queue is empty
        if (parsing_complete_ && record_queue_.empty()) {
            break;
        }
    }

    return batch;
}

bool StreamingJsonExtractor::has_more_data() const {
    std::unique_lock<std::mutex> lock(queue_mutex_);
    return !parsing_complete_ || !record_queue_.empty();
}

void StreamingJsonExtractor::finalize([[maybe_unused]] core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-streaming-json");

    // Wait for parser thread to complete
    if (parser_thread_ && parser_thread_->joinable()) {
        parser_thread_->join();
    }

    if (file_stream_.is_open()) {
        file_stream_.close();
    }

    auto end_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(
        end_time - start_time_).count();

    logger->info("Streaming JSON extraction completed: {} records extracted in {} seconds",
                extracted_count_, duration);
}

std::unordered_map<std::string, std::any> StreamingJsonExtractor::get_statistics() const {
    std::unordered_map<std::string, std::any> stats;

    stats["filepath"] = filepath_;
    stats["extracted_count"] = static_cast<size_t>(extracted_count_);
    stats["parsing_complete"] = parsing_complete_.load();
    stats["has_error"] = has_error_.load();

    auto current_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(
        current_time - start_time_).count();
    stats["extraction_time_seconds"] = duration;

    if (duration > 0) {
        stats["records_per_second"] = static_cast<double>(extracted_count_) / duration;
    }

    return stats;
}

// JsonExtractorFactory implementation

std::unique_ptr<core::IExtractor> JsonExtractorFactory::create(const std::string& type) {
    if (type == "json") {
        return std::make_unique<JsonExtractor>();
    } else if (type == "jsonl" || type == "json_lines") {
        return std::make_unique<JsonLinesExtractor>();
    } else if (type == "streaming_json") {
        return std::make_unique<StreamingJsonExtractor>();
    } else {
        throw common::ConfigurationException(
            "Unknown JSON extractor type: '" + type + "'");
    }
}

void JsonExtractorFactory::register_extractors() {
    // Register JSON extractors with the main factory
    ExtractorFactoryRegistry::register_type("json",
        []() { return std::make_unique<JsonExtractor>(); });

    ExtractorFactoryRegistry::register_type("jsonl",
        []() { return std::make_unique<JsonLinesExtractor>("jsonl"); });

    ExtractorFactoryRegistry::register_type("json_lines",
        []() { return std::make_unique<JsonLinesExtractor>("json_lines"); });

    ExtractorFactoryRegistry::register_type("streaming_json",
        []() { return std::make_unique<StreamingJsonExtractor>(); });

    auto logger = common::Logger::get("omop-json-extractor-factory");
    logger->info("Registered JSON extractor types: json, jsonl, json_lines, streaming_json");
}

} // namespace omop::extract

File src/lib/extract/extractor_base.h:

/**
 * @file extractor_base.h
 * @brief Abstract base class for data extractors in the OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This file contains the abstract base class for all data extractors,
 * defining the interface for extracting data from various sources.
 */

#pragma once

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <functional>
#include <any>
#include <optional>
#include <chrono>

#include "core/interfaces.h"
#include "core/record.h"
#include "common/exceptions.h"
#include "common/logging.h"
#include "common/configuration.h"
#include "common/validation.h"
#include "common/performance_monitor.h"

namespace omop::extract {

/**
 * @brief Extraction statistics
 */
struct ExtractionStats {
    size_t total_records{0};          ///< Total records processed
    size_t successful_records{0};     ///< Successfully processed records
    size_t failed_records{0};         ///< Failed records
    size_t skipped_records{0};        ///< Skipped records
    double extraction_time_seconds{0.0}; ///< Total extraction time
    std::unordered_map<std::string, size_t> error_counts; ///< Error type counts

    // Enhanced metrics
    size_t bytes_processed{0};        ///< Total bytes processed
    double records_per_second{0.0};   ///< Processing rate
    double bytes_per_second{0.0};     ///< Throughput
    size_t peak_memory_usage{0};      ///< Peak memory usage
    size_t total_memory_allocated{0}; ///< Total memory allocated
    std::chrono::system_clock::time_point start_time; ///< Start timestamp
    std::chrono::system_clock::time_point end_time;   ///< End timestamp

    // Performance metrics
    std::unordered_map<std::string, double> operation_timings; ///< Timing for operations
    std::unordered_map<std::string, size_t> operation_counts;  ///< Count of operations

    void calculate_rates();  ///< Calculate derived metrics
};

/**
 * @brief Extraction options
 */
struct ExtractionOptions {
    size_t batch_size{1000};          ///< Number of records per batch
    size_t max_records{0};            ///< Maximum records to extract (0 = no limit)
    size_t skip_records{0};           ///< Number of records to skip
    bool continue_on_error{true};     ///< Continue extraction on errors
    bool validate_schema{true};       ///< Validate source schema
    std::vector<std::string> columns; ///< Specific columns to extract (empty = all)
    std::string filter_expression;    ///< Filter expression (source-specific)
    std::unordered_map<std::string, std::any> custom_options; ///< Custom extractor options
};

/**
 * @brief Schema information for a data source
 */
struct SourceSchema {
    /**
     * @brief Column information
     */
    struct Column {
        std::string name;             ///< Column name
        std::string data_type;        ///< Data type
        bool nullable{true};          ///< Whether column is nullable
        std::optional<size_t> max_length; ///< Maximum length for string types
        std::optional<std::string> default_value; ///< Default value
        std::string description;      ///< Column description
    };

    std::string source_name;          ///< Source name/identifier
    std::string source_type;          ///< Source type (table, file, etc.)
    std::vector<Column> columns;      ///< Column definitions
    std::vector<std::string> primary_keys; ///< Primary key columns
    std::unordered_map<std::string, std::string> metadata; ///< Additional metadata
};

/**
 * @brief Standardized error handling policy
 */
class ExtractionErrorPolicy {
public:
    enum class ErrorAction {
        THROW_IMMEDIATELY,  ///< Throw exception on first error
        LOG_AND_CONTINUE,   ///< Log error and continue processing
        ACCUMULATE_ERRORS   ///< Accumulate errors for later reporting
    };

    /**
     * @brief Handle extraction error
     * @param error Error message
     * @param context Error context
     * @param logger Logger instance
     * @param action Error action to take
     * @return bool True to continue processing
     */
    static bool handle_error(const std::string& error,
                           const std::any& context,
                           std::shared_ptr<common::Logger> logger,
                           ErrorAction action = ErrorAction::LOG_AND_CONTINUE);

    static constexpr size_t MAX_ACCUMULATED_ERRORS = 1000;

private:
    // Thread-safe error accumulation container
    static thread_local std::vector<std::string> accumulated_errors_;
    static thread_local std::mutex accumulated_errors_mutex_;

public:
    /**
     * @brief Get accumulated errors for current thread
     * @return std::vector<std::string> List of accumulated errors
     */
    static std::vector<std::string> get_accumulated_errors();

    /**
     * @brief Clear accumulated errors for current thread
     */
    static void clear_accumulated_errors();
};

/**
 * @brief Abstract base class for data extractors
 *
 * This class defines the interface that all concrete extractors must implement.
 * It provides common functionality for batch processing, error handling, and
 * progress tracking.
 */
class ExtractorBase : public core::IExtractor {
public:
    /**
     * @brief Constructor
     * @param name Extractor name
     * @param config Configuration object
     * @param logger Logger instance
     */
    ExtractorBase(const std::string& name,
                  std::shared_ptr<common::ConfigurationManager> config,
                  std::shared_ptr<common::Logger> logger);

    /**
     * @brief Virtual destructor
     */
    virtual ~ExtractorBase() = default;

    /**
     * @brief Initialize the extractor
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Extract next batch of records
     * @param batch_size Maximum number of records to extract
     * @param context Processing context
     * @return RecordBatch Extracted records (empty if no more data)
     */
    core::RecordBatch extract_batch(size_t batch_size, core::ProcessingContext& context) override;

    /**
     * @brief Check if more data is available
     * @return bool True if more data can be extracted
     */
    bool has_more_data() const override;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override;

    /**
     * @brief Finalize extraction and clean up resources
     * @param context Processing context
     */
    void finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get extraction statistics
     * @return std::unordered_map<std::string, std::any> Statistics
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

    /**
     * @brief Get the schema of the data source
     * @return Source schema
     * @note This method must be implemented by derived classes
     */
    virtual SourceSchema getSchema() const = 0;

    /**
     * @brief Validate the data source
     * @return Validation result
     * @note This method must be implemented by derived classes
     */
    virtual omop::common::ValidationResult validateSource() = 0;

    /**
     * @brief Get extraction statistics (internal)
     * @return Extraction statistics
     */
    ExtractionStats getStatistics() const { return stats_; }

    /**
     * @brief Reset the extractor to initial state
     */
     virtual void reset();

    /**
     * @brief Close the extractor and release resources
     */
    virtual void close();

    /**
     * @brief Set progress callback
     * @param callback Progress callback function
     */
    void setProgressCallback(std::function<void(size_t, size_t)> callback) {
        progress_callback_ = callback;
    }

    /**
     * @brief Get extractor name
     * @return Extractor name
     */
    const std::string& getName() const { return name_; }

protected:
    /**
     * @brief Reset implementation for derived classes
     */
    virtual void resetImpl() {}

    /**
     * @brief Connect to the data source
     * @return true if connection successful
     * @note This method must be implemented by derived classes
     */
    virtual bool connect() = 0;

    /**
     * @brief Disconnect from the data source
     * @note This method must be implemented by derived classes
     */
    virtual void disconnect() = 0;

    /**
     * @brief Extract a single batch of records (implementation)
     * @param batch_size Size of batch to extract
     * @return Vector of records
     * @note This method must be implemented by derived classes
     */
    virtual std::vector<core::Record> extractBatchImpl(size_t batch_size) = 0;

    /**
     * @brief Convert source data to Record format
     * @param source_data Source data in native format
     * @return Converted record
     * @note This method must be implemented by derived classes
     */
    virtual core::Record convertToRecord(const std::any& source_data) = 0;

    /**
     * @brief Handle extraction error
     * @param error Error message
     * @param record_context Record context (if applicable)
     */
    void handleError(const std::string& error,
                    const std::optional<std::any>& record_context = std::nullopt);

    /**
     * @brief Update progress
     * @param current Current record count
     * @param total Total record count (0 if unknown)
     */
    void updateProgress(size_t current, size_t total = 0);

    /**
     * @brief Apply filter to record
     * @param record Record to filter
     * @return true if record passes filter
     */
    virtual bool applyFilter(const core::Record& record);

    /**
     * @brief Apply column selection to record
     * @param record Record to process
     * @return Record with selected columns
     */
    virtual core::Record selectColumns(const core::Record& record);

protected:
    std::string name_;                              ///< Extractor name
    std::shared_ptr<common::ConfigurationManager> config_; ///< Configuration
    std::shared_ptr<common::Logger> logger_;        ///< Logger
    ExtractionStats stats_;                         ///< Extraction statistics
    ExtractionOptions options_;                     ///< Current extraction options
    bool is_connected_{false};                      ///< Connection status
    bool is_initialized_{false};                    ///< Initialization status
    bool was_ever_initialized_{false};              ///< Whether extractor was ever initialized
    bool has_more_data_{true};                      ///< Whether more data is available
    size_t current_position_{0};                    ///< Current position in data source
    std::function<void(size_t, size_t)> progress_callback_; ///< Progress callback
    ExtractionErrorPolicy::ErrorAction error_action_{ExtractionErrorPolicy::ErrorAction::LOG_AND_CONTINUE};

private:
    std::chrono::steady_clock::time_point start_time_; ///< Extraction start time
};

} // namespace omop::extract

File src/lib/extract/database_connector.cpp:

/**
 * @file database_connector.cpp
 * @brief Implementation of database connection interfaces
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "database_connector.h"
#include "common/logging.h"
#include "common/exceptions.h"
#include "core/interfaces.h"
#include <chrono>
#include <thread>
#include <queue>
#include <condition_variable>
#include <sstream>
#include <any>
#include <regex>
#include <future>
#include <iostream>

namespace omop::extract {

// DatabaseExtractor Implementation

void DatabaseExtractor::initialize(const std::unordered_map<std::string, std::any>& config,
                                 [[maybe_unused]] core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-database-extractor");
    logger->info("Initializing database extractor");

    start_time_ = std::chrono::steady_clock::now();

    // Extract configuration parameters
    if (config.find("table") == config.end()) {
        throw common::ConfigurationException("Database extractor requires 'table' parameter");
    }
    table_name_ = std::any_cast<std::string>(config.at("table"));

    // Optional parameters
    if (config.find("schema") != config.end()) {
        schema_name_ = std::any_cast<std::string>(config.at("schema"));
    }

    if (config.find("columns") != config.end()) {
        columns_ = std::any_cast<std::vector<std::string>>(config.at("columns"));
    }

    if (config.find("filter") != config.end()) {
        filter_condition_ = std::any_cast<std::string>(config.at("filter"));

        // Validate filter condition for common injection patterns
        std::string filter_upper = filter_condition_;
        std::transform(filter_upper.begin(), filter_upper.end(), filter_upper.begin(), ::toupper);
        if (filter_upper.find("EXEC") != std::string::npos ||
            filter_upper.find("DROP") != std::string::npos ||
            filter_upper.find("CREATE") != std::string::npos ||
            filter_upper.find("ALTER") != std::string::npos) {
            throw common::SecurityException(
                "Filter condition contains potentially dangerous SQL keywords", "database");
        }
    }

    if (config.find("order_by") != config.end()) {
        order_by_ = std::any_cast<std::string>(config.at("order_by"));

        // Validate ORDER BY clause - should only contain column names and ASC/DESC
        std::regex order_by_pattern("^[a-zA-Z0-9_,\\s]+(\\s+(ASC|DESC))?$", std::regex::icase);
        if (!std::regex_match(order_by_, order_by_pattern)) {
            throw common::SecurityException("Invalid ORDER BY clause format", "database");
        }
    }

    // Verify connection
    if (!connection_->is_connected()) {
        throw common::DatabaseException("Database connection not established",
                                      connection_->get_database_type(), 0);
    }

    // Verify table exists
    if (!connection_->table_exists(table_name_, schema_name_)) {
        std::string schema_display = schema_name_.empty() ? "default" : schema_name_;
        throw common::DatabaseException(
            "Table '" + table_name_ + "' does not exist in schema '" + schema_display + "'",
            connection_->get_database_type(), 0);
    }

    // Execute initial query to get result set
    std::string query = build_query();
    logger->info("Executing extraction query: {}", query);

    try {
        current_result_set_ = connection_->execute_query(query);
        has_more_data_ = current_result_set_ != nullptr;
    } catch (const std::exception& e) {
        throw common::ExtractionException(
            "Failed to execute extraction query: " + std::string(e.what()),
            connection_->get_database_type());
    }
}

core::RecordBatch DatabaseExtractor::extract_batch(size_t batch_size,
                                                [[maybe_unused]] core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-database-extractor");
    
    if (!has_more_data_ || current_result_set_ == nullptr) {
        logger->debug("No more data to extract");
        return core::RecordBatch{};
    }

    core::RecordBatch batch(batch_size);
    size_t records_extracted = 0;

    try {
        while (records_extracted < batch_size && current_result_set_->next()) {
            core::Record record;
            
            // Extract all columns from the result set
            for (const auto& column_name : current_result_set_->get_column_names()) {
                std::any value = current_result_set_->get_value(column_name);
                record.fields[column_name] = value;
            }
            
            batch.records.push_back(std::move(record));
            records_extracted++;
        }

        // Check if we've reached the end of the result set
        if (records_extracted < batch_size) {
            has_more_data_ = false;
            current_result_set_.reset();
            logger->info("Extraction completed. Total records extracted: {}", total_records_extracted_);
        }

        total_records_extracted_ += records_extracted;
        logger->debug("Extracted batch of {} records", records_extracted);

    } catch (const std::exception& e) {
        logger->error("Error during batch extraction: {}", e.what());
        throw common::ExtractionException(
            "Failed to extract batch: " + std::string(e.what()),
            connection_->get_database_type());
    }

    return batch;
}

void DatabaseExtractor::finalize([[maybe_unused]] core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-database-extractor");

    // Clean up result set
    current_result_set_.reset();

    // Log final statistics
    auto end_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(
        end_time - start_time_).count();

    logger->info("Database extraction completed: {} records in {} seconds ({:.2f} records/sec)",
                total_records_extracted_, duration,
                duration > 0 ? static_cast<double>(total_records_extracted_) / duration : 0.0);
}

std::unordered_map<std::string, std::any> DatabaseExtractor::get_statistics() const {
    std::unordered_map<std::string, std::any> stats;

    stats["total_records"] = total_records_extracted_;
    stats["records_extracted"] = total_records_extracted_;  // Add test-expected key
    stats["batch_count"] = batch_count_;
    stats["table_name"] = table_name_;
    stats["schema_name"] = schema_name_;
    stats["database_type"] = connection_->get_database_type();

    auto current_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(
        current_time - start_time_).count();
    stats["extraction_time_seconds"] = duration;
    stats["extraction_time"] = duration;  // Add test-expected key

    return stats;
}

std::string DatabaseExtractor::apply_filters(const std::string& base_query) const {
    if (filter_condition_.empty()) {
        return base_query;
    }

    // Check if query already has WHERE clause
    std::string upper_query = base_query;
    std::transform(upper_query.begin(), upper_query.end(), upper_query.begin(), ::toupper);

    size_t where_pos = upper_query.find(" WHERE ");
    if (where_pos != std::string::npos) {
        // Append to existing WHERE clause
        return base_query + " AND " + filter_condition_;
    } else {
        // Add new WHERE clause
        return base_query + " WHERE " + filter_condition_;
    }
}

bool DatabaseExtractor::has_more_data() const {
    return has_more_data_;
}

size_t DatabaseExtractor::get_total_records_extracted() const {
    return total_records_extracted_;
}

std::chrono::milliseconds DatabaseExtractor::get_processing_time() const {
    auto end_time = std::chrono::steady_clock::now();
    return std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time_);
}

std::string DatabaseExtractor::build_query() const {
    std::stringstream query;

    // SELECT clause
    query << "SELECT ";
    if (columns_.empty()) {
        query << "*";
    } else {
        for (size_t i = 0; i < columns_.size(); ++i) {
            if (i > 0) query << ", ";
            query << columns_[i];
        }
    }

    // FROM clause
    query << " FROM ";
    if (!schema_name_.empty()) {
        query << schema_name_ << ".";
    }
    query << table_name_;

    // WHERE clause
    if (!filter_condition_.empty()) {
        query << " WHERE " << filter_condition_;
    }

    // ORDER BY clause
    if (!order_by_.empty()) {
        query << " ORDER BY " << order_by_;
    }

    // LIMIT clause
    if (limit_ > 0) {
        query << " LIMIT " << limit_;
    }

    return query.str();
}

// ConnectionPool Implementation

ConnectionPool::ConnectionPool(size_t min_connections,
                             size_t max_connections,
                             std::function<std::unique_ptr<IDatabaseConnection>()> connection_factory)
    : min_connections_(min_connections),
      max_connections_(max_connections),
      connection_factory_(connection_factory),
      total_acquisitions_(0),
      total_releases_(0),
      wait_count_(0),
      total_wait_time_(0),
      active_connections_(0),
      shutdown_(false) {
    
    auto logger = common::Logger::get("omop-connection-pool");
    logger->info("Initializing connection pool: min={}, max={}", min_connections, max_connections);
    
    // Pre-populate with minimum connections
    for (size_t i = 0; i < min_connections; ++i) {
        try {
            auto connection = connection_factory_();
            if (connection && connection->connect()) {
                idle_connections_.push(std::move(connection));
            } else {
                logger->warn("Failed to create initial connection {}", i);
            }
        } catch (const std::exception& e) {
            logger->error("Error creating initial connection {}: {}", i, e.what());
        }
    }
    
    logger->info("Connection pool initialized with {} idle connections", idle_connections_.size());
}

ConnectionPool::~ConnectionPool() {
    std::unique_lock<std::mutex> lock(mutex_);
    
    auto logger = common::Logger::get("omop-connection-pool");
    logger->info("Shutting down connection pool");
    
    shutdown_ = true;
    
    // Clear idle connections
    while (!idle_connections_.empty()) {
        idle_connections_.pop();
    }
    
    logger->info("Connection pool shutdown complete");
}

std::unique_ptr<IDatabaseConnection> ConnectionPool::acquire(int timeout_ms) {
    std::unique_lock<std::mutex> lock(mutex_);
    
    auto start_time = std::chrono::steady_clock::now();
    total_acquisitions_++;
    
    auto logger = common::Logger::get("omop-connection-pool");
    
    while (!shutdown_) {
        // Try to get an idle connection
        if (!idle_connections_.empty()) {
            auto connection = std::move(idle_connections_.front());
            idle_connections_.pop();
            active_connections_++;
            
            logger->debug("Acquired connection from pool. Active: {}, Idle: {}", 
                         active_connections_, idle_connections_.size());
            
            return connection;
        }
        
        // Try to create a new connection if under max limit
        if (active_connections_ + idle_connections_.size() < max_connections_) {
            try {
                auto connection = connection_factory_();
                if (connection && connection->connect()) {
                    active_connections_++;
                    
                    logger->debug("Created new connection. Active: {}, Idle: {}", 
                                 active_connections_, idle_connections_.size());
                    
                    return connection;
                } else {
                    logger->warn("Failed to create new connection");
                }
            } catch (const std::exception& e) {
                logger->error("Error creating new connection: {}", e.what());
            }
        }
        
        // Wait for a connection to become available
        if (timeout_ms > 0) {
            auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::steady_clock::now() - start_time);
            
            if (elapsed.count() >= timeout_ms) {
                wait_count_++;
                total_wait_time_ += elapsed.count();
                
                logger->warn("Connection acquisition timeout after {}ms", timeout_ms);
                return nullptr;
            }
            
            auto remaining_time = std::chrono::milliseconds(timeout_ms) - elapsed;
            cv_.wait_for(lock, remaining_time);
        } else {
            wait_count_++;
            cv_.wait(lock);
        }
    }
    
    logger->warn("Connection pool is shutting down");
    return nullptr;
}

void ConnectionPool::release(std::unique_ptr<IDatabaseConnection> connection) {
    if (!connection) {
        return;
    }
    
    std::unique_lock<std::mutex> lock(mutex_);
    
    auto logger = common::Logger::get("omop-connection-pool");
    
    if (shutdown_) {
        logger->debug("Pool is shutting down, discarding connection");
        return;
    }
    
    // Check if connection is still valid
    if (!connection->is_connected()) {
        logger->debug("Connection is not connected, discarding");
        active_connections_--;
        return;
    }
    
    // Return to idle pool if under max connections
    if (idle_connections_.size() < max_connections_) {
        idle_connections_.push(std::move(connection));
        active_connections_--;
        total_releases_++;
        
        logger->debug("Released connection to pool. Active: {}, Idle: {}", 
                     active_connections_, idle_connections_.size());
        
        cv_.notify_one();
    } else {
        logger->debug("Pool is full, discarding connection");
        active_connections_--;
    }
}

ConnectionPool::PoolStats ConnectionPool::get_statistics() const {
    std::unique_lock<std::mutex> lock(mutex_);
    
    PoolStats stats;
    stats.total_connections = active_connections_ + idle_connections_.size();
    stats.active_connections = active_connections_;
    stats.idle_connections = idle_connections_.size();
    stats.total_acquisitions = total_acquisitions_;
    stats.total_releases = total_releases_;
    stats.wait_count = wait_count_;
    stats.total_wait_time_ms = total_wait_time_;
    
    return stats;
}

void ConnectionPool::clear_idle_connections() {
    std::unique_lock<std::mutex> lock(mutex_);
    
    auto logger = common::Logger::get("omop-connection-pool");
    size_t cleared = idle_connections_.size();
    
    while (!idle_connections_.empty()) {
        idle_connections_.pop();
    }
    
    logger->info("Cleared {} idle connections", cleared);
}

size_t ConnectionPool::validate_connections() {
    std::unique_lock<std::mutex> lock(mutex_);
    
    auto logger = common::Logger::get("omop-connection-pool");
    size_t valid_count = 0;
    size_t invalid_count = 0;
    
    std::queue<std::unique_ptr<IDatabaseConnection>> valid_connections;
    
    while (!idle_connections_.empty()) {
        auto connection = std::move(idle_connections_.front());
        idle_connections_.pop();
        
        if (connection && connection->is_connected()) {
            valid_connections.push(std::move(connection));
            valid_count++;
        } else {
            invalid_count++;
        }
    }
    
    idle_connections_ = std::move(valid_connections);
    
    logger->info("Connection validation complete: {} valid, {} invalid", valid_count, invalid_count);
    
    return valid_count;
}

// DatabaseConnectionFactory implementation

std::unique_ptr<IDatabaseConnection> DatabaseConnectionFactory::create_from_config(
    const std::unordered_map<std::string, std::any>& config) {

    // Extract type from config
    auto type_it = config.find("type");
    if (type_it == config.end()) {
        throw std::runtime_error("Database type not specified in config");
    }
    std::string type = std::any_cast<std::string>(type_it->second);

    IDatabaseConnection::ConnectionParams params;

    // Extract connection parameters from config
    auto host_it = config.find("host");
    if (host_it != config.end()) {
        params.host = std::any_cast<std::string>(host_it->second);
    }

    auto port_it = config.find("port");
    if (port_it != config.end()) {
        params.port = std::any_cast<int>(port_it->second);
    }

    auto database_it = config.find("database");
    if (database_it != config.end()) {
        params.database = std::any_cast<std::string>(database_it->second);
    }

    auto username_it = config.find("username");
    if (username_it != config.end()) {
        params.username = std::any_cast<std::string>(username_it->second);
    }

    auto password_it = config.find("password");
    if (password_it != config.end()) {
        params.password = std::any_cast<std::string>(password_it->second);
    }

    auto options_it = config.find("options");
    if (options_it != config.end()) {
        params.options = std::any_cast<std::unordered_map<std::string, std::string>>(options_it->second);
    }

    auto connection = create(type, params);

    return connection;
}

} // namespace omop::extract

File src/lib/extract/odbc_connector.h:

/**
 * @file odbc_connector.h
 * @brief ODBC database connector interface
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This file provides the ODBC implementation of the database connector
 * interface, supporting various databases through ODBC drivers.
 */

#pragma once

#ifdef OMOP_HAS_ODBC

#include "extract/database_connector.h"
#include <sql.h>
#include <sqlext.h>
#include <memory>
#include <mutex>
#include <atomic>

namespace omop::extract {

/**
 * @brief ODBC error information
 */
struct OdbcError {
    std::string state;       ///< SQL state
    SQLINTEGER native_error; ///< Native error code
    std::string message;     ///< Error message
};

/**
 * @brief ODBC handle wrapper with RAII
 */
template<typename HandleType>
class OdbcHandle {
public:
    /**
     * @brief Constructor
     * @param handle_type SQL handle type
     * @param parent_handle Parent handle (if applicable)
     */
    OdbcHandle(SQLSMALLINT handle_type, SQLHANDLE parent_handle = SQL_NULL_HANDLE)
        : handle_type_(handle_type) {

        SQLRETURN ret = SQLAllocHandle(handle_type_, parent_handle, &handle_);
        if (!SQL_SUCCEEDED(ret)) {
            throw common::DatabaseException("Failed to allocate ODBC handle", "ODBC", ret);
        }
    }

    /**
     * @brief Destructor
     */
    ~OdbcHandle() {
        if (handle_ != SQL_NULL_HANDLE) {
            SQLFreeHandle(handle_type_, handle_);
        }
    }

    // Delete copy operations
    OdbcHandle(const OdbcHandle&) = delete;
    OdbcHandle& operator=(const OdbcHandle&) = delete;

    /**
     * @brief Move constructor
     */
    OdbcHandle(OdbcHandle&& other) noexcept
        : handle_(other.handle_), handle_type_(other.handle_type_) {
        other.handle_ = SQL_NULL_HANDLE;
    }

    /**
     * @brief Move assignment operator
     */
    OdbcHandle& operator=(OdbcHandle&& other) noexcept {
        if (this != &other) {
            if (handle_ != SQL_NULL_HANDLE) {
                SQLFreeHandle(handle_type_, handle_);
            }
            handle_ = other.handle_;
            handle_type_ = other.handle_type_;
            other.handle_ = SQL_NULL_HANDLE;
        }
        return *this;
    }

    /**
     * @brief Get raw handle
     * @return SQLHANDLE Raw ODBC handle
     */
    SQLHANDLE get() const { return handle_; }

    /**
     * @brief Implicit conversion to raw handle
     * @return SQLHANDLE Raw ODBC handle
     */
    operator SQLHANDLE() const { return handle_; }

private:
    SQLHANDLE handle_{SQL_NULL_HANDLE};
    SQLSMALLINT handle_type_;
};

using OdbcEnvironment = OdbcHandle<SQLHENV>;
using OdbcConnection = OdbcHandle<SQLHDBC>;
using OdbcStatement = OdbcHandle<SQLHSTMT>;

/**
 * @brief ODBC result set implementation
 *
 * This class provides access to ODBC query results through the
 * IResultSet interface, handling type conversions and NULL values.
 */
class OdbcResultSet : public ResultSetBase {
public:
    /**
     * @brief Constructor
     * @param statement ODBC statement handle
     */
    explicit OdbcResultSet(std::shared_ptr<OdbcStatement> statement);

    /**
     * @brief Destructor
     */
    ~OdbcResultSet() override;

    /**
     * @brief Move to next row
     * @return bool True if successful
     */
    bool next() override;

    /**
     * @brief Get column value by index
     * @param index Column index (0-based)
     * @return std::any Column value
     */
    std::any get_value(size_t index) const override;

    /**
     * @brief Get column value by name
     * @param column_name Column name
     * @return std::any Column value
     */
    std::any get_value(const std::string& column_name) const override;

    /**
     * @brief Check if column value is NULL
     * @param index Column index
     * @return bool True if NULL
     */
    bool is_null(size_t index) const override;

    /**
     * @brief Check if column value is NULL
     * @param column_name Column name
     * @return bool True if NULL
     */
    bool is_null(const std::string& column_name) const override;

    /**
     * @brief Get column count
     * @return size_t Number of columns
     */
    size_t column_count() const override;

    /**
     * @brief Get column name by index
     * @param index Column index
     * @return std::string Column name
     */
    std::string column_name(size_t index) const override;

    /**
     * @brief Get column type by index
     * @param index Column index
     * @return std::string Column type name
     */
    std::string column_type(size_t index) const override;

private:
    /**
     * @brief Column metadata
     */
    struct ColumnInfo {
        std::string name;
        SQLSMALLINT sql_type;
        SQLULEN size;
        SQLSMALLINT decimal_digits;
        SQLSMALLINT nullable;
    };

    /**
     * @brief Get column index by name
     * @param column_name Column name
     * @return size_t Column index
     * @throws DatabaseException if column not found
     */
    size_t get_column_index(const std::string& column_name) const;

    /**
     * @brief Convert ODBC value to appropriate type
     * @param index Column index
     * @return std::any Converted value
     */
    std::any convert_value(size_t index) const;

    /**
     * @brief Load column metadata
     */
    void load_metadata();

    std::shared_ptr<OdbcStatement> statement_;
    std::vector<ColumnInfo> columns_;
    mutable std::unordered_map<std::string, size_t> column_index_cache_;
    bool metadata_loaded_{false};
    mutable std::vector<SQLLEN> indicators_;  // For NULL checking
};

/**
 * @brief ODBC prepared statement implementation
 *
 * This class implements prepared statements for ODBC,
 * providing parameterized query execution across different databases.
 */
class OdbcPreparedStatement : public IPreparedStatement {
public:
    /**
     * @brief Constructor
     * @param connection ODBC connection handle
     * @param sql SQL query
     */
    OdbcPreparedStatement(std::shared_ptr<OdbcConnection> connection,
                         const std::string& sql);

    /**
     * @brief Destructor
     */
    ~OdbcPreparedStatement() override;

    /**
     * @brief Bind parameter by index
     * @param index Parameter index (1-based)
     * @param value Parameter value
     */
    void bind(size_t index, const std::any& value) override;

    /**
     * @brief Execute query and return result set
     * @return std::unique_ptr<IResultSet> Query results
     */
    std::unique_ptr<IResultSet> execute_query() override;

    /**
     * @brief Execute update/insert/delete
     * @return size_t Number of affected rows
     */
    size_t execute_update() override;

    /**
     * @brief Clear all bound parameters
     */
    void clear_parameters() override;

private:
    /**
     * @brief Parameter binding information
     */
    struct ParameterBinding {
        std::any value;
        SQLSMALLINT c_type;
        SQLSMALLINT sql_type;
        std::vector<char> buffer;
        SQLLEN indicator;
    };

    /**
     * @brief Bind parameter to statement
     * @param index Parameter index
     * @param binding Parameter binding info
     */
    void bind_parameter(size_t index, ParameterBinding& binding);

    std::shared_ptr<OdbcConnection> connection_;
    std::shared_ptr<OdbcStatement> statement_;
    std::string sql_;
    std::unordered_map<size_t, ParameterBinding> parameters_;
};

/**
 * @brief ODBC database connection implementation
 *
 * This class provides the concrete implementation of IDatabaseConnection
 * for ODBC databases, supporting various database systems through ODBC drivers.
 */
class OdbcDatabaseConnection : public IDatabaseConnection {
public:
    /**
     * @brief Constructor
     */
    OdbcDatabaseConnection();

    /**
     * @brief Destructor
     */
    ~OdbcDatabaseConnection() override;

    /**
     * @brief Connect to database
     * @param params Connection parameters
     */
    void connect(const ConnectionParams& params) override;

    /**
     * @brief Disconnect from database
     */
    void disconnect() override;

    /**
     * @brief Check if connected
     * @return bool True if connected
     */
    bool is_connected() const override;

    /**
     * @brief Execute SQL query
     * @param sql SQL query string
     * @return std::unique_ptr<IResultSet> Query results
     */
    std::unique_ptr<IResultSet> execute_query(const std::string& sql) override;

    /**
     * @brief Execute SQL update/insert/delete
     * @param sql SQL statement
     * @return size_t Number of affected rows
     */
    size_t execute_update(const std::string& sql) override;

    /**
     * @brief Prepare SQL statement
     * @param sql SQL statement with parameter placeholders
     * @return std::unique_ptr<IPreparedStatement> Prepared statement
     */
    std::unique_ptr<IPreparedStatement> prepare_statement(const std::string& sql) override;

    /**
     * @brief Begin transaction
     */
    void begin_transaction() override;

    /**
     * @brief Commit transaction
     */
    void commit() override;

    /**
     * @brief Rollback transaction
     */
    void rollback() override;

    /**
     * @brief Check if in transaction
     * @return bool True if in transaction
     */
    bool in_transaction() const override { return in_transaction_; }

    /**
     * @brief Get database type name
     * @return std::string Database type
     */
    std::string get_database_type() const override;

    /**
     * @brief Get database version
     * @return std::string Database version string
     */
    std::string get_version() const override;

    /**
     * @brief Set query timeout
     * @param seconds Timeout in seconds
     */
    void set_query_timeout(int seconds) override;

    /**
     * @brief Check if table exists
     * @param table_name Table name
     * @param schema Schema name (optional)
     * @return bool True if table exists
     */
    bool table_exists(const std::string& table_name,
                     const std::string& schema = "") const override;

    /**
     * @brief Get ODBC error information
     * @param handle_type Handle type
     * @param handle ODBC handle
     * @return std::vector<OdbcError> Error information
     */
    static std::vector<OdbcError> get_odbc_errors(SQLSMALLINT handle_type,
                                                  SQLHANDLE handle);

private:
    /**
     * @brief Build connection string from parameters
     * @param params Connection parameters
     * @return std::string ODBC connection string
     */
    std::string build_connection_string(const ConnectionParams& params) const;

    /**
     * @brief Build DSN connection string
     * @param params Connection parameters
     * @return std::string DSN connection string
     */
    std::string build_dsn_string(const ConnectionParams& params) const;

    /**
     * @brief Check for ODBC errors and throw if needed
     * @param ret ODBC return code
     * @param operation Operation description
     * @param handle_type Handle type
     * @param handle ODBC handle
     */
    void check_error(SQLRETURN ret, const std::string& operation,
                    SQLSMALLINT handle_type, SQLHANDLE handle) const;

public:
    /**
     * @brief Get SQL type name
     * @param sql_type SQL type code
     * @return std::string Type name
     */
    static std::string get_sql_type_name(SQLSMALLINT sql_type);

private:

    std::shared_ptr<OdbcEnvironment> environment_;
    std::shared_ptr<OdbcConnection> connection_;
    bool connected_{false};
    bool in_transaction_{false};
    std::string database_name_;
    std::string driver_name_;
    mutable std::mutex connection_mutex_;
};

/**
 * @brief ODBC-specific database extractor
 *
 * This class extends DatabaseExtractor with ODBC-specific optimizations
 * for different database systems.
 */
class OdbcExtractor : public DatabaseExtractor {
public:
    /**
     * @brief Constructor
     * @param connection ODBC connection
     */
    explicit OdbcExtractor(std::unique_ptr<IDatabaseConnection> connection)
        : DatabaseExtractor(std::move(connection)) {}

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "odbc"; }

protected:
    /**
     * @brief Build extraction query with ODBC-specific optimizations
     * @return std::string SQL query
     */
    std::string build_query() const override;
};

/**
 * @brief ODBC driver manager
 *
 * Manages ODBC driver discovery and configuration
 */
class OdbcDriverManager {
public:
    /**
     * @brief Driver information
     */
    struct DriverInfo {
        std::string name;
        std::string description;
        std::unordered_map<std::string, std::string> attributes;
    };

    /**
     * @brief Data source information
     */
    struct DataSourceInfo {
        std::string name;
        std::string description;
        std::string driver;
    };

    /**
     * @brief Get available ODBC drivers
     * @return std::vector<DriverInfo> Available drivers
     */
    static std::vector<DriverInfo> get_available_drivers();

    /**
     * @brief Get configured data sources
     * @return std::vector<DataSourceInfo> Data sources
     */
    static std::vector<DataSourceInfo> get_data_sources();

    /**
     * @brief Test ODBC connection
     * @param connection_string Connection string
     * @return std::pair<bool, std::string> Success flag and message
     */
    static std::pair<bool, std::string> test_connection(
        const std::string& connection_string);
};

} // namespace omop::extract

#endif // OMOP_HAS_ODBC

File src/lib/extract/postgresql_connector.h:

#pragma once

#include "extract/database_connector.h"
#include <libpq-fe.h>
#include <memory>
#include <mutex>

namespace omop::extract {

/**
 * @brief PostgreSQL result set implementation
 *
 * This class provides access to PostgreSQL query results through the
 * IResultSet interface, handling type conversions and NULL values.
 */
class PostgreSQLResultSet : public ResultSetBase {
public:
    /**
     * @brief Constructor
     * @param result PostgreSQL result handle
     */
    explicit PostgreSQLResultSet(PGresult* result);

    /**
     * @brief Destructor
     */
    ~PostgreSQLResultSet() override;

    /**
     * @brief Move to next row
     * @return bool True if successful
     */
    bool next() override;

    /**
     * @brief Get column value by index
     * @param index Column index
     * @return std::any Column value
     */
    std::any get_value(size_t index) const override;

    /**
     * @brief Get column value by name
     * @param column_name Column name
     * @return std::any Column value
     */
    std::any get_value(const std::string& column_name) const override;

    /**
     * @brief Check if column value is NULL
     * @param index Column index
     * @return bool True if NULL
     */
    bool is_null(size_t index) const override;

    /**
     * @brief Check if column value is NULL
     * @param column_name Column name
     * @return bool True if NULL
     */
    bool is_null(const std::string& column_name) const override;

    /**
     * @brief Get column count
     * @return size_t Number of columns
     */
    size_t column_count() const override;

    /**
     * @brief Get column name by index
     * @param index Column index
     * @return std::string Column name
     */
    std::string column_name(size_t index) const override;

    /**
     * @brief Get column type by index
     * @param index Column index
     * @return std::string Column type name
     */
    std::string column_type(size_t index) const override;

private:
    /**
     * @brief Get column index by name
     * @param column_name Column name
     * @return size_t Column index
     * @throws DatabaseException if column not found
     */
    size_t get_column_index(const std::string& column_name) const;

    /**
     * @brief Convert PostgreSQL value to appropriate type
     * @param value String value from PostgreSQL
     * @param oid PostgreSQL type OID
     * @return std::any Converted value
     */
    std::any convert_value(const char* value, Oid oid) const;

    PGresult* result_;
    int row_count_;
    int current_row_;
    int column_count_;
    mutable std::unordered_map<std::string, size_t> column_index_cache_;
};

/**
 * @brief PostgreSQL prepared statement implementation
 *
 * This class implements prepared statements for PostgreSQL,
 * providing parameterized query execution.
 */
class PostgreSQLPreparedStatement : public IPreparedStatement {
public:
    /**
     * @brief Constructor
     * @param connection PostgreSQL connection
     * @param statement_name Prepared statement name
     * @param sql SQL query
     */
    PostgreSQLPreparedStatement(PGconn* connection,
                              const std::string& statement_name,
                              const std::string& sql);

    /**
     * @brief Destructor
     */
    ~PostgreSQLPreparedStatement() override;

    /**
     * @brief Bind parameter by index
     * @param index Parameter index (1-based)
     * @param value Parameter value
     */
    void bind(size_t index, const std::any& value) override;

    /**
     * @brief Execute query and return result set
     * @return std::unique_ptr<IResultSet> Query results
     */
    std::unique_ptr<IResultSet> execute_query() override;

    /**
     * @brief Execute update/insert/delete
     * @return size_t Number of affected rows
     */
    size_t execute_update() override;

    /**
     * @brief Clear all bound parameters
     */
    void clear_parameters() override;

private:
    /**
     * @brief Convert parameter value to string
     * @param value Parameter value
     * @return std::string String representation
     */
    std::string convert_parameter(const std::any& value) const;

    PGconn* connection_;
    std::string statement_name_;
    std::string sql_;
    std::vector<std::string> parameters_;
    std::vector<const char*> param_values_;
    std::vector<int> param_lengths_;
    std::vector<int> param_formats_;
};

/**
 * @brief PostgreSQL database connection implementation
 *
 * This class provides the concrete implementation of IDatabaseConnection
 * for PostgreSQL databases, using libpq for database operations.
 */
class PostgreSQLConnection : public IDatabaseConnection {
public:
    /**
     * @brief Constructor
     */
    PostgreSQLConnection();

    /**
     * @brief Destructor
     */
    ~PostgreSQLConnection() override;

    /**
     * @brief Connect to database
     * @param params Connection parameters
     */
    void connect(const ConnectionParams& params) override;

    /**
     * @brief Disconnect from database
     */
    void disconnect() override;

    /**
     * @brief Check if connected
     * @return bool True if connected
     */
    bool is_connected() const override;

    /**
     * @brief Execute SQL query
     * @param sql SQL query string
     * @return std::unique_ptr<IResultSet> Query results
     */
    std::unique_ptr<IResultSet> execute_query(const std::string& sql) override;

    /**
     * @brief Execute SQL update/insert/delete
     * @param sql SQL statement
     * @return size_t Number of affected rows
     */
    size_t execute_update(const std::string& sql) override;

    /**
     * @brief Prepare SQL statement
     * @param sql SQL statement with parameter placeholders
     * @return std::unique_ptr<IPreparedStatement> Prepared statement
     */
    std::unique_ptr<IPreparedStatement> prepare_statement(const std::string& sql) override;

    /**
     * @brief Begin transaction
     */
    void begin_transaction() override;

    /**
     * @brief Commit transaction
     */
    void commit() override;

    /**
     * @brief Rollback transaction
     */
    void rollback() override;

    /**
     * @brief Get database type name
     * @return std::string Database type
     */
    std::string get_database_type() const override { return "PostgreSQL"; }

    /**
     * @brief Get database version
     * @return std::string Database version string
     */
    std::string get_version() const override;

    /**
     * @brief Set query timeout
     * @param seconds Timeout in seconds
     */
    void set_query_timeout(int seconds) override;

    /**
     * @brief Check if table exists
     * @param table_name Table name
     * @param schema Schema name (optional)
     * @return bool True if table exists
     */
    bool table_exists(const std::string& table_name,
                     const std::string& schema = "") const override;

    /**
     * @brief Get raw PostgreSQL connection handle
     * @return PGconn* Connection handle (for internal use)
     */
    PGconn* get_raw_connection() { return connection_; }

    bool in_transaction() const override { return in_transaction_; }

private:
    /**
     * @brief Build connection string from parameters
     * @param params Connection parameters
     * @return std::string PostgreSQL connection string
     */
    std::string build_connection_string(const ConnectionParams& params) const;

    /**
     * @brief Check for PostgreSQL errors
     * @param result Query result
     * @param operation Operation description
     */
    void check_error(PGresult* result, const std::string& operation) const;

    /**
     * @brief Generate unique statement name
     * @return std::string Unique statement name
     */
    std::string generate_statement_name();

    PGconn* connection_;
    bool in_transaction_;
    int query_timeout_;
    mutable std::mutex connection_mutex_;
    std::atomic<int> statement_counter_;
};

/**
 * @brief PostgreSQL-specific database extractor
 *
 * This class extends DatabaseExtractor with PostgreSQL-specific optimizations
 * such as cursor-based extraction for large result sets.
 */
class PostgreSQLExtractor : public DatabaseExtractor {
public:
    /**
     * @brief Constructor
     * @param connection PostgreSQL connection
     */
    explicit PostgreSQLExtractor(std::unique_ptr<IDatabaseConnection> connection)
        : DatabaseExtractor(std::move(connection)) {}

    /**
     * @brief Constructor with type
     * @param connection PostgreSQL connection
     * @param type The type string used to create this extractor
     */
    PostgreSQLExtractor(std::unique_ptr<IDatabaseConnection> connection, const std::string& type)
        : DatabaseExtractor(std::move(connection)), type_(type) {}

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return type_.empty() ? "postgresql" : type_; }

protected:
    /**
     * @brief Build extraction query with PostgreSQL-specific optimizations
     * @return std::string SQL query
     */
    std::string build_query() const override;

    /**
     * @brief Apply PostgreSQL-specific query hints
     * @param query Base query
     * @return std::string Query with hints
     */
    std::string apply_query_hints(const std::string& query) const;

private:
    std::string type_{"postgresql"};  // Default type
};

/**
 * @brief Registration helper for PostgreSQL components
 *
 * This class handles the registration of PostgreSQL components
 * with the appropriate factories during application startup.
 */
class PostgreSQLRegistrar {
public:
    /**
     * @brief Register all PostgreSQL components
     */
    static void register_components() {
        DatabaseConnectionFactory::instance().register_type(
            "postgresql",
            [](const IDatabaseConnection::ConnectionParams& params) {
                auto conn = std::make_unique<PostgreSQLConnection>();
                conn->connect(params);
                return conn;
            }
        );

        DatabaseConnectionFactory::instance().register_type(
            "postgres",  // Alias
            [](const IDatabaseConnection::ConnectionParams& params) {
                auto conn = std::make_unique<PostgreSQLConnection>();
                conn->connect(params);
                return conn;
            }
        );
    }

private:
    PostgreSQLRegistrar() = default;
};

} // namespace omop::extract
```