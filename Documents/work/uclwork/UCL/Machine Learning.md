ML Techniques:

1. Classification: Deep learning, transfer learning, classifiers, pre-trained classifiers
2. Regression: Statistical models, linear models, linear regression algorithms, logistic regression algorithms
3. Clustering: 

3. Decision tree algorithm
4. Random forest algorithm
5. K-means clustering algorithm
6. K-nearest neighbours algorithm
7. <PERSON><PERSON> algorithm
8. Time series analysis
9. Image recognition algorithms
10. Ensemble machine learning, base learners, recommender systems

ML computer applications, application libraries, and application platforms:

mlpack: fast, flexible C++ machine learning library
dlib: 
Shark: Shark is a fast, modular, feature-rich open-source C++ machine learning library.
It provides methods for linear and nonlinear optimisation, kernel-based learning algorithms, neural networks, and various other machine learning techniques.
TensorFlow: The core open source library to help you develop and train ML models. C++
OpenCV: 
PyTorch: An open source machine learning framework that accelerates the path from research prototyping to production deployment. Python, C++.
Core ML: Integrate machine learning models into your app. Xcode, C++
Create ML: Create machine learning models for use in your app. Xcode, C++, Spark
healthcare.ai: The healthcare.ai software is designed to streamline healthcare machine learning. They do this by including functionality specific to healthcare, as well as simplifying the workflow of creating and deploying models.
Mathworks: Machine Learning with MATLAB

ML books, training, and tutorials:

mlpack (kmeans): 
dlib (kmeans): 
Shark (Regression): 

TensorFlow: https://www.tensorflow.org/, https://itnext.io/how-to-use-your-c-muscle-using-tensorflow-2-0-and-xcode-without-using-bazel-builds-9dc82d5e7f80

OpenCV (Digital Imaging): 

Core ML: https://developer.apple.com/documentation/coreml

Create ML: https://developer.apple.com/documentation/createml

healthcare.ai: https://healthcare.ai/

Introducing Create ML: https://developer.apple.com/videos/play/wwdc2018/703/

Vision with Core ML: https://developer.apple.com/videos/play/wwdc2018/717

Image Classifier: https://apple.github.io/turicreate/docs/userguide/image_classifier/how-it-works.html

https://www.learnopencv.com/how-to-train-a-deep-learning-based-image-classifier-in-macos/

Machine Learning with MATLAB: https://uk.mathworks.com/campaigns/offers/machine-learning-with-matlab.confirmation.html?elqsid=1568037483791&potential_use=Education

Deep Learning with C++ - Peter Goldsborough - Meeting C++ 2017: https://youtu.be/8GoYXWOq55A

References:

The U.S. Department of Energy’s Oak Ridge National Laboratory (ORNL) and the Department of Veterans Affairs (VA) have jointly engineered an expanded iteration of the medication possession ratio algorithm, a predictive model for identifying veterans at risk of suicide.

Google Scholar: https://scholar.google.co.uk/scholar?q=Machine+Learning+For+Healthcare&hl=en&as_sdt=0&as_vis=1&oi=scholart

MACHINE LEARNING FOR HEALTHCARE, Stanford University: http://mucmd.org/

6.S897/HST.956: Machine Learning for Healthcare, MIT: https://mlhc19mit.github.io/

Nature Machine Learning in healthcare: https://www.nature.com/collections/zbkpvddmhm

Small data sets and image processing using machine learning: https://www.nature.com/articles/s41551-018-0324-9

Spotting brain bleeding after sparse training: https://www.nature.com/articles/s41551-019-0368-5

Towards trustable machine learning: https://www.nature.com/articles/s41551-018-0315-x

Auspicious machine learning: https://www.nature.com/articles/s41551-017-0036

Knowledge Spillovers of Medical Big Data Under Hierarchical Medical System and Patients’ Medical Treatment Decisions: https://ieeexplore.ieee.org/document/8678630

Diagnostic imaging: Intra operative virtual histology: https://www.nature.com/articles/s41551-017-0033

Medical Diagnostic Tests: A Review of Test Anatomy, Phases, and Statistical Treatment of Data: https://www.hindawi.com/journals/cmmm/2019/1891569/

The role of big data in medicine, McKinsey: https://www.mckinsey.com/industries/pharmaceuticals-and-medical-products/our-insights/the-role-of-big-data-in-medicine

Machine learning and therapeutics 2.0: Avoiding hype, realizing potential, McKinsey: https://www.mckinsey.com/industries/pharmaceuticals-and-medical-products/our-insights/machine-learning-and-therapeutics-2-0-avoiding-hype-realizing-potential

How Big Data Is Changing Healthcare, Forbes: https://www.forbes.com/sites/bernardmarr/2015/04/21/how-big-data-is-changing-healthcare/#1ad4cea28730

Big data and machine learning algorithms for health-care delivery, Lancet: https://www.thelancet.com/journals/lanonc/article/PIIS1470-2045(19)30149-4/fulltext

INTRODUCING MACHINE LEARNING FOR HEALTHCARE RESEARCH, Nottingham University: https://www.nottingham.ac.uk/research/groups/primarycarestratifiedmedicine/documents/s-weng-machine-learning-presentation-25.1.18.pdf

Machine Learning for Healthcare 2020, Duke University School of Medicine, Durham, NC: https://www.mlforhc.org/

Machine Learning Healthcare Applications – 2018 and Beyond: https://emerj.com/ai-sector-overviews/machine-learning-healthcare-applications/


