{"format_version": "1.0", "provider_schemas": {"registry.terraform.io/harvester/harvester": {"provider": {"version": 0, "block": {"attributes": {"kubeconfig": {"type": "string", "description": "kubeconfig file path, users can use the KUBECONFIG environment variable instead", "description_kind": "plain", "optional": true}, "kubecontext": {"type": "string", "description": "name of the kubernetes context to use", "description_kind": "plain", "optional": true}}, "description_kind": "plain"}}, "resource_schemas": {"harvester_cloudinit_secret": {"version": 0, "block": {"attributes": {"description": {"type": "string", "description": "Any text you want that better describes this resource", "description_kind": "plain", "optional": true}, "id": {"type": "string", "description_kind": "plain", "optional": true, "computed": true}, "message": {"type": "string", "description_kind": "plain", "computed": true}, "name": {"type": "string", "description": "A unique name", "description_kind": "plain", "required": true}, "namespace": {"type": "string", "description_kind": "plain", "optional": true}, "network_data": {"type": "string", "description_kind": "plain", "optional": true}, "network_data_base64": {"type": "string", "description_kind": "plain", "optional": true}, "state": {"type": "string", "description_kind": "plain", "computed": true}, "tags": {"type": ["map", "string"], "description_kind": "plain", "optional": true}, "user_data": {"type": "string", "description_kind": "plain", "optional": true}, "user_data_base64": {"type": "string", "description_kind": "plain", "optional": true}}, "block_types": {"timeouts": {"nesting_mode": "single", "block": {"attributes": {"create": {"type": "string", "description_kind": "plain", "optional": true}, "default": {"type": "string", "description_kind": "plain", "optional": true}, "delete": {"type": "string", "description_kind": "plain", "optional": true}, "read": {"type": "string", "description_kind": "plain", "optional": true}, "update": {"type": "string", "description_kind": "plain", "optional": true}}, "description_kind": "plain"}}}, "description_kind": "plain"}}, "harvester_clusternetwork": {"version": 0, "block": {"attributes": {"description": {"type": "string", "description": "Any text you want that better describes this resource", "description_kind": "plain", "optional": true}, "id": {"type": "string", "description_kind": "plain", "optional": true, "computed": true}, "message": {"type": "string", "description_kind": "plain", "computed": true}, "name": {"type": "string", "description": "A unique name", "description_kind": "plain", "required": true}, "state": {"type": "string", "description_kind": "plain", "computed": true}, "tags": {"type": ["map", "string"], "description_kind": "plain", "optional": true}}, "block_types": {"timeouts": {"nesting_mode": "single", "block": {"attributes": {"create": {"type": "string", "description_kind": "plain", "optional": true}, "default": {"type": "string", "description_kind": "plain", "optional": true}, "delete": {"type": "string", "description_kind": "plain", "optional": true}, "read": {"type": "string", "description_kind": "plain", "optional": true}, "update": {"type": "string", "description_kind": "plain", "optional": true}}, "description_kind": "plain"}}}, "description_kind": "plain"}}, "harvester_image": {"version": 0, "block": {"attributes": {"description": {"type": "string", "description": "Any text you want that better describes this resource", "description_kind": "plain", "optional": true}, "display_name": {"type": "string", "description_kind": "plain", "required": true}, "id": {"type": "string", "description_kind": "plain", "optional": true, "computed": true}, "message": {"type": "string", "description_kind": "plain", "computed": true}, "name": {"type": "string", "description": "A unique name", "description_kind": "plain", "required": true}, "namespace": {"type": "string", "description_kind": "plain", "optional": true}, "progress": {"type": "number", "description_kind": "plain", "computed": true}, "pvc_name": {"type": "string", "description_kind": "plain", "optional": true}, "pvc_namespace": {"type": "string", "description_kind": "plain", "optional": true}, "size": {"type": "number", "description_kind": "plain", "computed": true}, "source_type": {"type": "string", "description_kind": "plain", "required": true}, "state": {"type": "string", "description_kind": "plain", "computed": true}, "storage_class_name": {"type": "string", "description_kind": "plain", "optional": true}, "storage_class_parameters": {"type": ["map", "string"], "description_kind": "plain", "computed": true}, "tags": {"type": ["map", "string"], "description_kind": "plain", "optional": true}, "url": {"type": "string", "description": "supports the `raw` and `qcow2` image formats which are supported by [qemu](https://www.qemu.org/docs/master/system/images.html#disk-image-file-formats). Bootable ISO images can also be used and are treated like `raw` images.", "description_kind": "plain", "optional": true}, "volume_storage_class_name": {"type": "string", "description_kind": "plain", "computed": true}}, "block_types": {"timeouts": {"nesting_mode": "single", "block": {"attributes": {"create": {"type": "string", "description_kind": "plain", "optional": true}, "default": {"type": "string", "description_kind": "plain", "optional": true}, "delete": {"type": "string", "description_kind": "plain", "optional": true}, "read": {"type": "string", "description_kind": "plain", "optional": true}, "update": {"type": "string", "description_kind": "plain", "optional": true}}, "description_kind": "plain"}}}, "description_kind": "plain"}}, "harvester_network": {"version": 0, "block": {"attributes": {"cluster_network_name": {"type": "string", "description_kind": "plain", "required": true}, "config": {"type": "string", "description_kind": "plain", "optional": true, "computed": true}, "description": {"type": "string", "description": "Any text you want that better describes this resource", "description_kind": "plain", "optional": true}, "id": {"type": "string", "description_kind": "plain", "optional": true, "computed": true}, "message": {"type": "string", "description_kind": "plain", "computed": true}, "name": {"type": "string", "description": "A unique name", "description_kind": "plain", "required": true}, "namespace": {"type": "string", "description_kind": "plain", "optional": true}, "route_cidr": {"type": "string", "description": "e.g. **********/24", "description_kind": "plain", "optional": true, "computed": true}, "route_connectivity": {"type": "string", "description_kind": "plain", "computed": true}, "route_dhcp_server_ip": {"type": "string", "description_kind": "plain", "optional": true}, "route_gateway": {"type": "string", "description": "e.g. **********", "description_kind": "plain", "optional": true, "computed": true}, "route_mode": {"type": "string", "description_kind": "plain", "optional": true}, "state": {"type": "string", "description_kind": "plain", "computed": true}, "tags": {"type": ["map", "string"], "description_kind": "plain", "optional": true}, "vlan_id": {"type": "number", "description": "e.g. 0-4094", "description_kind": "plain", "required": true}}, "block_types": {"timeouts": {"nesting_mode": "single", "block": {"attributes": {"create": {"type": "string", "description_kind": "plain", "optional": true}, "default": {"type": "string", "description_kind": "plain", "optional": true}, "delete": {"type": "string", "description_kind": "plain", "optional": true}, "read": {"type": "string", "description_kind": "plain", "optional": true}, "update": {"type": "string", "description_kind": "plain", "optional": true}}, "description_kind": "plain"}}}, "description_kind": "plain"}}, "harvester_ssh_key": {"version": 0, "block": {"attributes": {"description": {"type": "string", "description": "Any text you want that better describes this resource", "description_kind": "plain", "optional": true}, "fingerprint": {"type": "string", "description_kind": "plain", "computed": true}, "id": {"type": "string", "description_kind": "plain", "optional": true, "computed": true}, "message": {"type": "string", "description_kind": "plain", "computed": true}, "name": {"type": "string", "description": "A unique name", "description_kind": "plain", "required": true}, "namespace": {"type": "string", "description_kind": "plain", "optional": true}, "public_key": {"type": "string", "description_kind": "plain", "required": true}, "state": {"type": "string", "description_kind": "plain", "computed": true}, "tags": {"type": ["map", "string"], "description_kind": "plain", "optional": true}}, "block_types": {"timeouts": {"nesting_mode": "single", "block": {"attributes": {"create": {"type": "string", "description_kind": "plain", "optional": true}, "default": {"type": "string", "description_kind": "plain", "optional": true}, "delete": {"type": "string", "description_kind": "plain", "optional": true}, "read": {"type": "string", "description_kind": "plain", "optional": true}, "update": {"type": "string", "description_kind": "plain", "optional": true}}, "description_kind": "plain"}}}, "description_kind": "plain"}}, "harvester_storageclass": {"version": 0, "block": {"attributes": {"allow_volume_expansion": {"type": "bool", "description_kind": "plain", "optional": true}, "description": {"type": "string", "description": "Any text you want that better describes this resource", "description_kind": "plain", "optional": true}, "id": {"type": "string", "description_kind": "plain", "optional": true, "computed": true}, "is_default": {"type": "bool", "description_kind": "plain", "optional": true}, "message": {"type": "string", "description_kind": "plain", "computed": true}, "name": {"type": "string", "description": "A unique name", "description_kind": "plain", "required": true}, "parameters": {"type": ["map", "string"], "description": "refer to https://longhorn.io/docs/latest/volumes-and-nodes/storage-tags. \"migratable\": \"true\" is required for Harvester Virtual Machine LiveMigration", "description_kind": "plain", "required": true}, "reclaim_policy": {"type": "string", "description_kind": "plain", "optional": true}, "state": {"type": "string", "description_kind": "plain", "computed": true}, "tags": {"type": ["map", "string"], "description_kind": "plain", "optional": true}, "volume_binding_mode": {"type": "string", "description_kind": "plain", "optional": true}, "volume_provisioner": {"type": "string", "description_kind": "plain", "optional": true}}, "block_types": {"timeouts": {"nesting_mode": "single", "block": {"attributes": {"create": {"type": "string", "description_kind": "plain", "optional": true}, "default": {"type": "string", "description_kind": "plain", "optional": true}, "delete": {"type": "string", "description_kind": "plain", "optional": true}, "read": {"type": "string", "description_kind": "plain", "optional": true}, "update": {"type": "string", "description_kind": "plain", "optional": true}}, "description_kind": "plain"}}}, "description_kind": "plain"}}, "harvester_virtualmachine": {"version": 0, "block": {"attributes": {"cpu": {"type": "number", "description_kind": "plain", "optional": true}, "description": {"type": "string", "description": "Any text you want that better describes this resource", "description_kind": "plain", "optional": true}, "efi": {"type": "bool", "description_kind": "plain", "optional": true}, "hostname": {"type": "string", "description_kind": "plain", "optional": true, "computed": true}, "id": {"type": "string", "description_kind": "plain", "optional": true, "computed": true}, "machine_type": {"type": "string", "description_kind": "plain", "optional": true, "computed": true}, "memory": {"type": "string", "description_kind": "plain", "optional": true}, "message": {"type": "string", "description_kind": "plain", "computed": true}, "name": {"type": "string", "description": "A unique name", "description_kind": "plain", "required": true}, "namespace": {"type": "string", "description_kind": "plain", "optional": true}, "node_name": {"type": "string", "description_kind": "plain", "computed": true}, "reserved_memory": {"type": "string", "description_kind": "plain", "optional": true}, "restart_after_update": {"type": "bool", "description": "restart vm after the vm is updated", "description_kind": "plain", "optional": true}, "run_strategy": {"type": "string", "description": "more info: https://kubevirt.io/user-guide/virtual_machines/run_strategies/", "description_kind": "plain", "optional": true}, "secure_boot": {"type": "bool", "description": "EFI must be enabled to use this feature", "description_kind": "plain", "optional": true}, "ssh_keys": {"type": ["list", "string"], "description_kind": "plain", "optional": true}, "start": {"type": "bool", "description_kind": "plain", "deprecated": true, "optional": true}, "state": {"type": "string", "description_kind": "plain", "computed": true}, "tags": {"type": ["map", "string"], "description_kind": "plain", "optional": true}}, "block_types": {"cloudinit": {"nesting_mode": "list", "block": {"attributes": {"network_data": {"type": "string", "description_kind": "plain", "optional": true}, "network_data_base64": {"type": "string", "description_kind": "plain", "optional": true}, "network_data_secret_name": {"type": "string", "description_kind": "plain", "optional": true}, "type": {"type": "string", "description_kind": "plain", "optional": true}, "user_data": {"type": "string", "description_kind": "plain", "optional": true}, "user_data_base64": {"type": "string", "description_kind": "plain", "optional": true}, "user_data_secret_name": {"type": "string", "description_kind": "plain", "optional": true}}, "description_kind": "plain"}, "max_items": 1}, "disk": {"nesting_mode": "list", "block": {"attributes": {"access_mode": {"type": "string", "description_kind": "plain", "optional": true, "computed": true}, "auto_delete": {"type": "bool", "description_kind": "plain", "optional": true, "computed": true}, "boot_order": {"type": "number", "description_kind": "plain", "optional": true}, "bus": {"type": "string", "description_kind": "plain", "optional": true, "computed": true}, "container_image_name": {"type": "string", "description_kind": "plain", "optional": true}, "existing_volume_name": {"type": "string", "description_kind": "plain", "optional": true}, "hot_plug": {"type": "bool", "description_kind": "plain", "optional": true, "computed": true}, "image": {"type": "string", "description_kind": "plain", "optional": true}, "name": {"type": "string", "description_kind": "plain", "required": true}, "size": {"type": "string", "description_kind": "plain", "optional": true}, "storage_class_name": {"type": "string", "description_kind": "plain", "optional": true, "computed": true}, "type": {"type": "string", "description_kind": "plain", "optional": true}, "volume_mode": {"type": "string", "description_kind": "plain", "optional": true, "computed": true}, "volume_name": {"type": "string", "description_kind": "plain", "optional": true, "computed": true}}, "description_kind": "plain"}, "min_items": 1}, "input": {"nesting_mode": "list", "block": {"attributes": {"bus": {"type": "string", "description_kind": "plain", "optional": true}, "name": {"type": "string", "description_kind": "plain", "required": true}, "type": {"type": "string", "description_kind": "plain", "optional": true}}, "description_kind": "plain"}}, "network_interface": {"nesting_mode": "list", "block": {"attributes": {"interface_name": {"type": "string", "description_kind": "plain", "computed": true}, "ip_address": {"type": "string", "description_kind": "plain", "computed": true}, "mac_address": {"type": "string", "description_kind": "plain", "optional": true, "computed": true}, "model": {"type": "string", "description_kind": "plain", "optional": true}, "name": {"type": "string", "description_kind": "plain", "required": true}, "network_name": {"type": "string", "description": "if the value is empty, management network is used", "description_kind": "plain", "optional": true}, "type": {"type": "string", "description_kind": "plain", "optional": true, "computed": true}, "wait_for_lease": {"type": "bool", "description": "wait for this network interface to obtain an IP address. If a non-management network is used, this feature requires qemu-guest-agent installed and started in the VM, otherwise, VM creation will stuck until timeout", "description_kind": "plain", "optional": true}}, "description_kind": "plain"}, "min_items": 1}, "timeouts": {"nesting_mode": "single", "block": {"attributes": {"create": {"type": "string", "description_kind": "plain", "optional": true}, "default": {"type": "string", "description_kind": "plain", "optional": true}, "delete": {"type": "string", "description_kind": "plain", "optional": true}, "read": {"type": "string", "description_kind": "plain", "optional": true}, "update": {"type": "string", "description_kind": "plain", "optional": true}}, "description_kind": "plain"}}, "tpm": {"nesting_mode": "list", "block": {"attributes": {"name": {"type": "string", "description": "just add this field for doc generation", "description_kind": "plain", "optional": true}}, "description_kind": "plain"}, "max_items": 1}}, "description_kind": "plain"}}, "harvester_vlanconfig": {"version": 0, "block": {"attributes": {"cluster_network_name": {"type": "string", "description": "mgmt is a built-in cluster network and does not support creating/updating network configs.", "description_kind": "plain", "required": true}, "description": {"type": "string", "description": "Any text you want that better describes this resource", "description_kind": "plain", "optional": true}, "id": {"type": "string", "description_kind": "plain", "optional": true, "computed": true}, "matched_nodes": {"type": ["list", "string"], "description_kind": "plain", "computed": true}, "message": {"type": "string", "description_kind": "plain", "computed": true}, "name": {"type": "string", "description": "A unique name", "description_kind": "plain", "required": true}, "node_selector": {"type": ["map", "string"], "description": "refer to https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#nodeselector", "description_kind": "plain", "optional": true}, "state": {"type": "string", "description_kind": "plain", "computed": true}, "tags": {"type": ["map", "string"], "description_kind": "plain", "optional": true}}, "block_types": {"timeouts": {"nesting_mode": "single", "block": {"attributes": {"create": {"type": "string", "description_kind": "plain", "optional": true}, "default": {"type": "string", "description_kind": "plain", "optional": true}, "delete": {"type": "string", "description_kind": "plain", "optional": true}, "read": {"type": "string", "description_kind": "plain", "optional": true}, "update": {"type": "string", "description_kind": "plain", "optional": true}}, "description_kind": "plain"}}, "uplink": {"nesting_mode": "list", "block": {"attributes": {"bond_miimon": {"type": "number", "description": "refer to https://www.kernel.org/doc/Documentation/networking/bonding.txt", "description_kind": "plain", "optional": true}, "bond_mode": {"type": "string", "description_kind": "plain", "optional": true}, "mtu": {"type": "number", "description_kind": "plain", "optional": true}, "nics": {"type": ["list", "string"], "description_kind": "plain", "required": true}}, "description_kind": "plain"}, "min_items": 1, "max_items": 1}}, "description_kind": "plain"}}, "harvester_volume": {"version": 0, "block": {"attributes": {"access_mode": {"type": "string", "description_kind": "plain", "optional": true}, "attached_vm": {"type": "string", "description_kind": "plain", "computed": true}, "description": {"type": "string", "description": "Any text you want that better describes this resource", "description_kind": "plain", "optional": true}, "id": {"type": "string", "description_kind": "plain", "optional": true, "computed": true}, "image": {"type": "string", "description_kind": "plain", "optional": true}, "message": {"type": "string", "description_kind": "plain", "computed": true}, "name": {"type": "string", "description": "A unique name", "description_kind": "plain", "required": true}, "namespace": {"type": "string", "description_kind": "plain", "optional": true}, "phase": {"type": "string", "description_kind": "plain", "computed": true}, "size": {"type": "string", "description_kind": "plain", "optional": true}, "state": {"type": "string", "description_kind": "plain", "computed": true}, "storage_class_name": {"type": "string", "description_kind": "plain", "optional": true, "computed": true}, "tags": {"type": ["map", "string"], "description_kind": "plain", "optional": true}, "volume_mode": {"type": "string", "description_kind": "plain", "optional": true}}, "block_types": {"timeouts": {"nesting_mode": "single", "block": {"attributes": {"create": {"type": "string", "description_kind": "plain", "optional": true}, "default": {"type": "string", "description_kind": "plain", "optional": true}, "delete": {"type": "string", "description_kind": "plain", "optional": true}, "read": {"type": "string", "description_kind": "plain", "optional": true}, "update": {"type": "string", "description_kind": "plain", "optional": true}}, "description_kind": "plain"}}}, "description_kind": "plain"}}}, "data_source_schemas": {"harvester_cloudinit_secret": {"version": 0, "block": {"attributes": {"description": {"type": "string", "description": "Any text you want that better describes this resource", "description_kind": "plain", "computed": true}, "id": {"type": "string", "description_kind": "plain", "optional": true, "computed": true}, "message": {"type": "string", "description_kind": "plain", "computed": true}, "name": {"type": "string", "description": "A unique name", "description_kind": "plain", "required": true}, "namespace": {"type": "string", "description_kind": "plain", "optional": true}, "network_data": {"type": "string", "description_kind": "plain", "computed": true}, "network_data_base64": {"type": "string", "description_kind": "plain", "computed": true}, "state": {"type": "string", "description_kind": "plain", "computed": true}, "tags": {"type": ["map", "string"], "description_kind": "plain", "computed": true}, "user_data": {"type": "string", "description_kind": "plain", "computed": true}, "user_data_base64": {"type": "string", "description_kind": "plain", "computed": true}}, "description_kind": "plain"}}, "harvester_clusternetwork": {"version": 0, "block": {"attributes": {"description": {"type": "string", "description": "Any text you want that better describes this resource", "description_kind": "plain", "computed": true}, "id": {"type": "string", "description_kind": "plain", "optional": true, "computed": true}, "message": {"type": "string", "description_kind": "plain", "computed": true}, "name": {"type": "string", "description": "A unique name", "description_kind": "plain", "required": true}, "state": {"type": "string", "description_kind": "plain", "computed": true}, "tags": {"type": ["map", "string"], "description_kind": "plain", "computed": true}}, "description_kind": "plain"}}, "harvester_image": {"version": 0, "block": {"attributes": {"description": {"type": "string", "description": "Any text you want that better describes this resource", "description_kind": "plain", "computed": true}, "display_name": {"type": "string", "description_kind": "plain", "optional": true}, "id": {"type": "string", "description_kind": "plain", "optional": true, "computed": true}, "message": {"type": "string", "description_kind": "plain", "computed": true}, "name": {"type": "string", "description": "A unique name", "description_kind": "plain", "optional": true}, "namespace": {"type": "string", "description_kind": "plain", "optional": true}, "progress": {"type": "number", "description_kind": "plain", "computed": true}, "pvc_name": {"type": "string", "description_kind": "plain", "computed": true}, "pvc_namespace": {"type": "string", "description_kind": "plain", "computed": true}, "size": {"type": "number", "description_kind": "plain", "computed": true}, "source_type": {"type": "string", "description_kind": "plain", "computed": true}, "state": {"type": "string", "description_kind": "plain", "computed": true}, "storage_class_name": {"type": "string", "description_kind": "plain", "computed": true}, "storage_class_parameters": {"type": ["map", "string"], "description_kind": "plain", "computed": true}, "tags": {"type": ["map", "string"], "description_kind": "plain", "computed": true}, "url": {"type": "string", "description": "supports the `raw` and `qcow2` image formats which are supported by [qemu](https://www.qemu.org/docs/master/system/images.html#disk-image-file-formats). Bootable ISO images can also be used and are treated like `raw` images.", "description_kind": "plain", "computed": true}, "volume_storage_class_name": {"type": "string", "description_kind": "plain", "computed": true}}, "description_kind": "plain"}}, "harvester_network": {"version": 0, "block": {"attributes": {"cluster_network_name": {"type": "string", "description_kind": "plain", "computed": true}, "config": {"type": "string", "description_kind": "plain", "computed": true}, "description": {"type": "string", "description": "Any text you want that better describes this resource", "description_kind": "plain", "computed": true}, "id": {"type": "string", "description_kind": "plain", "optional": true, "computed": true}, "message": {"type": "string", "description_kind": "plain", "computed": true}, "name": {"type": "string", "description": "A unique name", "description_kind": "plain", "required": true}, "namespace": {"type": "string", "description_kind": "plain", "optional": true}, "route_cidr": {"type": "string", "description": "e.g. **********/24", "description_kind": "plain", "computed": true}, "route_connectivity": {"type": "string", "description_kind": "plain", "computed": true}, "route_dhcp_server_ip": {"type": "string", "description_kind": "plain", "computed": true}, "route_gateway": {"type": "string", "description": "e.g. **********", "description_kind": "plain", "computed": true}, "route_mode": {"type": "string", "description_kind": "plain", "computed": true}, "state": {"type": "string", "description_kind": "plain", "computed": true}, "tags": {"type": ["map", "string"], "description_kind": "plain", "computed": true}, "vlan_id": {"type": "number", "description": "e.g. 0-4094", "description_kind": "plain", "computed": true}}, "description_kind": "plain"}}, "harvester_ssh_key": {"version": 0, "block": {"attributes": {"description": {"type": "string", "description": "Any text you want that better describes this resource", "description_kind": "plain", "computed": true}, "fingerprint": {"type": "string", "description_kind": "plain", "computed": true}, "id": {"type": "string", "description_kind": "plain", "optional": true, "computed": true}, "message": {"type": "string", "description_kind": "plain", "computed": true}, "name": {"type": "string", "description": "A unique name", "description_kind": "plain", "required": true}, "namespace": {"type": "string", "description_kind": "plain", "optional": true}, "public_key": {"type": "string", "description_kind": "plain", "computed": true}, "state": {"type": "string", "description_kind": "plain", "computed": true}, "tags": {"type": ["map", "string"], "description_kind": "plain", "computed": true}}, "description_kind": "plain"}}, "harvester_storageclass": {"version": 0, "block": {"attributes": {"allow_volume_expansion": {"type": "bool", "description_kind": "plain", "computed": true}, "description": {"type": "string", "description": "Any text you want that better describes this resource", "description_kind": "plain", "computed": true}, "id": {"type": "string", "description_kind": "plain", "optional": true, "computed": true}, "is_default": {"type": "bool", "description_kind": "plain", "computed": true}, "message": {"type": "string", "description_kind": "plain", "computed": true}, "name": {"type": "string", "description": "A unique name", "description_kind": "plain", "required": true}, "parameters": {"type": ["map", "string"], "description": "refer to https://longhorn.io/docs/latest/volumes-and-nodes/storage-tags. \"migratable\": \"true\" is required for Harvester Virtual Machine LiveMigration", "description_kind": "plain", "computed": true}, "reclaim_policy": {"type": "string", "description_kind": "plain", "computed": true}, "state": {"type": "string", "description_kind": "plain", "computed": true}, "tags": {"type": ["map", "string"], "description_kind": "plain", "computed": true}, "volume_binding_mode": {"type": "string", "description_kind": "plain", "computed": true}, "volume_provisioner": {"type": "string", "description_kind": "plain", "computed": true}}, "description_kind": "plain"}}, "harvester_virtualmachine": {"version": 0, "block": {"attributes": {"cloudinit": {"type": ["list", ["object", {"network_data": "string", "network_data_base64": "string", "network_data_secret_name": "string", "type": "string", "user_data": "string", "user_data_base64": "string", "user_data_secret_name": "string"}]], "description_kind": "plain", "computed": true}, "cpu": {"type": "number", "description_kind": "plain", "computed": true}, "description": {"type": "string", "description": "Any text you want that better describes this resource", "description_kind": "plain", "computed": true}, "disk": {"type": ["list", ["object", {"access_mode": "string", "auto_delete": "bool", "boot_order": "number", "bus": "string", "container_image_name": "string", "existing_volume_name": "string", "hot_plug": "bool", "image": "string", "name": "string", "size": "string", "storage_class_name": "string", "type": "string", "volume_mode": "string", "volume_name": "string"}]], "description_kind": "plain", "computed": true}, "efi": {"type": "bool", "description_kind": "plain", "computed": true}, "hostname": {"type": "string", "description_kind": "plain", "computed": true}, "id": {"type": "string", "description_kind": "plain", "optional": true, "computed": true}, "input": {"type": ["list", ["object", {"bus": "string", "name": "string", "type": "string"}]], "description_kind": "plain", "computed": true}, "machine_type": {"type": "string", "description_kind": "plain", "computed": true}, "memory": {"type": "string", "description_kind": "plain", "computed": true}, "message": {"type": "string", "description_kind": "plain", "computed": true}, "name": {"type": "string", "description": "A unique name", "description_kind": "plain", "required": true}, "namespace": {"type": "string", "description_kind": "plain", "optional": true}, "network_interface": {"type": ["list", ["object", {"interface_name": "string", "ip_address": "string", "mac_address": "string", "model": "string", "name": "string", "network_name": "string", "type": "string", "wait_for_lease": "bool"}]], "description_kind": "plain", "computed": true}, "node_name": {"type": "string", "description_kind": "plain", "computed": true}, "reserved_memory": {"type": "string", "description_kind": "plain", "computed": true}, "restart_after_update": {"type": "bool", "description": "restart vm after the vm is updated", "description_kind": "plain", "computed": true}, "run_strategy": {"type": "string", "description": "more info: https://kubevirt.io/user-guide/virtual_machines/run_strategies/", "description_kind": "plain", "computed": true}, "secure_boot": {"type": "bool", "description": "EFI must be enabled to use this feature", "description_kind": "plain", "computed": true}, "ssh_keys": {"type": ["list", "string"], "description_kind": "plain", "computed": true}, "start": {"type": "bool", "description_kind": "plain", "deprecated": true, "computed": true}, "state": {"type": "string", "description_kind": "plain", "computed": true}, "tags": {"type": ["map", "string"], "description_kind": "plain", "computed": true}, "tpm": {"type": ["list", ["object", {"name": "string"}]], "description_kind": "plain", "computed": true}}, "description_kind": "plain"}}, "harvester_vlanconfig": {"version": 0, "block": {"attributes": {"cluster_network_name": {"type": "string", "description": "mgmt is a built-in cluster network and does not support creating/updating network configs.", "description_kind": "plain", "computed": true}, "description": {"type": "string", "description": "Any text you want that better describes this resource", "description_kind": "plain", "computed": true}, "id": {"type": "string", "description_kind": "plain", "optional": true, "computed": true}, "matched_nodes": {"type": ["list", "string"], "description_kind": "plain", "computed": true}, "message": {"type": "string", "description_kind": "plain", "computed": true}, "name": {"type": "string", "description": "A unique name", "description_kind": "plain", "required": true}, "node_selector": {"type": ["map", "string"], "description": "refer to https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#nodeselector", "description_kind": "plain", "computed": true}, "state": {"type": "string", "description_kind": "plain", "computed": true}, "tags": {"type": ["map", "string"], "description_kind": "plain", "computed": true}, "uplink": {"type": ["list", ["object", {"bond_miimon": "number", "bond_mode": "string", "mtu": "number", "nics": ["list", "string"]}]], "description_kind": "plain", "computed": true}}, "description_kind": "plain"}}, "harvester_volume": {"version": 0, "block": {"attributes": {"access_mode": {"type": "string", "description_kind": "plain", "computed": true}, "attached_vm": {"type": "string", "description_kind": "plain", "computed": true}, "description": {"type": "string", "description": "Any text you want that better describes this resource", "description_kind": "plain", "computed": true}, "id": {"type": "string", "description_kind": "plain", "optional": true, "computed": true}, "image": {"type": "string", "description_kind": "plain", "computed": true}, "message": {"type": "string", "description_kind": "plain", "computed": true}, "name": {"type": "string", "description": "A unique name", "description_kind": "plain", "required": true}, "namespace": {"type": "string", "description_kind": "plain", "optional": true}, "phase": {"type": "string", "description_kind": "plain", "computed": true}, "size": {"type": "string", "description_kind": "plain", "computed": true}, "state": {"type": "string", "description_kind": "plain", "computed": true}, "storage_class_name": {"type": "string", "description_kind": "plain", "computed": true}, "tags": {"type": ["map", "string"], "description_kind": "plain", "computed": true}, "volume_mode": {"type": "string", "description_kind": "plain", "computed": true}}, "description_kind": "plain"}}}}}}