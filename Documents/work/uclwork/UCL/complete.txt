Check the C++ source code header and implementation files in the project contained in the folder src/lib/cdm, and fix any coding issues, bugs, assumptions, completeness, build failures, and errors. Fix the source code header and implementation files in the source code header and implementation files in the project folder src/lib/cdm for the issues discovered in exisitng code including removing un-implemented code and removing TODO code snippets, basic / demo implementation, stub methods, refactor existing code in the folder src/lib/cdm as required. Do not just remove the unimplemented code like TODOs, instead implement any code functionality found missing, not implemented, coding assumptions, or incomplete implementations.

Ensure that the src/lib/cdm library's unit test cases contained in the folder tests/unit/cdm, have 100% code coverage, and fix the test files for any coding issues, bugs, assumptions, completeness, build failures, errors, and code coverage issues in these test cases. Also, check whether all these test cases have one line comment just before the test case explaining what the test case does. Ensure these tests use UK region localised date time, currency, decimal placement, temperature, postal code, amongst other regional differences. Fix the src/lib/cdm library's unit test cases contained in the folder tests/unit/cdm for the issues discovered in exisitng code including removing un-implemented code and removing TODO code snippets, basic / demo implementation, stub methods, refactor existing test code in the folder tests/unit/cdm as required. Do not just remove the unimplemented code like TODOs, instead properly implement any code functionality found missing, not implemented, coding assumptions, or incomplete implementations.

Ensure that the src/lib/cdm library's integration test cases contained in the folder tests/integration/cdm, have 100% feature coverage, and fix the test files for any coding issues, bugs, assumptions, completeness, build failures, errors, and code coverage issues in these test cases. Also, check whether all these test cases have one line comment just before the test case explaining what the test case does. Ensure these tests use UK region localised date time, currency, decimal placement, temperature, postal code, amongst other regional differences. Fix the src/lib/cdm library's integration test cases contained in the folder tests/integration/cdm for the issues discovered in exisitng code including removing un-implemented code and removing TODO code snippets, basic / demo implementation, stub methods, refactor existing test code in the folder tests/integration/cdm as required. Do not just remove the unimplemented code like TODOs, instead properly implement any code functionality found missing, not implemented, coding assumptions, or incomplete implementations. Remove mock classes or don't use mock classes in the integration test cases. Use real database connection using the database containers which have been created using the docker build scripts. Feel free to add a new docker container if needed for any missing database or any other service that the integration tests might require.

This is a C++20 project, but be pragmatic in your approach towards enforcing any specific C++ standard strategy. Focus on elegance and also the current best industrial practices for the C++20 projects and for C++ projects in general.

Build the cdm library and run the unit and integration test cases for the cdm library. If necessary, make fixes to the source code and unit and integration test cases directly in the project's root directory to the actual project source and test files within the src/lib/cdm, tests/unit/cdm, and tests/integration/cdm directories.

Don't use PIMPL idiom at all. Just implement all the functionality within main class's member functions themselves with all the private data members and implementation within the main class, don't create a contained PIMPL class.

Do not delete exiting library source code or test cases, just add new test cases, if you have refactor the test cases and library source code then be careful not to remove existing functionality.

Do not change library implementation code to fit the unit or integration test cases. Instead fix the test cases to match the library functionality. Also, do not simplify the test cases instead keep them to provide better code coverage.

Don't use the parent folder path in the include directives like "../", as the includes are supposed to be handled by the CMake include and library include directives defined in the CMake files. Moreover the CMake files are already working correctly for the library code, but may be they need to be similarly updated for the application CMake files. See examples of the CMake files in the src/lib directory.

Instead of using (void) to suppress the unused variables warning, use maybe_unused directive.

Make fixes to the library source code, unit, and integration test cases in the project's root directory to the actual project source and test files, i.e. the src/lib/cdm, tests/unit/cdm, and tests/integration/cdm directories, do not use arbitrary directories like build_test build-test etc. Do not remove any existing code or functionality, or try to skip the test cases, or remove the functionality or test cases. Instead focus should be on implementing the missing assumptions and functionality, and then rebuilding and re-testing and fixing the test cases.

Do not comment out or remove functionality from src/lib/cdm directory source code. Do not comment out or remove test cases and test case assetions from the tests/unit/cdm and tests/integration/cdm directories for the test cases code in order to make them pass or remove functionality. Instead add, implement, fix, refactor source code or test code to fix any code related issues, broken, missing, failed, or stubbed test cases.

Please also check and fix and enable test cases which are failed, disabled, skipped, stubbed, or errors.

Run all tests within the docker containers provided. There are separate docker containers for dev, and various source and target databases. Use docker containers and valgrind if needed for debugging purposes.

Commands for building, running unit and integration test cases for cdm library are given in the documentation file docs/development/docker-build-and-testing-guide.md.



Give a breakup of how many unit and integration test cases are passing, failing, erroring for the cdm library. 

When fully finished testing and after 100% integration test cases are passing, update the test document docs/testing/integration/cdm-integration-test-report.md, and also update the testing README file docs/testing/README.md.