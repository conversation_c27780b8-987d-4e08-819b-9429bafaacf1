# Initial Research

This work is for expanding upon the broad points discussed on 19th July with Prof. <PERSON>.

Patient oriented computer application and systems development to assist in medical research.

## All possible routes
Computers applications can assist cancer research in the fields of medical imaging and diagnostics, drug discovery, automated treatment search and delivery, computer assisted clinical trials, patient advisory service, and predictive epidemiology.

Medical imaging and diagnostics involves can help with the detection of cancer forming cells, substances, cancerous cells, tumors, and cysts. Computer assisted radiology can make use of the application of artificial intelligence techniques in studying the radiological data, looking for patterns which could be useful in detecting cancerous cell growth and other cancer like conditions.

Computer based 'artificial intelligence' (AI) can help in medical research by performing expertanalysis of patient data, using the 'machine learning' (ML) techniques upon the available medical data by generating various models of inference like heuristic, statistical, data driven, or deep learning. The machine learning is a branch of artifical intelligence in which the computer 'learns' to evolve data processing algorithms based on the statistical data being processed. Machine learning algorithms involves the use of mathematical and statitical techniques for information discovery.

Deep learning is a branch within the machine learning which operates on very large data sets to 'train' or improve the algorithms and potentially 'learn' or 'discover' new 'features' or pieces of information from the processing of the data. An artificially intelligent system operates on certain premises or assumptions which again are based on previously available information from the field of study. One of the ML techniques is 'deep learning' where algorithmic analysis is performed upon large amounts of data sets and information models are built, which might lead to new 'information discovery' or extraction of useful 'features' which might have been previously unknown about the subject matter.

Transfer learning is a method used within deep learning where the models of information built previously on a large data set are then applied to another related data sets. Transfer learning is also inductive learning in which knowledge common to two potentially different domains of study could be used to cross train from one domain to another, and then used to discover more about the other related or completely different domain of study.

The machine learning benefits from the advances in availability of artifically intelligent computer applications, application libraries, and application platforms from all fields of artificial intelligence and applications of machine learning techniques.

AI applications could be developed to work as automated 'agents' acting between a patient and a medical consultant to improve the availability, scalability, and accuracy of treatment outcomes. The way AI could help in this matter would be by prototyping patient and consultant models, which would then communicate using a 'semantic gateway' to communicate in real time.

The term semantic gateway would be a space where data exists in machine readable form, so that AI agent applications could automatically catalogue the bits of information received or produced by the operating processes within the agent application. These 'AI agents' would be representing the real human beings who would be interacting with each other through these AI agents, with humans and AI agents both providing or consuming data at it's various stages of processing. We could call the AI agents representing human beings as 'AI avatar', 'avatar application', or 'avatar'.

Initial proposed list of the applications of artificial intelligence to the field of healthcare is as given below:

- Online Avatar for individuals and organisations
- Avatar based representation of users of services and providers of those services
- Patient Avatar based Medical Records
- Crowdsourcing Patient Avatar Medical Records (see Apple’s ResearchKit)
- Machine Learning in Radiology
- Machine Learning in Cancer Treatment
- Drug Discovery
- Treatment Suggestions for Personalised Treatment
- Treatment Recommendations and Clinical Trials
- Patient Behavioural Advisory
- Predictive Epidemiology

## Why to choose a method
Amongst the people of various demographic backgrounds the application of data processing by AI automated agents combined with the availability of previous data can iteratively improve the outcome of treatment. Iterative improvement is not just enough as it affects real people and the availability of data from previous proven treatment methods from across the globe can be potentially applied to improve treatment methods, data, models, and algorithms.

Transfer learning approach can be applied from majority demographic group for which extensive patient data is available, upon minority demographic groups for which the patient data available might be a small percentage of the entire set of patient dataset available.

## What is the criteria to choose a method
Whilst it is possible to develop sophisticated machine leaning algorithms which operate on sparsely available datasets, it is always desirable to have more datapoint as higher number of data points drive automated machine learning techniques such as deep learning and are used as learning material for better developing such machine learning algorithms.

## What are the matrices of success
There are several criterion to evaluate the approaches illustrated above for success in terms of feasibility, viability, and commercial success of each of those approaches.

The AI agent or avatar should be able to represent a user online as a representative and offline while interacting with the user. Online version of the avatar should be able to interact with other online medical specialists or researchers which could also be operating through their respective AI agents. These online researcher avatars could potentially provide expert guidance on possible treatments based on the medical data shared by the patient.

The AI agent should be a representation of real world human interactions conducted in real time based on patient and vendor information. The value of an end user provided data source is imperative in the age of data economy where the data drives all levels of interaction between people, organisations, businesses, and governments. The availability of people provided patient data will not only drive the medical research to new levels of delivering accurate and timely treatment to people of all demographic backgrounds, but it will also contribute to the fields of artificial intelligence and its important branch machine learning.

AI agent should be able to 'classify' or discover interesting 'artefacts' from the bits of information processed, this will help in 'making sense' of the data being processed or pre-processed for another stage of operation. An AI agent should also be able to apply quantification algorithms on the data being processed, as this will lead to faster extraction of key figures or 'statistics' from the the data being pprocessed when compared with the classifier processing time overheads.

AI agents should produce data which is 'reliable' and matrices which are of 'certain quality', as the data reliability would keep user base engaged in using the AI agent based avatar services. This qualitative approach to data processing would increase the possibility of a wider adoption of the AI agent based avatar service amongst its users.

## Cost and time criteria coming from commercial background
The cost of setting up a communications channel for AI agent based system suitable for gathering data globally from the people in a location agnostic fashion is quite reasonable, considering the availability of cloud technologies such as Akamai for providing secure cloud communication solutions for data distribution amongst all parties concerned.

The cost and time savings in setting up compute solutions and resources is minimal these days with cloud compute vendors such as Google/AWS/Azure all of whom deliver their cloud based compute resources in a software defined fashion called Platform As A Service (PaaS), all of which are relatively easy to setup and capable of delivering extremely scalable and feature rich solutions thus making it easy to deliver production grade software services.

Academic, and scientific that is more balanced approach
The academic research is extremely important part of digging out the depth of possibilities and potential solutions based the current body of knowledge available world wide in the academia and institutions of knowledge.

The commercial exposure brings the completion of this academic pursuit in that it becomes easier to design, develop, and deploy the requisite software artefacts successfully and thus providing a scope to run several potentially powerful and useful thoughts and ideas coming from the basic academic in the medical industry in general, medical physics and compute scientific research and engineering in particular.

## References
Chapter 1. What is epidemiology?, thebmj: https://www.bmj.com/about-bmj/resources-readers/publications/epidemiology-uninitiated/1-what-epidemiology
http://www.helsinki.fi/esslli/courses/readers/K10.pdf
https://www.datasciencecentral.com/profiles/blogs/automated-deep-learning-so-simple-anyone-can-do-it
https://www.kofax.com/Go/2019/kofax-intelligent-automation-platform
https://www.datarobot.com/lp/see-automl-will-transform-business/
https://pages.matillion.com/machine-learning-ebook?utm_source=google&utm_medium=cpc&utm_campaign=User_Guide_Machine_Learning_EMEA&utm_term=ebook_machine%2520learning&ckmscn=***********&gclid=Cj0KCQjwho7rBRDxARIsAJ5nhFpvh7i4a2IO_Q6zZnG1anH9FkhgIc7WAUVH2SUFqOVZLeb_l2WPZrgaAjmgEALw_wcB
https://scholar.google.co.uk/scholar?q=automated+machine+learning+techniques&hl=en&as_sdt=0&as_vis=1&oi=scholart
https://en.m.wikipedia.org/wiki/Automated_machine_learning
https://skymind.ai/wiki/automl-automated-machine-learning-ai
https://www.ml4aad.org/automl/
https://docs.microsoft.com/en-us/azure/machine-learning/service/concept-automated-ml
https://www.kdnuggets.com/2019/01/automated-machine-learning-python.html
https://medium.com/thinkgradient/automated-machine-learning-an-overview-5a3595d5c4b5
https://ml.informatik.uni-freiburg.de/papers/15-NIPS-auto-sklearn-preprint.pdf
https://subscription.packtpub.com/video/big_data_and_business_intelligence/9781789800289?uuid=97511409-3f40-4719-b05a-19f61b9e88a3
https://subscription.packtpub.com/video/big_data_and_business_intelligence/9781789530087?uuid=97511409-3f40-4719-b05a-19f61b9e88a3
https://en.wikipedia.org/wiki/Artificial_intelligence_in_healthcare

## Commercial applications
https://www.nvidia.com/en-us/industries/healthcare-life-sciences/
https://orionhealth.com/uk/knowledge-hub/reports/machine-learning-in-healthcare/
https://peltarion.com/platform?gclid=CjwKCAjwzdLrBRBiEiwAEHrAYlGSxul3MG-pr5cjefB2RhdyzXwb071uowocwVPaqNtAdqL0XN_IpBoCfakQAvD_BwE
https://c3.ai/c3-ai-suite/?mkwid=smj1EfYxT_dc|pcrid|************|pmt|b|pkw|%2Bmachine%20%2Blearning|slid||targetids|kwd-***********|groupid|***********|&pgrid=***********&ptaid=kwd-***********&gclid=CjwKCAjwzdLrBRBiEiwAEHrAYg7UmGih1BeGIxRLpP7j0sPpPYTC-_6iBD7XhcGWJ8wycP76gMwJDRoCA-cQAvD_BwE
https://www.genpact.com/industries/healthcare?&gclid=CjwKCAjwzdLrBRBiEiwAEHrAYg6pTrfV8VRUORZPQ-fdipZgi1DBqPI0LHojRQnlTLb-4EMjAhgwehoCvPAQAvD_BwE

## Sources of data
Cancer research UK statistics, with loads of metrics and statistics for patient outcome https://www.cancerresearchuk.org/health-professional/cancer-statistics-for-the-uk
