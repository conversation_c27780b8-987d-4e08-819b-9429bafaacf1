{"enableCsrfToken": true, "aliasTokenTimeout": "2 days", "uiDisplaySeriesDescription": true, "defaultToSortedListings": true, "uiShowLeftBar": true, "emailVerification": false, "requireChangeJustification": false, "maxFailedLoginsLockoutDuration": "1 hour", "uiShowPrearchiveFileActions": true, "security.prevent-data-deletion": "false", "maxNumberOfSessionsForJobsWithSharedData": 100000, "passwordHistoryDuration": "1 year", "reloadPrearcDatabaseOnStartup": false, "siteWideAlertType": "message", "allowNonAdminsToClaimUnassignedSessions": true, "uiShowLeftBarProjects": true, "requireImageAssessorLabels": false, "uiAllowPetTracerConfiguration": true, "uiPrearchiveHideArchiveBtn": false, "preventCrossModalityMerge": true, "sessionTimeoutMessage": "Session timed out at TIMEOUT_TIME.", "adminEmail": "<EMAIL>", "dataPaths": ["/xapi/**", "/data/**", "/REST/**", "/fs/**"], "uiAllowBlockedSubjectAssessorView": false, "separateSecondaryDicomOnArchive": true, "requireSaltedPasswords": false, "uiDisplayScanModality": false, "databaseUpdateRequiredWarning": "", "failMergeOn": [], "htmlResourceRenderingWhitelist": ["bmp", "gif", "jpeg", "jpg", "png", "tiff", "txt", "xml"], "requireEventName": false, "siteWideAlertMessage": "", "ohif.viewer.default": "true", "uiAllowProjectDelete": true, "addCountFieldsToProjectListings": true, "enableSitewideSeriesImportFilter": false, "securityMaxLoginInterval": 1, "concurrentMaxSessions": 1000, "uiDisplaySeriesClass": false, "siteDescriptionType": "Text", "zipCompressionMethod": 0, "siteDescriptionText": "Welcome to **XNAT**: UCL Cancer Data Engineering XNAT Server.", "cachePath": "/opt/xnat/cache", "uiShowLeftBarBrowse": true, "securityChannel": "any", "ipsThatCanSendEmailsThroughRest": "^.*$", "checksums": true, "docker:host": "\"unix:///var/run/docker.sock\"", "upload..temp_dir": "/usr/share/tomcat/temp", "zipExtensions": "zip,jar,rar,ear,gar,mrb", "mainPageSearchDatatypeOptions": ["xnat:mrSessionData", "xnat:petSessionData", "xnat:ctSessionData"], "passwordExpirationType": "Interval", "siteLogoPath": "/images/logo.png", "sitewideSeriesImportFilter": "", "allowHtmlResourceRendering": false, "passwordExpirationDate": "", "pull_images_on_xnat_startup": "true", "userRegistration": false, "uiAllowScanAddition": true, "siteName": "UCDE-XNAT", "uiHideXnatUploadAssistantDownload": true, "siteLoginLanding": "/screens/QuickSearch.vm", "uiShowRecentExptListScannerName": false, "sitewideAnonymizationScript": "//\n// Default XNAT anonymization script\n// XNAT http://www.xnat.org\n// Copyright (c) 2005-2017, Washington University School of Medicine and Howard Hughes Medical Institute\n// All Rights Reserved\n//\n// Released under the Simplified BSD.\n//\nversion \"6.1\"\nproject != \"Unassigned\" ? (0008,1030) := project\n(0010,0010) := subject\n(0010,0020) := session", "uiAllowScanTypeModification": true, "securityLastModifiedInterval": 1, "uiShowScanTypeMapping": true, "uiExptAllowLabelChange": true, "roleRepositoryService": "org.nrg.xdat.security.services.impl.RoleRepositoryServiceImpl", "initialized": true, "ftpPath": "/opt/xnat/ftp", "primaryAdminUsername": "xnat_svc", "restrictUserListAccessToAdmins": false, "siteDescriptionPage": "/screens/site_description.vm", "uiShowAdminManageSiteFeatures": true, "dicomFileNameTemplate": "${StudyInstanceUID}-${SeriesNumber}-${InstanceNumber}-${HashSOPClassUIDWithSOPInstanceUID}", "sessionXmlRebuilderRepeat": 60000, "uiDebugExtensionPoints": false, "imageSessionDisplayNamePlural": "Sessions", "displayedUserIdentifierType": "USERNAME", "aliasTokenTimeoutSchedule": "0 0 * * * *", "uiDateTimeSecondsFormat": "dd-MMM-yyyy HH:mm:ss.SSS", "interactiveAgentIds": [".*MSIE.*", ".*<PERSON><PERSON>.*", ".*AppleWebKit.*", ".*Opera.*"], "maintainFileHistory": false, "triagePath": "/opt/xnat/cache/TRIAGE", "passwordComplexityMessage": "Password must contain 8+ characters with uppercase, lowercase, number, and symbol.", "uiShowLeftBarFavorites": true, "emailVerificationMessage": "Dear FULL_NAME,\n<br><br>We received a request to register an account for you on SITE_NAME. If you would like to register, please confirm your email address by clicking this link: <a href=\"VERIFICATION_URL\">Verify Email</a>\n (This link will expire in 24 hours.)AUTO_ENABLE_TEXT<br><br>If you did not initiate this request, you can safely ignore this email.", "emailProjectAccessRequestToAdmin": true, "securityExternalUserParDisabled": false, "sessionArchiveTimeoutInterval": 3600, "pathErrorWarning": "The following system path errors have been discovered:<br>\t1. Archive path \"/opt/xnat/archive\" does not exist.<br>\t2. Cache path \"/opt/xnat/cache\" does not exist.<br>\t3. Build path \"/opt/xnat/build\" does not exist.<br>\t4. Prearchive path \"/opt/xnat/prearchive\" does not exist.", "csrfEmailAlert": false, "showChangeJustification": false, "uiTimeFormat": "HH:mm:ss", "defaultProjectAutoArchiveSetting": 4, "uiShowProjectManageFiles": true, "featureService": "org.nrg.xdat.security.services.impl.FeatureServiceImpl", "uiAllowMoreProjectInvestigators": true, "useSopInstanceUidToUniquelyIdentifyDicom": true, "siteUrl": "https://xnat-web.medp-proj-cde.condenser.arc.ucl.ac.uk", "passwordExpirationInterval": "1 year", "uiLoginFailureMessage": "Your login attempt failed because the username and password combination you provided was invalid or your user already has the maximum number of user sessions open. After %d failed login attempts, your user account will be locked. If you believe your account is currently locked, you can:<ul><li>Unlock it by resetting your password</li><li>Wait one hour for it to unlock automatically</li></ul>", "sitewidePetTracers": "PIB\nFDG", "imageSessionDisplayNameSingular": "Session", "scanTypeMapping": true, "uiAllowNonAdminProjectCreation": true, "receivedFileUser": "xnat_svc", "defaultToPagedRestfulLists": false, "maxFailedLogins": 20, "enableSitewideAnonymizationScript": true, "pipelinePath": "/opt/xnat/pipeline", "uiAllowSubjectCreateFromExptEdit": true, "uiShowRecentExptListDate": false, "par": true, "processingUrl": "", "inactivityBeforeLockout": "1 year", "requireLogin": true, "fileOperationUsedForJobsWithSharedData": "hard_link", "uiAllowQuarantine": true, "docker:enabled": "true", "uiShowLeftBarSearch": true, "canResetFailedLoginsWithForgotPassword": true, "siteDescription": "Welcome to **XNAT**: UCL Cancer Data Engineering XNAT Server.", "upload..maximum_size": "***********", "uiDateTimeFormat": "dd-MMM-yyyy HH:mm", "uiExptAllowSubjectChange": true, "sessionXmlRebuilderInterval": 5, "inactivityBeforeLockoutSchedule": "0 0 1 * * ?", "securityAllowNonPrivateProjects": true, "sessionTimeout": "3 hours", "backupDeletedToCache": false, "roleService": "org.nrg.xdat.security.services.impl.RoleServiceImpl", "enableDicomReceiver": true, "enableDicomReceiverPropertyChangedListener": "org.nrg.dcm.DicomSCPSiteConfigurationListener", "prearchivePath": "/opt/xnat/prearchive", "passwordComplexity": "^(?=.*\\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[\\W]).{8,}$", "archivePath": "/opt/xnat/archive", "buildPath": "/opt/xnat/build", "docker": {"enabled": true, "host": "unix:///var/run/docker.sock"}, "enabledProviders": ["localdb"], "allowDataAdmins": true, "uiShowManageFiles": true, "passwordReuseRestriction": "None", "matchSecurityProtocol": false, "siteWideAlertStatus": 0, "uiExptAllowProjectChange": true, "displayHostName": "multinode", "securityNewUserRegistrationDisabled": false, "securityLocalDbParRegistrationDisabled": false, "uiDateFormat": "dd-M<PERSON>-yyyy", "inboxPath": "/opt/xnat/inbox", "projectAllowAutoArchive": true, "resetFailedLoginsSchedule": "0 0 * * * *", "uiHideCompressedUploaderUploadOption": false, "uiAllowAdvancedSearch": true, "featureRepositoryService": "org.nrg.xdat.security.services.impl.FeatureRepositoryServiceImpl", "siteHomeLayout": "/Index.vm", "siteLandingLayout": "/Index.vm", "uiDefaultCompressedUploaderImporter": "DICOM-zip", "uiHideDesktopClientDownload": false, "sitewidePetMr": "", "sitewideSeriesImportFilterMode": "blacklist", "rerunProjectAnonOnRename": false, "siteId": "UCDE-XNAT", "removeScanAggregateFields": false, "uiAllowNewUserComments": true, "siteHome": "/app/template/Index.vm"}