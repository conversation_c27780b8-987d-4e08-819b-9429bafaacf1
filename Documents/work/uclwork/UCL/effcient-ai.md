- Efficient pipeline
- Missing data
- Minimal use of resources

Explainable AI
Its not random data for cancer research
Genomics - genome is genetic information and is unique and yet similar for a species
Lean a lot about cancer from genomic information 
Link between geneomics and pathology
People with certain genetic information have similar body cells and cell behaviour
Chemical affect the growth of mutations
Relation between genetics and cancerous cells
Mutations are not limitless
Pathologist annd biologist for sarcoma
Computer scientist working on Robust AI (Rohan - <EMAIL>)
Cancer generates due to biiology

Contact Rose for PhD programme re-instatement - <EMAIL>





Research Focus

There is a pressing need to leverage our current understanding of AI engines by applying established engineering principles, with a focus on enhancing efficiency in AI research. My goal is to develop AI systems that can operate locally on edge devices or scale massively through the use of the intelligent agents connected to the cloud infrastructure as needed. These systems should serve as both general purpose AI, capable of interpreting user queries in agentic and chat modes, as well in autonomous mode using multi-modal expert systems. By prioritizing energy efficiency, this work could enable widespread applications in medical physics and long term healthcare, addressing critical global needs.

A multi-modal expert system is an artificial intelligence (AI) system designed to process, interpret, and integrate information from multiple types of input data known as modalities.

Proposed Research and Impact

I’m excited about the potential of this research to advance scientific knowledge and accelerate progress in medicine through efficient, localised, and scalable AI systems. My proposed approach involves creating a hybrid AI ecosystem by integrating edge computing AI with general LLM based engines. This could, for instance, improve medical imaging analysis, delivering faster and more accurate results even in resource limited settings.

Hybrid Design and Rationale

This hybrid design stands in contrast to traditional cloud based LLM API pipelines. By combining edge based neuromorphic modules (e.g., for voice preprocessing or sensor fusion) with cloud scale transformers based LLM inference, it optimises both efficiency and performance. This approach could be likened to choosing a hierarchical model (coarse-to-fine or latent modular) over a flat, large-model framework.

Neuromorphic and brain-inspired models excel in energy and latency sensitive tasks, while transformers shine in complex reasoning, my pipeline harnesses the strengths of both.

Conclusion

I am eager to contribute to this field, bringing my extensive industry experience and passion for efficient AI to address real world challenges in healthcare and beyond.







Efficient AI Pipeline for Conversational Assistants

There is a pressing need to leverage our current understanding of AI engines by applying established engineering principles, with a focus on enhancing efficiency in AI research. My goal is to develop AI systems that can operate locally on edge devices or scale massively via cloud infrastructure as needed. These systems should serve as both general purpose AI—capable of interpreting user queries in agentic and chat modes—and autonomous, multi-modal expert systems. By prioritizing energy efficiency, this work could enable widespread applications in medical physics and long term healthcare, addressing critical global needs.

System-Level Architecture

The system is organized as a modular AI engine: user interfaces feed into a core inference pipeline and back-end memory subsystems. Each subsystem – data ingestion, LLM inference, memory storage, and neuromorphic acceleration – is a distinct module. Neuromorphic co-processors mimic spiking neurons to perform specialized inference and memory tasks; by placing computation close to synaptic memory, they achieve lower power and faster inference. The pipeline orchestrates data flow across these modules: user input is tokenized and passed to the inference engine (LLM), which may call out to external tools (search, databases) or retrieve context from memory. A feedback loop captures outputs, logs and telemetry back into long-term storage for future use. This architectural layering given below clearly demarcates compute nodes and signal flows, enabling scalability and efficient resource usage.

•	Modules: Data sources → LLM inference engine → Output layer
•	Co-Processors: Neuromorphic accelerators handle event-driven tasks (e.g. spiking computations)	.
•	Memory: Separate short-term (conversational context) and long-term (vector DB / knowledge base) stores.
•	Interfaces: External APIs and tools integrated via an agentic layer (see below).

Component Breakdown

The core components include LLM Inference, Memory Stores, Retrieval (RAG) Layer, and Neuromorphic Accelerators. For example, neuromorphic processors implement spiking neuron models (Hodgkin– Huxley, Izhikevich, etc.) as shown above; these integrate computation with memory directly, enabling ultra-low-power, low-latency processing. In parallel, a traditional LLM inference engine (often on GPU/FPGA/CPU) handles natural language reasoning. Each component is modular: the pipeline can route queries to the LLM, to a knowledge retrieval module, or offload tasks to specialized hardware. This separation lets designers optimize each block independently (e.g. tune memory layout or swap in a higher-capacity LLM).

•	Short-term Memory: Context buffer of recent conversation turns, managed by the dialogue system (e.g. sliding window, summarization).
•	Long-term Memory (RAG): A vector database or knowledge graph index. On each query, the
Retrieval-Augmented Generation (RAG) module fetches relevant facts to augment the LLM prompt
.
•	Inference: A large language model (LLM) or transformer, possibly partitioned or distilled for efficiency.
•	Neuromorphic Co-Processors: Dedicated chips (e.g. spiking cores, memristive arrays) for tasks like embedding search or real-time signal processing. These cores treat weights as in-memory synapses for energy efficiency	.
 
The RAG module is illustrated below: it shows how internal data stores and knowledge bases feed into the LLM at query time. By grounding generation on retrieved facts, RAG dramatically improves factual accuracy and reduces hallucinations  . In practice, this component updates an “augmented prompt” that blends user input with recent logs or documents, then passes it to the LLM.

Deployment Models

The pipeline supports multiple deployment configurations. A desktop/edge mode runs the LLM and retrieval locally (on CPU/GPU or specialized ASIC) for privacy-sensitive use cases. In cloud/VM clusters, the components run in distributed containers: large LLM inference may be scaled across GPU nodes, and memory stores reside in a managed database. For hybrid FPGA/ASIC or neuromorphic deployment, critical loops (e.g. embedding, indexing, or small SNN workloads) execute on embedded hardware. For example, a factory chatbot might run on an FPGA cluster with on-device neuromorphic chips for sensor data encoding. This flexibility allows trading off latency, throughput, and power: neuromorphic hardware provides efficiency on edge devices, while cloud clusters offer massive scale for peak loads  .

•	On-Premises/Desktop: All modules co-located on a local machine (CPU/GPU or edge AI hardware). Good for privacy or low-latency.
•	Cloud/VM Cluster: LLMs and services in containers or VMs (e.g. Kubernetes). Scale-out with load balancing for high traffic.
•	FPGA/ASIC/Neuromorphic Hybrid: Custom silicon or programmable logic accelerators for inference/RAG. This includes neuromorphic chips (spiking cores) working alongside FPGAs or ASICs to offload neural workloads	.

Memory Flow + Feedback Loop

The assistant maintains two memory channels to improve over time. Short-term memory is the in- context transcript (recent user exchanges), which is continuously fed to the LLM for coherent dialogue. Long-term memory consists of logged data: user profiles, conversation histories, and application state, typically stored in a vector database or knowledge base. New information (user corrections, outcomes of actions) is logged and indexed, then consumed on future queries. Crucially, every step forms a feedback loop: model outputs and user feedback are captured as episodic memory, which can be queried or used to fine-tune future responses  . Telemetry and logs (e.g. system events, error rates) are also collected to monitor performance.

•	Memory Update: After each response, relevant facts (e.g. user preferences, confirmed knowledge) are appended to long-term storage. Over time this creates a personalized knowledge base.
•	Retrieval Cycle: On each new query, the retrieval module searches both memory types (e.g. RAG querying the vector DB) to bring context into the prompt.
•	Feedback Loop: The system periodically retrains or fine-tunes modules on logged interactions. Human-in-the-loop adjustments can be applied to correct model errors. In practice, this means “self-consistency” checks and memory consolidation are used to refine reasoning	.
This memory-centric pipeline ensures that the assistant “learns” from past interactions. For example, if the assistant repeatedly fails on a certain question, that error (along with a corrected answer) is stored and can be reintroduced to the LLM as a memory, improving future accuracy.
 
Agent & Reasoning Flow

The LLM is used agentically: it reasons step-by-step and invokes tools via APIs. For each user query, the system may execute a chain-of-thought procedure (ReAct style) where the LLM alternates between inference and actions . For example, the LLM might generate an intermediate “thought” that includes a function call (e.g. an API to fetch a database record), then the result is fed back into the prompt for further reasoning. In this way the model’s output guides external computations, then consumes the results to continue the dialogue.

•	Tool Integration: The LLM is trained or instructed to recognize special tokens that represent function calls. It can call any exposed API (search, database query, math engine) mid-dialog, then incorporate the response	.
•	Reasoning Loop: Each API call is treated as an action; its returned value is appended to the conversation context, allowing the LLM to “think” further. This ReAct loop (alternating
) makes reasoning traceable and robust	.
•	Memory Use: When reasoning requires external knowledge, the agent consults its long-term memory (e.g. RAG) and includes retrieved snippets in the prompt. Short-term memory of recent reasoning steps is always maintained in the context	.
•	Adaptivity: Over successive turns, the agent updates its internal state. For example, it might note a user’s preferred phrasing or a particular context cue, storing it as semantic memory. This allows more informed actions in later steps, effectively blending memory and reasoning	.
In summary, the conversational agent runs a loop of “prompt → reason → act → observe”. By combining LLM reasoning with tool use and memory recall, the architecture supports complex, multi- step tasks while providing transparency (each action and its result can be inspected). This modular, agentic design also improves reliability: errors (like hallucinations) can be caught via tool verification or looped back into memory for correction	.

Sources: Modern neuromorphic and LLM architectures	were reviewed to inform this pipeline design.


Neuromorphic Computing Architecture | Download Scientific Diagram
https://www.researchgate.net/figure/Neuromorphic-Computing-Architecture_fig3_349370733

LLM Agents and Agentic Design Patterns | Towards AI
https://towardsai.net/p/machine-learning/llm-agents-and-agentic-design-patterns-5
