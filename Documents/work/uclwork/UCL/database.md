/**
 * @file DatabaseConnector.hpp
 * @brief Database connector class hierarchy for OMOP ETL
 */

#pragma once
#include <string>
#include <memory>
#include <vector>
#include <stdexcept>
#include <libpq-fe.h>
#include <sql.h>
#include <sqlext.h>

namespace omop::extract::connectors {

/**
 * @brief Custom exception class for database operations
 */
class DatabaseException : public std::runtime_error {
public:
    explicit DatabaseException(const std::string& message) 
        : std::runtime_error(message) {}
};

/**
 * @brief Structure to hold connection parameters
 */
struct ConnectionParams {
    std::string host;
    std::string port;
    std::string database;
    std::string username;
    std::string password;
    std::string additionalParams;
};

/**
 * @brief Structure to hold query results
 */
struct QueryResult {
    std::vector<std::string> columnNames;
    std::vector<std::vector<std::string>> rows;
    size_t affectedRows;
    bool success;
    std::string errorMessage;
};

/**
 * @brief Abstract base class for database connections
 */
class IDBConnector {
public:
    virtual ~IDBConnector() = default;
    
    /**
     * @brief Connect to database
     * @param params Connection parameters
     * @throw DatabaseException if connection fails
     */
    virtual void connect(const ConnectionParams& params) = 0;
    
    /**
     * @brief Disconnect from database
     */
    virtual void disconnect() = 0;
    
    /**
     * @brief Execute a query that returns results
     * @param query SQL query string
     * @return QueryResult containing results or error information
     */
    virtual QueryResult executeQuery(const std::string& query) = 0;
    
    /**
     * @brief Execute a query that modifies data
     * @param query SQL query string
     * @return QueryResult containing affected rows or error information
     */
    virtual QueryResult executeUpdate(const std::string& query) = 0;
    
    /**
     * @brief Begin a transaction
     */
    virtual void beginTransaction() = 0;
    
    /**
     * @brief Commit a transaction
     */
    virtual void commitTransaction() = 0;
    
    /**
     * @brief Rollback a transaction
     */
    virtual void rollbackTransaction() = 0;
    
    /**
     * @brief Check if connection is active
     * @return true if connected, false otherwise
     */
    virtual bool isConnected() const = 0;
};

/**
 * @brief PostgreSQL database connector implementation
 */
class PostgresConnector : public IDBConnector {
public:
    PostgresConnector() : conn_(nullptr) {}
    ~PostgresConnector() override {
        disconnect();
    }
    
    void connect(const ConnectionParams& params) override {
        std::string connStr = 
            "host=" + params.host + 
            " port=" + params.port +
            " dbname=" + params.database +
            " user=" + params.username +
            " password=" + params.password;
            
        conn_ = PQconnectdb(connStr.c_str());
        
        if (PQstatus(conn_) != CONNECTION_OK) {
            std::string error = PQerrorMessage(conn_);
            disconnect();
            throw DatabaseException("PostgreSQL connection failed: " + error);
        }
    }
    
    void disconnect() override {
        if (conn_) {
            PQfinish(conn_);
            conn_ = nullptr;
        }
    }
    
    QueryResult executeQuery(const std::string& query) override {
        QueryResult result;
        
        PGresult* res = PQexec(conn_, query.c_str());
        if (PQresultStatus(res) != PGRES_TUPLES_OK) {
            result.success = false;
            result.errorMessage = PQerrorMessage(conn_);
            PQclear(res);
            return result;
        }
        
        // Get column names
        int cols = PQnfields(res);
        for (int i = 0; i < cols; i++) {
            result.columnNames.push_back(PQfname(res, i));
        }
        
        // Get rows
        int rows = PQntuples(res);
        for (int i = 0; i < rows; i++) {
            std::vector<std::string> row;
            for (int j = 0; j < cols; j++) {
                row.push_back(PQgetvalue(res, i, j));
            }
            result.rows.push_back(row);
        }
        
        result.success = true;
        result.affectedRows = rows;
        
        PQclear(res);
        return result;
    }
    
    QueryResult executeUpdate(const std::string& query) override {
        QueryResult result;
        
        PGresult* res = PQexec(conn_, query.c_str());
        if (PQresultStatus(res) != PGRES_COMMAND_OK) {
            result.success = false;
            result.errorMessage = PQerrorMessage(conn_);
            PQclear(res);
            return result;
        }
        
        result.success = true;
        result.affectedRows = std::stoi(PQcmdTuples(res));
        
        PQclear(res);
        return result;
    }
    
    void beginTransaction() override {
        auto result = executeUpdate("BEGIN");
        if (!result.success) {
            throw DatabaseException("Failed to begin transaction: " + result.errorMessage);
        }
    }
    
    void commitTransaction() override {
        auto result = executeUpdate("COMMIT");
        if (!result.success) {
            throw DatabaseException("Failed to commit transaction: " + result.errorMessage);
        }
    }
    
    void rollbackTransaction() override {
        auto result = executeUpdate("ROLLBACK");
        if (!result.success) {
            throw DatabaseException("Failed to rollback transaction: " + result.errorMessage);
        }
    }
    
    bool isConnected() const override {
        return conn_ && PQstatus(conn_) == CONNECTION_OK;
    }
    
private:
    PGconn* conn_;
};

/**
 * @brief ODBC database connector implementation
 */
class ODBCConnector : public IDBConnector {
public:
    ODBCConnector() : env_(nullptr), dbc_(nullptr) {
        SQLAllocHandle(SQL_HANDLE_ENV, SQL_NULL_HANDLE, &env_);
        SQLSetEnvAttr(env_, SQL_ATTR_ODBC_VERSION, (void*)SQL_OV_ODBC3, 0);
        SQLAllocHandle(SQL_HANDLE_DBC, env_, &dbc_);
    }
    
    ~ODBCConnector() override {
        disconnect();
        if (dbc_) SQLFreeHandle(SQL_HANDLE_DBC, dbc_);
        if (env_) SQLFreeHandle(SQL_HANDLE_ENV, env_);
    }
    
    void connect(const ConnectionParams& params) override {
        std::string connStr = 
            "DRIVER={" + params.additionalParams + "};" +
            "SERVER=" + params.host + ";" +
            "DATABASE=" + params.database + ";" +
            "UID=" + params.username + ";" +
            "PWD=" + params.password + ";";
            
        SQLCHAR outStr[1024];
        SQLSMALLINT outStrLen;
        
        SQLRETURN ret = SQLDriverConnect(
            dbc_, 
            NULL, 
            (SQLCHAR*)connStr.c_str(),
            SQL_NTS,
            outStr,
            sizeof(outStr),
            &outStrLen,
            SQL_DRIVER_NOPROMPT
        );
        
        if (!SQL_SUCCEEDED(ret)) {
            throw DatabaseException("ODBC connection failed: " + getODBCError(SQL_HANDLE_DBC, dbc_));
        }
    }
    
    void disconnect() override {
        if (dbc_) SQLDisconnect(dbc_);
    }
    
    QueryResult executeQuery(const std::string& query) override {
        QueryResult result;
        SQLHSTMT stmt;
        
        if (!SQL_SUCCEEDED(SQLAllocHandle(SQL_HANDLE_STMT, dbc_, &stmt))) {
            result.success = false;
            result.errorMessage = "Failed to allocate statement handle";
            return result;
        }
        
        SQLRETURN ret = SQLExecDirect(stmt, (SQLCHAR*)query.c_str(), SQL_NTS);
        if (!SQL_SUCCEEDED(ret)) {
            result.success = false;
            result.errorMessage = getODBCError(SQL_HANDLE_STMT, stmt);
            SQLFreeHandle(SQL_HANDLE_STMT, stmt);
            return result;
        }
        
        // Get column info
        SQLSMALLINT cols;
        SQLNumResultCols(stmt, &cols);
        
        for (SQLSMALLINT i = 1; i <= cols; i++) {
            SQLCHAR colName[256];
            SQLSMALLINT colNameLen;
            SQLColAttribute(stmt, i, SQL_DESC_NAME, colName, sizeof(colName), &colNameLen, NULL);
            result.columnNames.push_back((char*)colName);
        }
        
        // Fetch rows
        while (SQL_SUCCEEDED(SQLFetch(stmt))) {
            std::vector<std::string> row;
            for (SQLSMALLINT i = 1; i <= cols; i++) {
                SQLCHAR value[1024];
                SQLLEN len;
                SQLGetData(stmt, i, SQL_C_CHAR, value, sizeof(value), &len);
                row.push_back(len == SQL_NULL_DATA ? "NULL" : (char*)value);
            }
            result.rows.push_back(row);
        }
        
        result.success = true;
        result.affectedRows = result.rows.size();
        
        SQLFreeHandle(SQL_HANDLE_STMT, stmt);
        return result;
    }
    
    QueryResult executeUpdate(const std::string& query) override {
        QueryResult result;
        SQLHSTMT stmt;
        
        if (!SQL_SUCCEEDED(SQLAllocHandle(SQL_HANDLE_STMT, dbc_, &stmt))) {
            result.success = false;
            result.errorMessage = "Failed to allocate statement handle";
            return result;
        }
        
        SQLRETURN ret = SQLExecDirect(stmt, (SQLCHAR*)query.c_str(), SQL_NTS);
        if (!SQL_SUCCEEDED(ret)) {
            result.success = false;
            result.errorMessage = getODBCError(SQL_HANDLE_STMT, stmt);
            SQLFreeHandle(SQL_HANDLE_STMT, stmt);
            return result;
        }
        
        SQLLEN rowCount;
        SQLRowCount(stmt, &rowCount);
        
        result.success = true;
        result.affectedRows = rowCount;
        
        SQLFreeHandle(SQL_HANDLE_STMT, stmt);
        return result;
    }
    
    void beginTransaction() override {
        auto result = executeUpdate("BEGIN TRANSACTION");
        if (!result.success) {
            throw DatabaseException("Failed to begin transaction: " + result.errorMessage);
        }
    }
    
    void commitTransaction() override {
        auto result = executeUpdate("COMMIT");
        if (!result.success) {
            throw DatabaseException("Failed to commit transaction: " + result.errorMessage);
        }
    }
    
    void rollbackTransaction() override {
        auto result = executeUpdate("ROLLBACK");
        if (!result.success) {
            throw DatabaseException("Failed to rollback transaction: " + result.errorMessage);
        }
    }
    
    bool isConnected() const override {
        SQLINTEGER connected;
        SQLGetConnectAttr(dbc_, SQL_ATTR_CONNECTION_DEAD, &connected, 0, NULL);
        return connected == SQL_CD_FALSE;
    }
    
private:
    SQLHENV env_;
    SQLHDBC dbc_;
    
    std::string getODBCError(SQLSMALLINT handleType, SQLHANDLE handle) {
        SQLCHAR sqlState[6];
        SQLCHAR message[SQL_MAX_MESSAGE_LENGTH];
        SQLINTEGER nativeError;
        SQLSMALLINT messageLen;
        
        SQLGetDiagRec(
            handleType,
            handle,
            1,
            sqlState,
            &nativeError,
            message,
            sizeof(message),
            &messageLen
        );
        
        return std::string((char*)sqlState) + ": " + std::string((char*)message);
    }
};

} // namespace omop::extract::connectors

Unit test cases.

/**
 * @file DatabaseConnector.cpp
 * @brief Unit tests for database connectors
 */

#include <gtest/gtest.h>
#include "database_connector.hpp"

using namespace omop::extract::connectors;

class PostgresConnectorTest : public ::testing::Test {
protected:
    std::unique_ptr<PostgresConnector> connector;
    ConnectionParams testParams;
    
    void SetUp() override {
        connector = std::make_unique<PostgresConnector>();
        testParams.host = "localhost";
        testParams.port = "5432";
        testParams.database = "test_db";
        testParams.username = "test_user";
        testParams.password = "test_pass";
    }
    
    void TearDown() override {
        if (connector->isConnected()) {
            connector->disconnect();
        }
    }
};

TEST_F(PostgresConnectorTest, ConnectionSuccess) {
    ASSERT_NO_THROW(connector->connect(testParams));
    ASSERT_TRUE(connector->isConnected());
}

TEST_F(PostgresConnectorTest, ConnectionFailure) {
    testParams.password = "wrong_password";
    ASSERT_THROW(connector->connect(testParams), DatabaseException);
    ASSERT_FALSE(connector->isConnected());
}

TEST_F(PostgresConnectorTest, QueryExecution) {
    connector->connect(testParams);
    
    auto result = connector->executeQuery("SELECT 1 as test");
    ASSERT_TRUE(result.success);
    ASSERT_EQ(result.columnNames.size(), 1);
    ASSERT_EQ(result.columnNames[0], "test");
    ASSERT_EQ(result.rows.size(), 1);
    ASSERT_EQ(result.rows[0][0], "1");
}

TEST_F(PostgresConnectorTest, UpdateExecution) {
    connector->connect(testParams);
    
    // Create test table
    auto createResult = connector->executeUpdate(
        "CREATE TEMPORARY TABLE test_table (id INT, value TEXT)"
    );
    ASSERT_TRUE(createResult.success);
    
    // Insert data
    auto insertResult = connector->executeUpdate(
        "INSERT INTO test_table VALUES (1, 'test')"
    );
    ASSERT_TRUE(insertResult.success);
    ASSERT_EQ(insertResult.affectedRows, 1);
}

TEST_F(PostgresConnectorTest, TransactionHandling) {
    connector->connect(testParams);
    
    // Start transaction
    ASSERT_NO_THROW(connector->beginTransaction());
    
    // Create test table
    auto createResult = connector->executeUpdate(
        "CREATE TEMPORARY TABLE test_table (id INT, value TEXT)"
    );
    ASSERT_TRUE(createResult.success);
    
    // Insert data
    auto insertResult = connector->executeUpdate(
        "INSERT INTO test_table VALUES (1, 'test')"
    );
    ASSERT_TRUE(insertResult.success);
    
    // Verify data exists
    auto queryResult = connector->executeQuery(
        "SELECT * FROM test_table"
    );
    ASSERT_TRUE(queryResult.success);
    ASSERT_EQ(queryResult.rows.size(), 1);
    
    // Rollback transaction
    ASSERT_NO_THROW(connector->rollbackTransaction());
    
    // Verify data doesn't exist
    auto verifyResult = connector->executeQuery(
        "SELECT * FROM test_table"
    );
    ASSERT_TRUE(verifyResult.success);
    ASSERT_EQ(verifyResult.rows.size(), 0);
}

TEST_F(PostgresConnectorTest, ConcurrentTransactions) {
    connector->connect(testParams);
    
    // First transaction
    connector->beginTransaction();
    auto result1 = connector->executeUpdate(
        "CREATE TEMPORARY TABLE test_table (id INT PRIMARY KEY, value TEXT)"
    );
    ASSERT_TRUE(result1.success);
    
    // Create second connection
    auto connector2 = std::make_unique<PostgresConnector>();
    connector2->connect(testParams);
    connector2->beginTransaction();
    
    // Insert in first transaction
    auto insertResult = connector->executeUpdate(
        "INSERT INTO test_table VALUES (1, 'test')"
    );
    ASSERT_TRUE(insertResult.success);
    
    // Try to insert same record in second transaction - should block
    auto futureResult = std::async(std::launch::async, [&connector2]() {
        return connector2->executeUpdate(
            "INSERT INTO test_table VALUES (1, 'test2')"
        );
    });
    
    // First transaction commits
    ASSERT_NO_THROW(connector->commitTransaction());
    
    // Second transaction should fail with constraint violation
    auto result2 = futureResult.get();
    ASSERT_FALSE(result2.success);
    ASSERT_NO_THROW(connector2->rollbackTransaction());
}

class ODBCConnectorTest : public ::testing::Test {
protected:
    std::unique_ptr<ODBCConnector> connector;
    ConnectionParams testParams;
    
    void SetUp() override {
        connector = std::make_unique<ODBCConnector>();
        testParams.host = "localhost";
        testParams.database = "test_db";
        testParams.username = "test_user";
        testParams.password = "test_pass";
        testParams.additionalParams = "SQL Server"; // Or "Microsoft Access Driver (*.mdb, *.accdb)"
    }
    
    void TearDown() override {
        if (connector->isConnected()) {
            connector->disconnect();
        }
    }
};

TEST_F(ODBCConnectorTest, ConnectionSuccess) {
    ASSERT_NO_THROW(connector->connect(testParams));
    ASSERT_TRUE(connector->isConnected());
}

TEST_F(ODBCConnectorTest, ConnectionFailure) {
    testParams.password = "wrong_password";
    ASSERT_THROW(connector->connect(testParams), DatabaseException);
    ASSERT_FALSE(connector->isConnected());
}

TEST_F(ODBCConnectorTest, QueryExecution) {
    connector->connect(testParams);
    
    auto result = connector->executeQuery("SELECT 1 as test");
    ASSERT_TRUE(result.success);
    ASSERT_EQ(result.columnNames.size(), 1);
    ASSERT_EQ(result.columnNames[0], "test");
    ASSERT_EQ(result.rows.size(), 1);
    ASSERT_EQ(result.rows[0][0], "1");
}

TEST_F(ODBCConnectorTest, NullHandling) {
    connector->connect(testParams);
    
    // Create test table with NULL values
    auto createResult = connector->executeUpdate(
        "CREATE TEMPORARY TABLE null_test (id INT, nullable_value TEXT NULL)"
    );
    ASSERT_TRUE(createResult.success);
    
    // Insert NULL value
    auto insertResult = connector->executeUpdate(
        "INSERT INTO null_test VALUES (1, NULL)"
    );
    ASSERT_TRUE(insertResult.success);
    
    // Query NULL value
    auto queryResult = connector->executeQuery(
        "SELECT * FROM null_test"
    );
    ASSERT_TRUE(queryResult.success);
    ASSERT_EQ(queryResult.rows.size(), 1);
    ASSERT_EQ(queryResult.rows[0][1], "NULL");
}

TEST_F(ODBCConnectorTest, LargeResultSet) {
    connector->connect(testParams);
    
    // Create test table
    auto createResult = connector->executeUpdate(
        "CREATE TEMPORARY TABLE large_test (id INT PRIMARY KEY)"
    );
    ASSERT_TRUE(createResult.success);
    
    // Insert many rows
    for (int i = 0; i < 1000; i++) {
        auto insertResult = connector->executeUpdate(
            "INSERT INTO large_test VALUES (" + std::to_string(i) + ")"
        );
        ASSERT_TRUE(insertResult.success);
    }
    
    // Query all rows
    auto queryResult = connector->executeQuery(
        "SELECT * FROM large_test ORDER BY id"
    );
    ASSERT_TRUE(queryResult.success);
    ASSERT_EQ(queryResult.rows.size(), 1000);
    
    // Verify first and last rows
    ASSERT_EQ(queryResult.rows[0][0], "0");
    ASSERT_EQ(queryResult.rows[999][0], "999");
}

TEST_F(ODBCConnectorTest, ErrorHandling) {
    connector->connect(testParams);
    
    // Invalid SQL syntax
    auto result1 = connector->executeQuery("INVALID SQL");
    ASSERT_FALSE(result1.success);
    ASSERT_FALSE(result1.errorMessage.empty());
    
    // Invalid table name
    auto result2 = connector->executeQuery("SELECT * FROM nonexistent_table");
    ASSERT_FALSE(result2.success);
    ASSERT_FALSE(result2.errorMessage.empty());
    
    // Invalid column name
    auto result3 = connector->executeQuery("SELECT nonexistent_column FROM large_test");
    ASSERT_FALSE(result3.success);
    ASSERT_FALSE(result3.errorMessage.empty());
}

// Main function for running the tests
int main(int argc, char **argv) {
    testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
