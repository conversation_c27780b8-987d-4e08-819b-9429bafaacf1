This project is an implementation of OMOP CDM pipeline using CMake and standard C++ project to create the ETL pipeline application. Please produce a robust standard C++ based complete implementation, complete with C++ base and implementation classes, producing a layered project structure containing multiple CMake files in subfolders containing various project sub-modules, which are listed further below. The project should be platform neutral and use latest C++ standard specification. The project should also contains src directory for source. Use Doxygen comments for all the class member functions and data members.

This C++20 project provides a flexible, high-performance framework for healthcare data transformation with support for multiple data sources and use configuration files in YAML format, for mapping data sources to OMOP OHDSI compliant CDM database tables.

In effect the mapping configuration YAML files is used to perform the transformation from the source data (which could be in CSV format or a custom database format), to the target OMOP CDM database tables.

The directory structure of the proposed OMOP ETL Pipeline is as given below.

```
omop-etl/
├── CMakeLists.txt                  # Root CMake configuration
├── CMakePresets.json               # CMake build presets
├── CMakeWorkspaceSettings.json     # CMake workspace settings
├── README.md                       # Project documentation
├── LICENSE                         # Project license
├── Dockerfile                      # Production Docker image
├── Dockerfile.dev                  # Development Docker image
├── Dockerfile.dev.arm64           # ARM64 development Docker image
├── .dockerignore                   # Docker ignore rules
├── .clang-tidy                     # Clang-tidy configuration
├── .gitignore                      # Git ignore rules
├── github_workflow.yml            # GitHub Actions workflow
├── sample_config.yml              # Sample configuration file
├── settings.json                  # Project settings
├── dump.txt                       # Development dump file
├── core_pipeline_test.txt         # Core pipeline test output
├── core_validation_result_test.txt # Validation test output
├── core_processing_context_test.txt # Processing context test output
├── additional_core_tests.txt      # Additional core tests output
├── memory_management.patch        # Memory management improvements
├── type_conversion.patch          # Type conversion improvements
├── thread_safety.patch            # Thread safety improvements
├── config/                        # Configuration files
│   ├── etl/                      # ETL mapping configurations
│   │   ├── csv_mappings.yaml
│   │   ├── json_mappings.yaml
│   │   ├── microservices_config.yaml
│   │   ├── mysql_mappings.yaml
│   │   └── postgres_mappings.yaml
│   └── api/                      # API configuration
│       └── config.yaml
├── cmake/                         # CMake modules and configuration
│   ├── deploy-external-package.cmake
│   └── omop-config.cmake.in
├── src/                          # Source code root
│   ├── CMakeLists.txt           # Src directory CMake
│   ├── app/                     # Application code
│   │   ├── CMakeLists.txt
│   │   ├── api/                # Web API service
│   │   │   ├── CMakeLists.txt
│   │   │   ├── api_service.h
│   │   │   ├── api_service.cpp
│   │   │   ├── etl_service.cpp
│   │   │   └── microservice_main.cpp
│   │   └── cli/                # Command line interface
│   │       ├── CMakeLists.txt
│   │       ├── cli_application.h
│   │       └── cli_application.cpp
│   └── lib/                     # Library code
│       ├── CMakeLists.txt
│       ├── cdm/                # OHDSI CDM data handling
│       │   ├── CMakeLists.txt
│       │   ├── omop_tables.h
│       │   ├── omop_tables.cpp
│       │   ├── table_definitions.h
│       │   ├── table_definitions.cpp
│       │   └── sql/            # SQL schema definitions
│       │       ├── CMakeLists.txt
│       │       ├── create_constraints.sql.in
│       │       ├── create_indexes.sql.in
│       │       ├── create_location.sql.in
│       │       ├── create_provider_care_site.sql.in
│       │       ├── create_schemas.sql.in
│       │       ├── create_tables.sql.in
│       │       ├── process_sql.cmake
│       │       ├── process_sql.py
│       │       ├── process_sql.sh
│       │       └── schema_config.cmake
│       ├── common/             # Common components
│       │   ├── CMakeLists.txt
│       │   ├── config.h.in     # Configuration template
│       │   ├── configuration.h
│       │   ├── configuration.cpp
│       │   ├── exceptions.h
│       │   ├── exceptions.cpp
│       │   ├── logging.h
│       │   ├── logging.cpp
│       │   ├── utilities.h
│       │   ├── utilities.cpp
│       │   ├── validation.h
│       │   └── validation.cpp
│       ├── core/              # Core pipeline components
│       │   ├── CMakeLists.txt
│       │   ├── component_factory.cpp
│       │   ├── interfaces.h
│       │   ├── interfaces.cpp
│       │   ├── interfaces.h.orig
│       │   ├── job_manager.h
│       │   ├── job_manager.cpp
│       │   ├── job_scheduler.h
│       │   ├── job_scheduler.cpp
│       │   ├── pipeline.h
│       │   ├── pipeline.cpp
│       │   ├── record.h
│       │   └── record.cpp
│       ├── extract/           # Data extraction components
│       │   ├── CMakeLists.txt
│       │   ├── extractor_base.h
│       │   ├── extractor_base.cpp
│       │   ├── extractor_factory.h
│       │   ├── extractor_factory.cpp
│       │   ├── csv_extractor.h
│       │   ├── csv_extractor.cpp
│       │   ├── compressed_csv_extractor.cpp
│       │   ├── json_extractor.h
│       │   ├── json_extractor.cpp
│       │   ├── database_connector.h
│       │   ├── database_connector.cpp
│       │   ├── connection_pool.cpp
│       │   ├── postgresql_connector.h
│       │   ├── postgresql_connector.cpp
│       │   ├── mysql_connector.h
│       │   ├── mysql_connector.cpp
│       │   ├── odbc_connector.h
│       │   ├── odbc_connector.cpp
│       │   ├── extract_utils.cpp
│       │   ├── extract.h
│       │   └── platform/            # Platform-specific utilities
│       │       ├── CMakeLists.txt
│       │       ├── windows_utils.cpp    # Windows-specific utilities
│       │       ├── windows_utils.h      # Windows utilities header
│       │       ├── unix_utils.cpp       # Unix/Linux-specific utilities
│       │       └── unix_utils.h         # Unix utilities header
│       ├── transform/         # Data transformation logic
│       │   ├── CMakeLists.txt
│       │   ├── transformation_engine.h
│       │   ├── transformation_engine.cpp
│       │   ├── vocabulary_service.h
│       │   ├── vocabulary_service.cpp
│       │   ├── field_transformations.cpp
│       │   ├── field_transformations.h
│       │   ├── transformations.h
│       │   ├── date_transformations.cpp      # Date/time transformations
│       │   ├── date_transformations.h
│       │   ├── numeric_transformations.cpp   # Numeric data transformations
│       │   ├── numeric_transformations.h
│       │   ├── string_transformations.cpp    # String manipulation transformations
│       │   ├── string_transformations.h
│       │   ├── conditional_transformations.cpp # Conditional logic transformations
│       │   ├── conditional_transformations.h
│       │   ├── custom_transformations.cpp    # User-defined transformations
│       │   ├── custom_transformations.h
│       │   ├── vocabulary_transformations.cpp # Vocabulary mapping transformations
│       │   ├── vocabulary_transformations.h
│       │   ├── validation_engine.cpp         # Transformation validation engine
│       │   ├── validation_engine.h
│       │   ├── transformation_registry_improvements.h
│       │   └── transformation_result.h
│       ├── load/             # Data loading components
│       │   ├── CMakeLists.txt
│       │   ├── database_loader.h
│       │   ├── database_loader.cpp
│       │   ├── loader_base.h            # Base loader interface
│       │   ├── loader_base.cpp          # Base loader implementation
│       │   ├── batch_loader.h           # Batch loading functionality
│       │   ├── batch_loader.cpp         # Batch loader implementation
│       │   ├── additional_loaders.h     # Additional loader implementations
│       │   ├── additional_loaders.cpp   # Additional loader implementations
│       │   ├── load_cmakelists.txt      # Load module CMake configuration
│       │   └── load_module_readme.md    # Load module documentation
│       └── service/          # Service layer functionality
│           ├── CMakeLists.txt
│           ├── etl_service.h
│           ├── etl_service.cpp
│           └── service.cpp
├── tests/                    # Unit and integration tests
│   ├── CMakeLists.txt
│   ├── unit/                # Unit tests
│   │   ├── CMakeLists.txt
│   │   ├── api/            # API unit tests
│   │   │   └── CMakeLists.txt
│   │   ├── cdm/            # CDM unit tests
│   │   │   ├── CMakeLists.txt
│   │   │   ├── omop_tables_test.cpp
│   │   │   └── table_definitions_test.cpp
│   │   ├── common/         # Common unit tests
│   │   │   ├── CMakeLists.txt
│   │   │   ├── configuration_test.cpp
│   │   │   ├── exceptions_test.cpp
│   │   │   ├── logging_test.cpp
│   │   │   ├── utilities_test.cpp
│   │   │   └── validation_test.cpp
│   │   ├── core/           # Core unit tests
│   │   │   ├── CMakeLists.txt
│   │   │   ├── interfaces_test.cpp
│   │   │   ├── job_manager_test.cpp
│   │   │   ├── job_scheduler_test.cpp
│   │   │   ├── pipeline_test.cpp
│   │   │   └── record_test.cpp
│   │   ├── extract/        # Extract unit tests
│   │   │   ├── CMakeLists.txt
│   │   │   ├── compressed_csv_test.cpp
│   │   │   ├── connection_pool_test.cpp
│   │   │   ├── csv_extractor_test.cpp
│   │   │   ├── database_connector_test.cpp
│   │   │   ├── extract_utils_test.cpp
│   │   │   ├── extract_utils_extended_test.cpp
│   │   │   ├── extractor_base_test.cpp
│   │   │   ├── extractor_factory_test.cpp
│   │   │   ├── json_extractor_test.cpp
│   │   │   ├── mysql_connector_test.cpp
│   │   │   ├── odbc_connector_test.cpp
│   │   │   ├── platform_utils_test.cpp
│   │   │   ├── postgresql_connector_test.cpp
│   │   │   └── extract_performance_test.cpp
│   │   ├── load/           # Load unit tests
│   │   │   ├── CMakeLists.txt
│   │   │   ├── additional_loaders_test.cpp
│   │   │   ├── batch_loader_test.cpp
│   │   │   ├── database_loader_test.cpp
│   │   │   └── load_utils_test.cpp
│   │   └── transform/      # Transform unit tests
│   │       ├── CMakeLists.txt
│   │       ├── conditional_transformations_test.cpp
│   │       ├── custom_transformations_test.cpp
│   │       ├── date_transformations_test.cpp
│   │       ├── field_transformation_helpers_test.cpp
│   │       ├── field_transformation_test.cpp
│   │       ├── numeric_transformations_test.cpp
│   │       ├── string_transformations_test.cpp
│   │       ├── transform_integration_test.cpp
│   │       ├── transform_utils_test.cpp
│   │       ├── transformation_engine_test.cpp
│   │       ├── transformation_registry_test.cpp
│   │       ├── transformation_edge_cases_test.cpp
│   │       ├── transformation_memory_management_test.cpp
│   │       ├── transformation_pipeline_comprehensive_test.cpp
│   │       ├── validation_engine_test.cpp
│   │       ├── vocabulary_service_test.cpp
│   │       ├── vocabulary_transformations_test.cpp
│   │       └── test_helpers.h
│   └── integration/        # Integration tests
│       ├── CMakeLists.txt
│       ├── integration_test_structure.txt
│       ├── example_usage.cpp
│       ├── api/            # API integration tests
│       │   ├── CMakeLists.txt
│       │   ├── test_api_integration.cpp
│       │   ├── test_grpc_api_integration.cpp
│       │   └── test_rest_api_integration.cpp
│       ├── cdm/            # CDM integration tests
│       │   ├── CMakeLists.txt
│       │   ├── condition_occurrence/
│       │   │   └── tes_condition_occurrence-integration.cpp
│       │   ├── death/
│       │   │   └── test_death_integration.cpp
│       │   ├── drug_exposure/
│       │   │   └── test_drug_exposure-integration.cpp
│       │   ├── measurement/
│       │   │   └── test_measurement_integration.cpp
│       │   ├── note/
│       │   │   └── test_note_integration.cpp
│       │   ├── observation/
│       │   │   └── test_observation_integration.cpp
│       │   ├── observation_period/
│       │   │   └── test_observation_period_integration.cpp
│       │   ├── person/
│       │   │   └── test_person_integration.cpp
│       │   ├── procedure_occurrence/
│       │   │   └── test_procedure_occurrence-integration.cpp
│       │   ├── test_omop_tables_integration.cpp
│       │   ├── test_schema_creation_integration.cpp
│       │   ├── visit_detail/
│       │   │   └── test_visit_detail_integration.cpp
│       │   └── visit_occurrence/
│       │       └── test_visit_occurrence_integration.cpp
│       ├── common/         # Common integration tests
│       │   ├── CMakeLists.txt
│       │   ├── test_configuration_integration.cpp
│       │   ├── test_logging_integration.cpp
│       │   ├── test_utilities_integration.cpp
│       │   └── test_validation_integration.cpp
│       ├── config/         # Configuration integration tests
│       │   └── CMakeLists.txt
│       │   └── test_configuration_management.cpp
│       ├── core/           # Core integration tests
│       │   ├── CMakeLists.txt
│       │   ├── test_job_manager_integration.cpp
│       │   ├── test_job_scheduler_integration.cpp
│       │   ├── test_pipeline_integration.cpp
│       │   └── test_record_integration.cpp
│       ├── e2e/            # End-to-end integration tests
│       │   ├── CMakeLists.txt
│       │   ├── test_advanced_transformation_integration.cpp
│       │   ├── test_cross_module_integration.cpp
│       │   ├── test_data_quality_integration.cpp
│       │   ├── test_full_pipeline_integration.cpp
│       │   └── test_performance_integration.cpp
│       ├── extract/        # Extract integration tests
│       │   ├── CMakeLists.txt
│       │   ├── extractor_integration_test.cpp
│       │   ├── test_csv_extractor_integration.cpp
│       │   ├── test_database_extractor_integration.cpp
│       │   └── test_json_extractor_integration.cpp
│       ├── load/           # Load integration tests
│       │   ├── CMakeLists.txt
│       │   ├── test_batch_inserter_integration.cpp
│       │   ├── test_database_loader_integration.cpp
│       │   ├── test_loader_performance_integration.cpp
│       │   └── test_transaction_integration.cpp
│       ├── monitoring/     # Monitoring integration tests
│       │   ├── CMakeLists.txt
│       │   ├── test_alerting_integration.cpp
│       │   ├── test_logging_integration.cpp
│       │   ├── test_metrics_integration.cpp
│       │   └── test_performance_monitoring_integration.cpp
│       ├── performance/    # Performance integration tests
│       │   ├── CMakeLists.txt
│       │   ├── test_concurrent_operations.cpp
│       │   ├── test_load_performance.cpp
│       │   ├── test_memory_usage_integration.cpp
│       │   └── test_throughput_integration.cpp
│       ├── quality/        # Data quality integration tests
│       │   ├── CMakeLists.txt
│       │   ├── test_anomaly_detection.cpp
│       │   ├── test_data_lineage.cpp
│       │   ├── test_data_quality_integration.cpp
│       │   └── test_validation_integration.cpp
│       ├── security/       # Security integration tests
│       │   ├── CMakeLists.txt
│       │   └── test_authentication_integration.cpp
│       ├── service/        # Service integration tests
│       │   ├── CMakeLists.txt
│       │   ├── test_advanced_loaders_integration.txt
│       │   ├── test_advanced_transformations_integration.txt
│       │   ├── test_etl_service_integration.cpp
│       │   ├── test_service_lifecycle_integration.cpp
│       │   ├── test_service_manager_integration.cpp
│       │   ├── test_service_performance_integration.cpp
│       │   └── test_service_reliability_integration.cpp
│       ├── test_data/      # Test data files
│       │   ├── csv/
│       │   │   ├── conditions.csv
│       │   │   ├── medications.csv
│       │   │   ├── patients.csv
│       │   │   ├── procedures.csv
│       │   │   ├── test_data.csv
│       │   │   ├── test_data_compressed.csv.gz
│       │   │   ├── test_data_large.csv
│       │   │   ├── test_data_malformed.csv
│       │   │   ├── test_data_unicode.csv
│       │   │   ├── test_data_utf8.csv
│       │   │   └── vocabulary.csv
│       │   ├── json/
│       │   │   ├── clinical_data.json
│       │   │   ├── patient_records.json
│       │   │   └── vocabulary_mappings.json
│       │   ├── sql/
│       │   │   ├── test_data.sql
│       │   │   └── test_schema.sql
│       │   └── yaml/
│       │       ├── advanced_mapping_config.yaml
│       │       ├── mapping_config.yaml
│       │       └── test_config.yaml
│       ├── test_helpers/   # Test helper utilities
│       │   ├── CMakeLists.txt
│       │   ├── database_fixture.cpp
│       │   ├── database_fixture.h
│       │   ├── test_utils.cpp
│       │   ├── test_utils.h
│       │   └── test_environment.cpp
│       ├── transform/      # Transform integration tests
│       │   ├── CMakeLists.txt
│       │   ├── test_complex_transformations_integration.cpp
│       │   ├── test_custom_transformations_integration.cpp
│       │   ├── test_transformation_engine_integration.cpp
│       │   └── test_vocabulary_integration.cpp
│       ├── workflow/       # Workflow integration tests
│       │   ├── CMakeLists.txt
│       │   ├── test_batch_workflow_integration.cpp
│       │   ├── test_complex_etl_workflow.cpp
│       │   ├── test_error_handling_integration.cpp
│       │   ├── test_workflow_management_integration.cpp
│       │   └── test_workflow_performance_integration.cpp
├── examples/               # Example configurations
│   └── simple_patient_etl.yaml
├── docs/                   # Documentation
│   ├── api/               # API documentation
│   │   └── openapi.yaml
│   ├── design/            # Design documents
│   │   ├── architecture.md
│   │   └── data_flow.md
│   ├── development/       # Development documentation
│   │   ├── BUILD_TARGETS.md
│   │   ├── DOCKER_BUILD_GUIDE.md
│   │   ├── DOCKER-MULTIARCH.md
│   │   ├── DOCKER-USAGE.md
│   │   ├── DOCKER.md
│   │   └── QUICK_REFERENCE.md
│   ├── user/             # User documentation
│   │   ├── installation.md
│   │   └── usage.md
│   ├── implementation_guide.md
│   └── omop-project-structure.md
├── test_configs/         # Test configuration files
│   └── test_pipeline.yaml
├── test_csv_output/      # Test output directory
│   └── output.csv
├── Testing/              # CTest output (temporary)
│   └── Temporary/
│       ├── CTestCostData.txt
│       └── LastTest.log
└── scripts/              # Build and deployment scripts
```

The C++ source code files in the project directory src/lib/common is pasted at the end of this content given below. Check the source code header and implementation files in the project contained in the folder src/lib/common for any coding issues, bugs, assumptions, completeness, and code coverage issues. Create patch files for fixing the source code header and implementation files in the project folder src/lib/common for the issues discovered in exisitng code including removing un-implemented code and removing TODO code snippets. Refactor existing code in the folder src/lib/common.

Also is attached the unit tests for the src/lib/common library - tests/unit/common. Ensure that the src/lib/common library's unit test cases have 100% test coverage.

Make sure to save the processed files and make them available to download before the Max usage limit is reached.

The C++ source code header and implementation files in the project directory src/app/common.

```
File src/lib/common/exceptions.cpp:

#include "exceptions.h"

namespace omop::common {

OmopException::OmopException(std::string_view message,
                           const std::source_location& location)
    : message_(message), location_(location) {
    formatted_message_ = std::format("[{}:{}] {}: {}",
        location_.file_name(),
        location_.line(),
        location_.function_name(),
        message_);
}

const char* OmopException::what() const noexcept {
    return formatted_message_.c_str();
}

std::string_view OmopException::message() const noexcept {
    return message_;
}

const std::source_location& OmopException::location() const noexcept {
    return location_;
}

ConfigurationException::ConfigurationException(std::string_view message,
                                            std::string_view config_key,
                                            const std::source_location& location)
    : OmopException(std::format("{}: key '{}'", message, config_key), location),
      config_key_(config_key) {}

std::string_view ConfigurationException::config_key() const noexcept {
    return config_key_;
}

ExtractionException::ExtractionException(std::string_view message,
                                       std::string_view source_name,
                                       const std::source_location& location)
    : OmopException(std::format("Extraction error from '{}': {}", source_name, message), location),
      source_name_(source_name) {}

std::string_view ExtractionException::source_name() const noexcept {
    return source_name_;
}

TransformationException::TransformationException(std::string_view message,
                                               std::string_view field_name,
                                               std::string_view transformation_type,
                                               const std::source_location& location)
    : OmopException(std::format("Transformation error for field '{}' (type: {}): {}",
                               field_name, transformation_type, message), location),
      field_name_(field_name),
      transformation_type_(transformation_type) {}

std::string_view TransformationException::field_name() const noexcept {
    return field_name_;
}

std::string_view TransformationException::transformation_type() const noexcept {
    return transformation_type_;
}

LoadException::LoadException(std::string_view message,
                           std::string_view target_table,
                           const std::source_location& location)
    : OmopException(std::format("Load error for table '{}': {}", target_table, message), location),
      target_table_(target_table) {}

std::string_view LoadException::target_table() const noexcept {
    return target_table_;
}

DatabaseException::DatabaseException(std::string_view message,
                                   std::string_view database_type,
                                   int error_code,
                                   const std::source_location& location)
    : OmopException(std::format("Database error [{}] (code: {}): {}",
                               database_type, error_code, message), location),
      database_type_(database_type),
      error_code_(error_code) {}

std::string_view DatabaseException::database_type() const noexcept {
    return database_type_;
}

int DatabaseException::error_code() const noexcept {
    return error_code_;
}

SecurityException::SecurityException(std::string_view message,
                                   std::string_view security_context,
                                   const std::source_location& location)
    : OmopException(std::format("Security error in context '{}': {}", security_context, message), location),
      security_context_(security_context) {}

std::string_view SecurityException::security_context() const noexcept {
    return security_context_;
}

ValidationException::ValidationException(std::string_view message,
                                       std::string_view rule_name,
                                       std::string_view field_value,
                                       const std::source_location& location)
    : OmopException(std::format("Validation failed for rule '{}' with value '{}': {}",
                               rule_name, field_value, message), location),
      rule_name_(rule_name),
      field_value_(field_value) {}

std::string_view ValidationException::rule_name() const noexcept {
    return rule_name_;
}

std::string_view ValidationException::field_value() const noexcept {
    return field_value_;
}

VocabularyException::VocabularyException(std::string_view message,
                                       std::string_view vocabulary_name,
                                       std::string_view source_value,
                                       const std::source_location& location)
    : OmopException(std::format("Vocabulary mapping error for '{}' with value '{}': {}",
                               vocabulary_name, source_value, message), location),
      vocabulary_name_(vocabulary_name),
      source_value_(source_value) {}

std::string_view VocabularyException::vocabulary_name() const noexcept {
    return vocabulary_name_;
}

std::string_view VocabularyException::source_value() const noexcept {
    return source_value_;
}

ApiException::ApiException(std::string_view message,
                         int http_status,
                         std::string_view endpoint,
                         const std::source_location& location)
    : OmopException(std::format("API error at endpoint '{}' (status: {}): {}",
                               endpoint, http_status, message), location),
      http_status_(http_status),
      endpoint_(endpoint) {}

int ApiException::http_status() const noexcept {
    return http_status_;
}

std::string_view ApiException::endpoint() const noexcept {
    return endpoint_;
}

PlatformException::PlatformException(std::string_view message,
                                    std::string_view operation_name,
                                    std::string_view platform_type,
                                    const std::source_location& location)
    : OmopException(std::format("Platform error on {} during {}: {}",
                               platform_type, operation_name, message), location),
      operation_name_(operation_name),
      platform_type_(platform_type) {}

std::string_view PlatformException::operation_name() const noexcept {
    return operation_name_;
}

std::string_view PlatformException::platform_type() const noexcept {
    return platform_type_;
}

} // namespace omop::common

File src/lib/common/validation.cpp:

/**
 * @file validation.cpp
 * @brief Implementation of data validation framework for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "validation.h"
#include "exceptions.h"
#include <algorithm>
#include <sstream>
#include <iomanip>
#include <cctype>
#include <any>
#include <optional>
#include <typeinfo>
#include <yaml-cpp/yaml.h>
#include <chrono>
#include <nlohmann/json.hpp>

namespace omop::common {

// ValidationResult implementation
void ValidationResult::merge(const ValidationResult& other) {
    is_valid = is_valid && other.is_valid;
    errors.insert(errors.end(), other.errors.begin(), other.errors.end());
    warnings.insert(warnings.end(), other.warnings.begin(), other.warnings.end());
    records_validated += other.records_validated;
    records_failed += other.records_failed;
}

// ValidationRule base class implementation
ValidationRule::ValidationRule(const std::string& field_name,
                             ValidationType type,
                             const std::string& error_message)
    : field_name_(field_name), type_(type), error_message_(error_message) {}

std::string ValidationRule::getErrorMessage() const {
    if (!error_message_.empty()) {
        return error_message_;
    }

    // Generate default error message based on validation type
    std::stringstream ss;
    ss << "Field '" << field_name_ << "' failed validation: ";

    switch (type_) {
        case ValidationType::NOT_NULL:
            ss << "value is null";
            break;
        case ValidationType::NOT_EMPTY:
            ss << "value is empty";
            break;
        case ValidationType::UNIQUE:
            ss << "value is not unique";
            break;
        case ValidationType::IN_LIST:
            ss << "value is not in allowed list";
            break;
        case ValidationType::REGEX:
            ss << "value does not match required pattern";
            break;
        case ValidationType::DATE_RANGE:
            ss << "date is outside allowed range";
            break;
        case ValidationType::NUMERIC_RANGE:
            ss << "number is outside allowed range";
            break;
        case ValidationType::GREATER_THAN:
            ss << "value is not greater than required minimum";
            break;
        case ValidationType::LESS_THAN:
            ss << "value is not less than required maximum";
            break;
        case ValidationType::BETWEEN:
            ss << "value is not between required bounds";
            break;
        case ValidationType::BEFORE:
            ss << "date is not before required date";
            break;
        case ValidationType::AFTER:
            ss << "date is not after required date";
            break;
        case ValidationType::LENGTH:
            ss << "string length is invalid";
            break;
        case ValidationType::CUSTOM:
            ss << "custom validation failed";
            break;
        case ValidationType::NOT_ZERO:
            ss << "value is zero";
            break;
        case ValidationType::NOT_FUTURE_DATE:
            ss << "date is in the future";
            break;
        case ValidationType::FOREIGN_KEY:
            ss << "foreign key constraint violation";
            break;
        case ValidationType::COMPOSITE_KEY:
            ss << "composite key validation failed";
            break;
        case ValidationType::CONDITIONAL:
            ss << "conditional validation failed";
            break;
        default:
            ss << "validation failed";
    }

    return ss.str();
}

// NotNullRule implementation
NotNullRule::NotNullRule(const std::string& field_name)
    : ValidationRule(field_name, ValidationType::NOT_NULL) {}

bool NotNullRule::validate(const std::any& value,
                          const std::unordered_map<std::string, std::any>& record) const {
    return value.has_value();
}

// InListRule implementation
template<typename T>
InListRule<T>::InListRule(const std::string& field_name, const std::vector<T>& allowed_values)
    : ValidationRule(field_name, ValidationType::IN_LIST), allowed_values_(allowed_values) {}

template<typename T>
bool InListRule<T>::validate(const std::any& value,
                            const std::unordered_map<std::string, std::any>& record) const {
    if (!value.has_value()) {
        return false;
    }

    try {
        const T& val = std::any_cast<const T&>(value);
        return std::find(allowed_values_.begin(), allowed_values_.end(), val) != allowed_values_.end();
    } catch (const std::bad_any_cast&) {
        return false;
    }
}

// Explicit template instantiations
template class InListRule<int>;
template class InListRule<int64_t>;
template class InListRule<std::string>;
template class InListRule<double>;

// DateRangeRule implementation
DateRangeRule::DateRangeRule(const std::string& field_name,
                           const std::optional<std::chrono::system_clock::time_point>& min_date,
                           const std::optional<std::chrono::system_clock::time_point>& max_date)
    : ValidationRule(field_name, ValidationType::DATE_RANGE),
      min_date_(min_date), max_date_(max_date) {}

bool DateRangeRule::validate(const std::any& value,
                           const std::unordered_map<std::string, std::any>& record) const {
    if (!value.has_value()) {
        return false;
    }

    try {
        const auto& date = std::any_cast<const std::chrono::system_clock::time_point&>(value);

        if (min_date_ && date < *min_date_) {
            return false;
        }

        if (max_date_ && date > *max_date_) {
            return false;
        }

        return true;
    } catch (const std::bad_any_cast&) {
        return false;
    }
}

// NumericRangeRule implementation
template<typename T>
NumericRangeRule<T>::NumericRangeRule(const std::string& field_name,
                                     const std::optional<T>& min_value,
                                     const std::optional<T>& max_value)
    : ValidationRule(field_name, ValidationType::NUMERIC_RANGE),
      min_value_(min_value), max_value_(max_value) {}

template<typename T>
bool NumericRangeRule<T>::validate(const std::any& value,
                                  const std::unordered_map<std::string, std::any>& record) const {
    if (!value.has_value()) {
        return false;
    }

    try {
        // First try to cast to the exact type T
        const T& val = std::any_cast<const T&>(value);

        if (min_value_ && val < *min_value_) {
            return false;
        }

        if (max_value_ && val > *max_value_) {
            return false;
        }

        return true;
    } catch (const std::bad_any_cast&) {
        // If exact type cast fails, try to convert from other numeric types
        try {
            // Attempt safe numeric conversions
            if constexpr (std::is_same_v<T, double>) {
                if (value.type() == typeid(int)) {
                    int int_val = std::any_cast<int>(value);
                    double val = static_cast<double>(int_val);
                    
                    if (min_value_ && val < *min_value_) {
                        return false;
                    }
                    
                    if (max_value_ && val > *max_value_) {
                        return false;
                    }
                    
                    return true;
                } else if (value.type() == typeid(float)) {
                    float float_val = std::any_cast<float>(value);
                    double val = static_cast<double>(float_val);
                    
                    if (min_value_ && val < *min_value_) {
                        return false;
                    }
                    
                    if (max_value_ && val > *max_value_) {
                        return false;
                    }
                    
                    return true;
                }
            } else if constexpr (std::is_same_v<T, int>) {
                if (value.type() == typeid(double)) {
                    double double_val = std::any_cast<double>(value);
                    
                    // Check if conversion is safe
                    if (double_val != std::floor(double_val) ||
                        double_val < std::numeric_limits<int>::min() ||
                        double_val > std::numeric_limits<int>::max()) {
                        return false;
                    }
                    
                    int val = static_cast<int>(double_val);

                    if (min_value_ && val < *min_value_) {
                        return false;
                    }

                    if (max_value_ && val > *max_value_) {
                        return false;
                    }

                    return true;
                }
            }
        } catch (const std::bad_any_cast&) {
            // Log the type mismatch for debugging
            return false;
        }
        
        // If we reach here, type conversion failed
        return false;
    }
}

// Explicit template instantiations
template class NumericRangeRule<int>;
template class NumericRangeRule<int64_t>;
template class NumericRangeRule<double>;
template class NumericRangeRule<float>;

// RegexRule implementation
RegexRule::RegexRule(const std::string& field_name, const std::string& pattern)
    : ValidationRule(field_name, ValidationType::REGEX), pattern_(pattern) {}

bool RegexRule::validate(const std::any& value,
                        const std::unordered_map<std::string, std::any>& record) const {
    if (!value.has_value()) {
        return false;
    }

    try {
        const auto& str = std::any_cast<const std::string&>(value);
        return std::regex_match(str, pattern_);
    } catch (const std::bad_any_cast&) {
        return false;
    }
}

// CustomRule implementation
CustomRule::CustomRule(const std::string& field_name,
                      ValidationFunction validator,
                      const std::string& error_message)
    : ValidationRule(field_name, ValidationType::CUSTOM, error_message),
      validator_(validator) {}

bool CustomRule::validate(const std::any& value,
                         const std::unordered_map<std::string, std::any>& record) const {
    return validator_(value, record);
}

// ValidationEngine implementation
ValidationEngine::ValidationEngine(std::shared_ptr<Logger> logger)
    : logger_(logger ? logger : Logger::get("ValidationEngine")) {
    // Enable all validation types by default
    for (int i = 0; i <= static_cast<int>(ValidationType::CONDITIONAL); ++i) {
        enabled_types_[static_cast<ValidationType>(i)] = true;
    }
}

void ValidationEngine::addRule(std::unique_ptr<ValidationRule> rule) {
    rules_.push_back(std::move(rule));
}

ValidationResult ValidationEngine::validateRecord(const std::unordered_map<std::string, std::any>& record) {
    ValidationResult result;
    result.is_valid = true;
    result.records_validated = 1;
    result.records_failed = 0;

    for (const auto& rule : rules_) {
        if (!enabled_types_[rule->getType()]) {
            continue;
        }

        const auto& field_name = rule->getFieldName();
        auto it = record.find(field_name);

        std::any value;
        if (it != record.end()) {
            value = it->second;
        }

        if (!rule->validate(value, record)) {
            result.is_valid = false;
            result.errors.push_back(rule->getErrorMessage());
            logger_->debug("Validation failed for field '{}': {}",
                         field_name, rule->getErrorMessage());
        }
    }

    if (!result.is_valid) {
        result.records_failed = 1;
    }

    return result;
}

ValidationResult ValidationEngine::validateBatch(
    const std::vector<std::unordered_map<std::string, std::any>>& records,
    bool stop_on_error) {

    ValidationResult total_result;
    total_result.is_valid = true;
    total_result.records_validated = 0;
    total_result.records_failed = 0;

    for (size_t i = 0; i < records.size(); ++i) {
        auto result = validateRecord(records[i]);

        if (!result.is_valid) {
            // Add record index to error messages
            for (auto& error : result.errors) {
                error = "Record " + std::to_string(i) + ": " + error;
            }

            if (stop_on_error) {
                total_result.merge(result);
                break;
            }
        }

        total_result.merge(result);
        
        // Break early if we've exceeded a reasonable error threshold
        if (total_result.errors.size() > 1000) {
            total_result.errors.push_back("Error limit exceeded, validation stopped");
            break;
        }
    }

    return total_result;
}

void ValidationEngine::clearRules() {
    rules_.clear();
}

void ValidationEngine::setValidationTypeEnabled(ValidationType type, bool enabled) {
    enabled_types_[type] = enabled;
}

// ValidationRuleFactory implementation
std::unique_ptr<ValidationRule> ValidationRuleFactory::createRule(
    const std::unordered_map<std::string, std::any>& config) {

    auto field_it = config.find("field");
    auto type_it = config.find("type");

    if (field_it == config.end() || type_it == config.end()) {
        throw ConfigurationException("Validation rule missing required 'field' or 'type'");
    }

    std::string field_name = std::any_cast<std::string>(field_it->second);
    std::string type_str = std::any_cast<std::string>(type_it->second);

    // Get custom error message if provided
    std::string error_message;
    auto error_it = config.find("error_message");
    if (error_it != config.end()) {
        error_message = std::any_cast<std::string>(error_it->second);
    }

    if (type_str == "not_null") {
        return std::make_unique<NotNullRule>(field_name);
    }
    else if (type_str == "in_list") {
        auto values_it = config.find("values");
        if (values_it == config.end()) {
            throw ConfigurationException("in_list validation requires 'values' parameter");
        }

        // Handle different value types
        try {
            auto int_values = std::any_cast<std::vector<int>>(values_it->second);
            return std::make_unique<InListRule<int>>(field_name, int_values);
        } catch (const std::bad_any_cast&) {
            try {
                auto str_values = std::any_cast<std::vector<std::string>>(values_it->second);
                return std::make_unique<InListRule<std::string>>(field_name, str_values);
            } catch (const std::bad_any_cast&) {
                try {
                    auto double_values = std::any_cast<std::vector<double>>(values_it->second);
                    return std::make_unique<InListRule<double>>(field_name, double_values);
                } catch (const std::bad_any_cast&) {
                    throw ConfigurationException("Invalid values type for in_list validation");
                }
            }
        }
    }
    else if (type_str == "regex") {
        auto pattern_it = config.find("pattern");
        if (pattern_it == config.end()) {
            throw ConfigurationException("regex validation requires 'pattern' parameter");
        }

        std::string pattern = std::any_cast<std::string>(pattern_it->second);
        return std::make_unique<RegexRule>(field_name, pattern);
    }
    else if (type_str == "numeric_range") {
        std::optional<double> min_value, max_value;

        auto min_it = config.find("min");
        if (min_it != config.end()) {
            // Handle both int and double types
            try {
                min_value = std::any_cast<double>(min_it->second);
            } catch (const std::bad_any_cast&) {
                try {
                    min_value = static_cast<double>(std::any_cast<int>(min_it->second));
                } catch (const std::bad_any_cast&) {
                    throw ConfigurationException("Invalid min value type for numeric_range validation");
                }
            }
        }

        auto max_it = config.find("max");
        if (max_it != config.end()) {
            // Handle both int and double types
            try {
                max_value = std::any_cast<double>(max_it->second);
            } catch (const std::bad_any_cast&) {
                try {
                    max_value = static_cast<double>(std::any_cast<int>(max_it->second));
                } catch (const std::bad_any_cast&) {
                    throw ConfigurationException("Invalid max value type for numeric_range validation");
                }
            }
        }

        return std::make_unique<NumericRangeRule<double>>(field_name, min_value, max_value);
    }
    else if (type_str == "date_range") {
        std::optional<std::chrono::system_clock::time_point> min_date, max_date;

        auto min_it = config.find("min_date");
        if (min_it != config.end()) {
            // Parse date string
            std::string min_date_str = std::any_cast<std::string>(min_it->second);
            std::tm tm = {};
            std::istringstream ss(min_date_str);
            ss >> std::get_time(&tm, "%Y-%m-%d");
            if (!ss.fail()) {
                min_date = std::chrono::system_clock::from_time_t(std::mktime(&tm));
            }
        }

        auto max_it = config.find("max_date");
        if (max_it != config.end()) {
            // Parse date string
            std::string max_date_str = std::any_cast<std::string>(max_it->second);
            std::tm tm = {};
            std::istringstream ss(max_date_str);
            ss >> std::get_time(&tm, "%Y-%m-%d");
            if (!ss.fail()) {
                max_date = std::chrono::system_clock::from_time_t(std::mktime(&tm));
            }
        }

        return std::make_unique<DateRangeRule>(field_name, min_date, max_date);
    }
    else if (type_str == "custom") {
        // For custom rules, check if a validator function is provided in config
        auto validator_it = config.find("validator");
        if (validator_it != config.end()) {
            try {
                auto validator_func = std::any_cast<CustomRule::ValidationFunction>(validator_it->second);
                return std::make_unique<CustomRule>(field_name, validator_func, error_message);
            } catch (const std::bad_any_cast&) {
                throw ConfigurationException("Invalid validator function for custom validation rule");
            }
        }
        
        // If no validator provided, throw exception
        throw ConfigurationException("Custom validation rules must be registered programmatically");
    }

    throw ConfigurationException("Unknown validation type: " + type_str);
}

std::unique_ptr<ValidationEngine> ValidationRuleFactory::createEngineFromYaml(const std::string& yaml_config) {
    auto engine = std::make_unique<ValidationEngine>();

    try {
        YAML::Node config = YAML::Load(yaml_config);

        if (config["validation_rules"]) {
            for (const auto& rule_node : config["validation_rules"]) {
                std::unordered_map<std::string, std::any> rule_config;

                for (const auto& item : rule_node) {
                    std::string key = item.first.as<std::string>();

                    // Convert YAML node to appropriate type
                    if (item.second.IsScalar()) {
                        // Try to determine the type
                        std::string scalar_value = item.second.as<std::string>();

                        // Try to parse as number first
                        try {
                            if (scalar_value.find('.') != std::string::npos) {
                                double d = std::stod(scalar_value);
                                rule_config[key] = d;
                            } else {
                                int i = std::stoi(scalar_value);
                                rule_config[key] = i;
                            }
                        } catch (...) {
                            // Not a number, store as string
                            rule_config[key] = scalar_value;
                        }
                    } else if (item.second.IsSequence()) {
                        // Check if it's a list of strings or numbers
                        if (item.second.size() > 0) {
                            if (item.second[0].IsScalar()) {
                                std::string first_val = item.second[0].as<std::string>();

                                try {
                                    // Try integer list
                                    std::stoi(first_val);
                                    std::vector<int> values;
                                    for (const auto& val : item.second) {
                                        values.push_back(val.as<int>());
                                    }
                                    rule_config[key] = values;
                                } catch (...) {
                                    try {
                                        // Try double list
                                        std::stod(first_val);
                                        std::vector<double> values;
                                        for (const auto& val : item.second) {
                                            values.push_back(val.as<double>());
                                        }
                                        rule_config[key] = values;
                                    } catch (...) {
                                        // String list
                                        std::vector<std::string> values;
                                        for (const auto& val : item.second) {
                                            values.push_back(val.as<std::string>());
                                        }
                                        rule_config[key] = values;
                                    }
                                }
                            }
                        }
                    }
                }

                auto rule = createRule(rule_config);
                engine->addRule(std::move(rule));
            }
        }
    } catch (const YAML::Exception& e) {
        throw ConfigurationException("Failed to parse validation YAML: " + std::string(e.what()));
    }

    // Set up default validation types if specified
    if (config["validation_settings"]) {
        if (config["validation_settings"]["enabled_types"]) {
            for (const auto& type_node : config["validation_settings"]["enabled_types"]) {
                // Implementation for enabling/disabling specific validation types
            }
        }
    }

    return engine;
}

} // namespace omop::common

File src/lib/common/logging.cpp:

/**
 * @file logging.cpp
 * @brief Implementation of logging framework for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "logging.h"
#include <nlohmann/json.hpp>
#include <spdlog/sinks/basic_file_sink.h>
#include <spdlog/sinks/stdout_color_sinks.h>
#include <spdlog/sinks/rotating_file_sink.h>
#include <yaml-cpp/yaml.h>
#include <thread>
#include <sstream>
#include <iomanip>
#include <chrono>

namespace omop::common {

// Static member definitions
std::unordered_map<std::string, std::shared_ptr<Logger>> Logger::loggers_;
std::mutex Logger::loggers_mutex_;

// Helper function to convert LogLevel to spdlog level
static spdlog::level::level_enum to_spdlog_level(LogLevel level) {
    switch (level) {
        case LogLevel::Trace: return spdlog::level::trace;
        case LogLevel::Debug: return spdlog::level::debug;
        case LogLevel::Info: return spdlog::level::info;
        case LogLevel::Warning: return spdlog::level::warn;
        case LogLevel::Error: return spdlog::level::err;
        case LogLevel::Critical: return spdlog::level::critical;
        default: return spdlog::level::info;
    }
}

// Helper function to convert LogLevel to string
static std::string log_level_to_string(LogLevel level) {
    switch (level) {
        case LogLevel::Trace: return "TRACE";
        case LogLevel::Debug: return "DEBUG";
        case LogLevel::Info: return "INFO";
        case LogLevel::Warning: return "WARN";
        case LogLevel::Error: return "ERROR";
        case LogLevel::Critical: return "CRITICAL";
        default: return "UNKNOWN";
    }
}

// JsonLogFormatter implementation
std::string JsonLogFormatter::format(const LogEntry& entry) {
    nlohmann::json j;

    // Format timestamp as ISO 8601
    auto time_t = std::chrono::system_clock::to_time_t(entry.timestamp);
    std::stringstream ss;
    ss << std::put_time(std::gmtime(&time_t), "%Y-%m-%dT%H:%M:%S");

    // Add milliseconds
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        entry.timestamp.time_since_epoch()) % 1000;
    ss << "." << std::setfill('0') << std::setw(3) << ms.count() << "Z";

    j["timestamp"] = ss.str();
    j["level"] = log_level_to_string(entry.level);
    j["logger"] = entry.logger_name;
    j["message"] = entry.message;
    j["thread_id"] = entry.thread_id;

    if (!entry.job_id.empty()) {
        j["job_id"] = entry.job_id;
    }

    if (!entry.component.empty()) {
        j["component"] = entry.component;
    }

    if (!entry.operation.empty()) {
        j["operation"] = entry.operation;
    }

    if (entry.error_code.has_value()) {
        j["error_code"] = entry.error_code.value();
    }

    if (entry.stack_trace.has_value()) {
        j["stack_trace"] = entry.stack_trace.value();
    }

    // Add context fields
    if (!entry.context.empty()) {
        nlohmann::json context_json;
        for (const auto& [key, value] : entry.context) {
            // Handle different types in std::any
            if (value.type() == typeid(int)) {
                context_json[key] = std::any_cast<int>(value);
            } else if (value.type() == typeid(double)) {
                context_json[key] = std::any_cast<double>(value);
            } else if (value.type() == typeid(std::string)) {
                context_json[key] = std::any_cast<std::string>(value);
            } else if (value.type() == typeid(bool)) {
                context_json[key] = std::any_cast<bool>(value);
            } else if (value.type() == typeid(size_t)) {
                context_json[key] = std::any_cast<size_t>(value);
            } else {
                // Try to convert to string
                try {
                    context_json[key] = std::any_cast<std::string>(value);
                } catch (...) {
                    context_json[key] = "<unsupported type>";
                }
            }
        }
        j["context"] = context_json;
    }

    if (pretty_print_) {
        return j.dump(4);
    } else {
        return j.dump();
    }
}

// TextLogFormatter implementation
TextLogFormatter::TextLogFormatter(const std::string& pattern)
    : pattern_(pattern) {}

std::string TextLogFormatter::format(const LogEntry& entry) {
    std::string result = pattern_;

    // Format timestamp
    auto time_t = std::chrono::system_clock::to_time_t(entry.timestamp);
    std::stringstream timestamp_ss;
    timestamp_ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");

    // Add milliseconds
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        entry.timestamp.time_since_epoch()) % 1000;
    timestamp_ss << "." << std::setfill('0') << std::setw(3) << ms.count();

    // Replace placeholders
    size_t pos = 0;
    while ((pos = result.find("%Y-%m-%d %H:%M:%S.%e", pos)) != std::string::npos) {
        result.replace(pos, 21, timestamp_ss.str());
        pos += timestamp_ss.str().length();
    }

    // Replace level
    pos = 0;
    while ((pos = result.find("%l", pos)) != std::string::npos) {
        std::string level_str = log_level_to_string(entry.level);
        result.replace(pos, 2, level_str);
        pos += level_str.length();
    }

    // Replace logger name
    pos = 0;
    while ((pos = result.find("%n", pos)) != std::string::npos) {
        result.replace(pos, 2, entry.logger_name);
        pos += entry.logger_name.length();
    }

    // Replace message
    pos = 0;
    while ((pos = result.find("%v", pos)) != std::string::npos) {
        std::string full_message = entry.message;

        // Append job_id and component if present
        if (!entry.job_id.empty() || !entry.component.empty()) {
            full_message += " [";
            if (!entry.job_id.empty()) {
                full_message += "job:" + entry.job_id;
                if (!entry.component.empty()) {
                    full_message += ", ";
                }
            }
            if (!entry.component.empty()) {
                full_message += "component:" + entry.component;
            }
            full_message += "]";
        }

        result.replace(pos, 2, full_message);
        pos += full_message.length();
    }

    return result;
}

// Logger implementation
std::shared_ptr<Logger> Logger::get(const std::string& name) {
    std::lock_guard<std::mutex> lock(loggers_mutex_);

    auto it = loggers_.find(name);
    if (it != loggers_.end()) {
        return it->second;
    }

    // Use make_shared for exception safety and atomicity
    try {
        auto logger = std::make_shared<Logger>(name);
        loggers_[name] = logger;
        return logger;
    } catch (...) {
        // If logger creation fails, ensure map remains unchanged
        throw;
    }
}

Logger::Logger(const std::string& name)
    : name_(name) {
    // Create spdlog logger with console sink by default
    auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
    spdlog_logger_ = std::make_shared<spdlog::logger>(name, console_sink);
    spdlog_logger_->set_level(spdlog::level::info);
}

void Logger::set_level(LogLevel level) {
    min_level_ = level;
    if (spdlog_logger_) {
        spdlog_logger_->set_level(to_spdlog_level(level));
    }
}

void Logger::add_sink(std::shared_ptr<ILogSink> sink) {
    if (!sink) {
        return;
    }
    
    // Thread-safe sink addition
    std::lock_guard<std::mutex> lock(sinks_mutex_);
    sinks_.push_back(sink);
}

void Logger::log_structured(LogLevel level, const std::string& msg,
                           const std::unordered_map<std::string, std::any>& context) {
    if (level < min_level_) return;

    LogEntry entry{
        .timestamp = std::chrono::system_clock::now(),
        .level = level,
        .logger_name = name_,
        .message = msg,
        .thread_id = get_thread_id(),
        .job_id = job_id_,
        .component = component_,
        .operation = "",
        .context = context,
        .error_code = std::nullopt,
        .stack_trace = std::nullopt
    };

    write_entry(entry);
}

void Logger::log_operation(const std::string& operation,
                          const std::string& status,
                          const std::unordered_map<std::string, std::any>& details) {
    std::unordered_map<std::string, std::any> context = details;
    context["operation_status"] = status;

    LogEntry entry{
        .timestamp = std::chrono::system_clock::now(),
        .level = LogLevel::Info,
        .logger_name = name_,
        .message = std::format("Operation '{}' completed with status: {}", operation, status),
        .thread_id = get_thread_id(),
        .job_id = job_id_,
        .component = component_,
        .operation = operation,
        .context = context,
        .error_code = std::nullopt,
        .stack_trace = std::nullopt
    };

    write_entry(entry);
}

void Logger::log_metrics(const std::unordered_map<std::string, double>& metrics) {
    std::unordered_map<std::string, std::any> context;
    for (const auto& [key, value] : metrics) {
        context[key] = value;
    }

    LogEntry entry{
        .timestamp = std::chrono::system_clock::now(),
        .level = LogLevel::Info,
        .logger_name = name_,
        .message = "Metrics update",
        .thread_id = get_thread_id(),
        .job_id = job_id_,
        .component = component_,
        .operation = "metrics",
        .context = context,
        .error_code = std::nullopt,
        .stack_trace = std::nullopt
    };

    write_entry(entry);
}

void Logger::log_exception(const std::exception& e,
                          const std::unordered_map<std::string, std::any>& context) {
    LogEntry entry{
        .timestamp = std::chrono::system_clock::now(),
        .level = LogLevel::Error,
        .logger_name = name_,
        .message = std::format("Exception caught: {}", e.what()),
        .thread_id = get_thread_id(),
        .job_id = job_id_,
        .component = component_,
        .operation = "",
        .context = context,
        .error_code = std::nullopt,
        .stack_trace = std::string(e.what())  // In production, use proper stack trace
    };

    write_entry(entry);
}

void Logger::write_entry(const LogEntry& entry) {
    // Write to custom sinks
    {
        std::lock_guard<std::mutex> lock(sinks_mutex_);
        for (const auto& sink : sinks_) {
            if (sink) sink->write(entry);
        }
    }

    // Also write to spdlog
    if (spdlog_logger_) {
        switch (entry.level) {
            case LogLevel::Trace:
                spdlog_logger_->trace(entry.message);
                break;
            case LogLevel::Debug:
                spdlog_logger_->debug(entry.message);
                break;
            case LogLevel::Info:
                spdlog_logger_->info(entry.message);
                break;
            case LogLevel::Warning:
                spdlog_logger_->warn(entry.message);
                break;
            case LogLevel::Error:
                spdlog_logger_->error(entry.message);
                break;
            case LogLevel::Critical:
                spdlog_logger_->critical(entry.message);
                break;
        }
    }
}

std::string Logger::get_thread_id() const {
    std::stringstream ss;
    ss << std::this_thread::get_id();
    return ss.str();
}

// PerformanceLogger implementation
void PerformanceLogger::start_timing(const std::string& operation_id) {
    std::lock_guard<std::mutex> lock(timings_mutex_);
    timings_[operation_id] = std::chrono::steady_clock::now();
}

void PerformanceLogger::end_timing(const std::string& operation_id,
                                  std::optional<size_t> record_count) {
    std::lock_guard<std::mutex> lock(timings_mutex_);

    auto it = timings_.find(operation_id);
    if (it == timings_.end()) {
        logger_->warn("No start timing found for operation: {}", operation_id);
        return;
    }

    auto end_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
        end_time - it->second).count();

    std::unordered_map<std::string, std::any> metrics;
    metrics["duration_ms"] = static_cast<double>(duration);
    metrics["operation_id"] = operation_id;

    if (record_count.has_value()) {
        metrics["record_count"] = record_count.value();
        if (duration > 0) {
            double records_per_second = (record_count.value() * 1000.0) / duration;
            metrics["records_per_second"] = records_per_second;
        }
    }

    logger_->log_operation(operation_id, "completed", metrics);
    timings_.erase(it);
}

void PerformanceLogger::log_throughput(const std::string& operation,
                                      double records_per_second) {
    std::unordered_map<std::string, std::any> metrics;
    metrics["operation"] = operation;
    metrics["throughput_rps"] = records_per_second;

    logger_->log_metrics({{"throughput_" + operation, records_per_second}});
}

void PerformanceLogger::log_resource_usage(double cpu_percent,
                                          double memory_mb,
                                          double disk_io_mb) {
    std::unordered_map<std::string, double> metrics;
    metrics["cpu_percent"] = cpu_percent;
    metrics["memory_mb"] = memory_mb;
    metrics["disk_io_mb"] = disk_io_mb;

    logger_->log_metrics(metrics);
}

// AuditLogger implementation
void AuditLogger::log_data_access(const std::string& table_name,
                                 const std::string& operation,
                                 size_t record_count,
                                 const std::string& user) {
    if (!logger_) {
        return;
    }
    
    std::unordered_map<std::string, std::any> context;
    {
        // Ensure thread-safe context building
        context["table_name"] = table_name;
        context["operation"] = operation;
        context["record_count"] = record_count;
        context["user"] = user;
        context["audit_type"] = std::string("data_access");
    }

    logger_->log_structured(LogLevel::Info,
        std::format("Data access: {} {} on {} ({} records)",
                   user, operation, table_name, record_count),
        context);
}

void AuditLogger::log_config_change(const std::string& config_item,
                                   const std::string& old_value,
                                   const std::string& new_value,
                                   const std::string& user) {
    std::unordered_map<std::string, std::any> context;
    context["config_item"] = config_item;
    context["old_value"] = old_value;
    context["new_value"] = new_value;
    context["user"] = user;
    context["audit_type"] = std::string("config_change");

    logger_->log_structured(LogLevel::Info,
        std::format("Configuration change: {} changed '{}' from '{}' to '{}'",
                   user, config_item, old_value, new_value),
        context);
}

void AuditLogger::log_security_event(const std::string& event_type,
                                    const std::unordered_map<std::string, std::string>& details) {
    std::unordered_map<std::string, std::any> context;
    for (const auto& [key, value] : details) {
        context[key] = value;
    }
    context["audit_type"] = std::string("security");
    context["event_type"] = event_type;

    logger_->log_structured(LogLevel::Warning,
        std::format("Security event: {}", event_type),
        context);
}

// LoggingConfig implementation
void LoggingConfig::initialize(const std::string& config_file) {
    try {
        YAML::Node config = YAML::LoadFile(config_file);
        
        // Set global log level
        if (config["logging"] && config["logging"]["level"]) {
            std::string level_str = config["logging"]["level"].as<std::string>();
            LogLevel level = LogLevel::Info;
            
            if (level_str == "trace") level = LogLevel::Trace;
            else if (level_str == "debug") level = LogLevel::Debug;
            else if (level_str == "info") level = LogLevel::Info;
            else if (level_str == "warning" || level_str == "warn") level = LogLevel::Warning;
            else if (level_str == "error") level = LogLevel::Error;
            else if (level_str == "critical") level = LogLevel::Critical;
            
            set_global_level(level);
        }
        
        // Set log pattern
        if (config["logging"] && config["logging"]["pattern"]) {
            std::string pattern = config["logging"]["pattern"].as<std::string>();
            spdlog::set_pattern(pattern);
        }
        
        // Configure file output
        if (config["logging"] && config["logging"]["file"]) {
            auto file_config = config["logging"]["file"];
            std::string filename = file_config["path"].as<std::string>("logs/omop_etl.log");
            size_t max_size = file_config["max_size_mb"].as<size_t>(10) * 1048576;
            size_t max_files = file_config["max_files"].as<size_t>(5);
            
            auto file_sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
                filename, max_size, max_files);
            
            auto default_logger = std::make_shared<spdlog::logger>("default", file_sink);
            spdlog::set_default_logger(default_logger);
        }
    } catch (const std::exception& e) {
        // If configuration loading fails, fall back to default
        initialize_default();
    }
}

void LoggingConfig::initialize_default() {
    // Set default pattern for spdlog
    spdlog::set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%^%l%$] [%n] %v");

    // Set default level
    spdlog::set_level(spdlog::level::info);

    // Create default file sink
    auto file_sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
        "logs/omop_etl.log", 1048576 * 10, 5);

    // Set default logger
    auto default_logger = std::make_shared<spdlog::logger>("default", file_sink);
    spdlog::set_default_logger(default_logger);
}

void LoggingConfig::set_global_level(LogLevel level) {
    spdlog::set_level(to_spdlog_level(level));
}

void LoggingConfig::add_global_sink(std::shared_ptr<ILogSink> sink) {
    // Add sink to all existing loggers
    std::lock_guard<std::mutex> lock(Logger::loggers_mutex_);
    for (auto& [name, logger] : Logger::loggers_) {
        logger->add_sink(sink);
    }
}

void LoggingConfig::flush_all() {
    spdlog::apply_all([](std::shared_ptr<spdlog::logger> l) { l->flush(); });
    
    // Also flush custom sinks
    std::lock_guard<std::mutex> lock(Logger::loggers_mutex_);
    for (auto& [name, logger] : Logger::loggers_) {
        // Custom flush implementation if needed
    }
}

void LoggingConfig::shutdown() {
    flush_all();
    
    // Clear all loggers before spdlog shutdown
    {
        std::lock_guard<std::mutex> lock(Logger::loggers_mutex_);
        Logger::loggers_.clear();
    }
    
    spdlog::shutdown();
}

// Note: DatabaseLogSink implementation has been removed to eliminate circular dependency.
// Concrete database log sink implementations should be provided by the extract module
// or other modules that have access to database connections.

} // namespace omop::common

File src/lib/common/exceptions.h:

#ifndef OMOP_COMMON_EXCEPTIONS_H
#define OMOP_COMMON_EXCEPTIONS_H

#include <exception>
#include <string>
#include <string_view>
#include <source_location>
#include <format>

namespace omop::common {

/**
 * @brief Base exception class for the OMOP ETL pipeline
 *
 * This class serves as the root of the exception hierarchy for the entire
 * OMOP ETL pipeline project. It provides enhanced error information including
 * source location tracking and formatted error messages.
 */
class OmopException : public std::exception {
public:
    /**
     * @brief Construct a new OmopException
     * @param message Error message describing the exception
     * @param location Source location where the exception occurred
     */
    explicit OmopException(std::string_view message,
                           const std::source_location& location = std::source_location::current());

    /**
     * @brief Get the exception message
     * @return const char* Pointer to the error message
     */
    [[nodiscard]] const char* what() const noexcept override;

    /**
     * @brief Get the raw error message without location information
     * @return std::string_view The original error message
     */
    [[nodiscard]] std::string_view message() const noexcept;

    /**
     * @brief Get the source location where the exception occurred
     * @return const std::source_location& Reference to the source location
     */
    [[nodiscard]] const std::source_location& location() const noexcept;

protected:
    std::string message_;           ///< Original error message
    std::string formatted_message_; ///< Formatted message with location
    std::source_location location_; ///< Source location of the exception
};

/**
 * @brief Exception thrown during configuration operations
 */
class ConfigurationException : public OmopException {
public:
    using OmopException::OmopException;

    /**
     * @brief Construct a configuration exception with a specific config key
     * @param message Error message
     * @param config_key The configuration key that caused the error
     * @param location Source location
     */
    ConfigurationException(std::string_view message,
                           std::string_view config_key,
                           const std::source_location& location = std::source_location::current());

    /**
     * @brief Get the configuration key that caused the error
     * @return std::string_view The configuration key
     */
    [[nodiscard]] std::string_view config_key() const noexcept;

private:
    std::string config_key_;
};

/**
 * @brief Exception thrown during data extraction operations
 */
class ExtractionException : public OmopException {
public:
    using OmopException::OmopException;

    /**
     * @brief Construct an extraction exception with source details
     * @param message Error message
     * @param source_name Name of the data source
     * @param location Source location
     */
    ExtractionException(std::string_view message,
                        std::string_view source_name,
                        const std::source_location& location = std::source_location::current());

    /**
     * @brief Get the name of the data source
     * @return std::string_view The source name
     */
    [[nodiscard]] std::string_view source_name() const noexcept;

private:
    std::string source_name_;
};

/**
 * @brief Exception thrown during data transformation operations
 */
class TransformationException : public OmopException {
public:
    using OmopException::OmopException;

    /**
     * @brief Construct a transformation exception with field details
     * @param message Error message
     * @param field_name Name of the field being transformed
     * @param transformation_type Type of transformation attempted
     * @param location Source location
     */
    TransformationException(std::string_view message,
                            std::string_view field_name,
                            std::string_view transformation_type,
                            const std::source_location& location = std::source_location::current());

    /**
     * @brief Get the field name
     * @return std::string_view The field name
     */
    [[nodiscard]] std::string_view field_name() const noexcept;

    /**
     * @brief Get the transformation type
     * @return std::string_view The transformation type
     */
    [[nodiscard]] std::string_view transformation_type() const noexcept;

private:
    std::string field_name_;
    std::string transformation_type_;
};

/**
 * @brief Exception thrown during data loading operations
 */
class LoadException : public OmopException {
public:
    using OmopException::OmopException;

    /**
     * @brief Construct a load exception with target details
     * @param message Error message
     * @param target_table Target table name
     * @param location Source location
     */
    LoadException(std::string_view message,
                  std::string_view target_table,
                  const std::source_location& location = std::source_location::current());

    /**
     * @brief Get the target table name
     * @return std::string_view The target table name
     */
    [[nodiscard]] std::string_view target_table() const noexcept;

private:
    std::string target_table_;
};

/**
 * @brief Exception thrown during database operations
 */
class DatabaseException : public OmopException {
public:
    using OmopException::OmopException;

    /**
     * @brief Construct a database exception with connection details
     * @param message Error message
     * @param database_type Type of database (PostgreSQL, MySQL, etc.)
     * @param error_code Database-specific error code
     * @param location Source location
     */
    DatabaseException(std::string_view message,
                      std::string_view database_type,
                      int error_code,
                      const std::source_location& location = std::source_location::current());

    /**
     * @brief Get the database type
     * @return std::string_view The database type
     */
    [[nodiscard]] std::string_view database_type() const noexcept;

    /**
     * @brief Get the error code
     * @return int The database-specific error code
     */
    [[nodiscard]] int error_code() const noexcept;

private:
    std::string database_type_;
    int error_code_;
};

/**
 * @brief Exception thrown during security-related operations
 */
class SecurityException : public OmopException {
public:
    using OmopException::OmopException;

    /**
     * @brief Construct a security exception with security context
     * @param message Error message
     * @param security_context The security context (e.g., "database", "api", "file")
     * @param location Source location
     */
    SecurityException(std::string_view message,
                      std::string_view security_context,
                      const std::source_location& location = std::source_location::current());

    /**
     * @brief Get the security context
     * @return std::string_view The security context
     */
    [[nodiscard]] std::string_view security_context() const noexcept;

private:
    std::string security_context_;
};

/**
 * @brief Exception thrown during validation operations
 */
class ValidationException : public OmopException {
public:
    using OmopException::OmopException;

    /**
     * @brief Construct a validation exception with rule details
     * @param message Error message
     * @param rule_name Name of the validation rule
     * @param field_value The value that failed validation
     * @param location Source location
     */
    ValidationException(std::string_view message,
                        std::string_view rule_name,
                        std::string_view field_value,
                        const std::source_location& location = std::source_location::current());

    /**
     * @brief Get the validation rule name
     * @return std::string_view The rule name
     */
    [[nodiscard]] std::string_view rule_name() const noexcept;

    /**
     * @brief Get the field value that failed validation
     * @return std::string_view The field value
     */
    [[nodiscard]] std::string_view field_value() const noexcept;

private:
    std::string rule_name_;
    std::string field_value_;
};

/**
 * @brief Exception thrown during vocabulary mapping operations
 */
class VocabularyException : public OmopException {
public:
    using OmopException::OmopException;

    /**
     * @brief Construct a vocabulary exception with mapping details
     * @param message Error message
     * @param vocabulary_name Name of the vocabulary
     * @param source_value The value that couldn't be mapped
     * @param location Source location
     */
    VocabularyException(std::string_view message,
                        std::string_view vocabulary_name,
                        std::string_view source_value,
                        const std::source_location& location = std::source_location::current());

    /**
     * @brief Get the vocabulary name
     * @return std::string_view The vocabulary name
     */
    [[nodiscard]] std::string_view vocabulary_name() const noexcept;

    /**
     * @brief Get the source value
     * @return std::string_view The source value
     */
    [[nodiscard]] std::string_view source_value() const noexcept;

private:
    std::string vocabulary_name_;
    std::string source_value_;
};

/**
 * @brief Exception thrown during API operations
 */
class ApiException : public OmopException {
public:
    using OmopException::OmopException;

    /**
     * @brief Construct an API exception with HTTP details
     * @param message Error message
     * @param http_status HTTP status code
     * @param endpoint The API endpoint
     * @param location Source location
     */
    ApiException(std::string_view message,
                 int http_status,
                 std::string_view endpoint,
                 const std::source_location& location = std::source_location::current());

    /**
     * @brief Get the HTTP status code
     * @return int The HTTP status code
     */
    [[nodiscard]] int http_status() const noexcept;

    /**
     * @brief Get the API endpoint
     * @return std::string_view The endpoint
     */
    [[nodiscard]] std::string_view endpoint() const noexcept;

private:
    int http_status_;
    std::string endpoint_;
};

/**
 * @brief Exception thrown during platform-specific operations
 */
class PlatformException : public OmopException {
public:
    using OmopException::OmopException;

    /**
     * @brief Construct a platform exception with operation details
     * @param message Error message
     * @param operation_name Name of the platform operation
     * @param platform_type Type of platform (Windows, Unix, etc.)
     * @param location Source location
     */
    PlatformException(std::string_view message,
                      std::string_view operation_name,
                      std::string_view platform_type,
                      const std::source_location& location = std::source_location::current());

    /**
     * @brief Get the operation name
     * @return std::string_view The operation name
     */
    [[nodiscard]] std::string_view operation_name() const noexcept;

    /**
     * @brief Get the platform type
     * @return std::string_view The platform type
     */
    [[nodiscard]] std::string_view platform_type() const noexcept;

private:
    std::string operation_name_;
    std::string platform_type_;
};

} // namespace omop::common

#endif // OMOP_COMMON_EXCEPTIONS_H

File src/lib/common/utilities.h:

#pragma once

#include <string>
#include <vector>
#include <optional>
#include <chrono>
#include <filesystem>
#include <unordered_map>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <regex>
#include <uuid/uuid.h>
#include <any>
#include <typeinfo>

namespace omop::common {

/**
 * @brief Convert std::any to string representation
 * @param value Any value to convert
 * @return String representation
 */
std::string any_to_string(const std::any& value);

/**
 * @brief String utility functions
 */
class StringUtils {
public:
    /**
     * @brief Trim whitespace from both ends of a string
     * @param str String to trim
     * @return Trimmed string
     */
    static std::string trim(const std::string& str);

    /**
     * @brief Convert string to lowercase
     * @param str Input string
     * @return Lowercase string
     */
    static std::string to_lower(const std::string& str);

    /**
     * @brief Convert string to uppercase
     * @param str Input string
     * @return Uppercase string
     */
    static std::string to_upper(const std::string& str);

    /**
     * @brief Split string by delimiter
     * @param str String to split
     * @param delimiter Delimiter character
     * @return Vector of substrings
     */
    static std::vector<std::string> split(const std::string& str, char delimiter);

    /**
     * @brief Split string by string delimiter
     * @param str String to split
     * @param delimiter Delimiter string
     * @return Vector of substrings
     */
    static std::vector<std::string> split(const std::string& str, const std::string& delimiter);

    /**
     * @brief Join strings with delimiter
     * @param strings Strings to join
     * @param delimiter Delimiter to use
     * @return Joined string
     */
    static std::string join(const std::vector<std::string>& strings, const std::string& delimiter);

    /**
     * @brief Replace all occurrences of a substring
     * @param str Input string
     * @param from Substring to replace
     * @param to Replacement string
     * @return Modified string
     */
    static std::string replace_all(const std::string& str,
                                  const std::string& from,
                                  const std::string& to);

    /**
     * @brief Check if string starts with prefix
     * @param str String to check
     * @param prefix Prefix to look for
     * @return True if starts with prefix
     */
    static bool starts_with(const std::string& str, const std::string& prefix);

    /**
     * @brief Check if string ends with suffix
     * @param str String to check
     * @param suffix Suffix to look for
     * @return True if ends with suffix
     */
    static bool ends_with(const std::string& str, const std::string& suffix);

    /**
     * @brief Check if string contains substring
     * @param str String to search in
     * @param substr Substring to find
     * @return True if contains substring
     */
    static bool contains(const std::string& str, const std::string& substr);

    /**
     * @brief Convert string to snake_case
     * @param str Input string
     * @return Snake case string
     */
    static std::string to_snake_case(const std::string& str);

    /**
     * @brief Convert string to camelCase
     * @param str Input string
     * @return Camel case string
     */
    static std::string to_camel_case(const std::string& str);

    /**
     * @brief Escape SQL string
     * @param str String to escape
     * @return Escaped string
     */
    static std::string escape_sql(const std::string& str);

    /**
     * @brief Generate random string
     * @param length String length
     * @return Random string
     */
    static std::string random_string(size_t length);
};

/**
 * @brief Date and time utility functions
 */
class DateTimeUtils {
public:
    using time_point = std::chrono::system_clock::time_point;

    /**
     * @brief Parse date string to time_point
     * @param date_str Date string
     * @param format Date format
     * @return Parsed time_point
     */
    static std::optional<time_point> parse_date(const std::string& date_str,
                                               const std::string& format = "%Y-%m-%d");

    /**
     * @brief Format time_point to string
     * @param tp Time point
     * @param format Output format
     * @return Formatted string
     */
    static std::string format_date(const time_point& tp,
                                  const std::string& format = "%Y-%m-%d");

    /**
     * @brief Get current timestamp string
     * @param format Timestamp format
     * @return Current timestamp
     */
    static std::string current_timestamp(const std::string& format = "%Y-%m-%d %H:%M:%S");

    /**
     * @brief Convert between timezones
     * @param tp Time point
     * @param from_tz Source timezone
     * @param to_tz Target timezone
     * @return Converted time point
     */
    static time_point convert_timezone(const time_point& tp,
                                      const std::string& from_tz,
                                      const std::string& to_tz);

    /**
     * @brief Add duration to time_point
     * @param tp Time point
     * @param days Days to add
     * @param hours Hours to add
     * @param minutes Minutes to add
     * @return New time point
     */
    static time_point add_duration(const time_point& tp,
                                  int days = 0,
                                  int hours = 0,
                                  int minutes = 0);

    /**
     * @brief Calculate age from birth date
     * @param birth_date Birth date
     * @param as_of_date Reference date (default: today)
     * @return Age in years
     */
    static int calculate_age(const time_point& birth_date,
                           const time_point& as_of_date = std::chrono::system_clock::now());

    /**
     * @brief Check if date is valid
     * @param year Year
     * @param month Month
     * @param day Day
     * @return True if valid date
     */
    static bool is_valid_date(int year, int month, int day);

    /**
     * @brief Get ISO week number
     * @param tp Time point
     * @return Week number (1-53)
     */
    static int get_iso_week(const time_point& tp);
};

/**
 * @brief File system utility functions
 */
class FileUtils {
public:
    /**
     * @brief Read entire file to string
     * @param filepath File path
     * @return File contents
     */
    static std::optional<std::string> read_file(const std::string& filepath);

    /**
     * @brief Write string to file
     * @param filepath File path
     * @param content Content to write
     * @param append Whether to append
     * @return True if successful
     */
    static bool write_file(const std::string& filepath,
                          const std::string& content,
                          bool append = false);

    /**
     * @brief Check if file exists
     * @param filepath File path
     * @return True if exists
     */
    static bool file_exists(const std::string& filepath);

    /**
     * @brief Get file size
     * @param filepath File path
     * @return File size in bytes
     */
    static std::optional<size_t> file_size(const std::string& filepath);

    /**
     * @brief Get file extension
     * @param filepath File path
     * @return Extension (including dot)
     */
    static std::string get_extension(const std::string& filepath);

    /**
     * @brief Get file name without extension
     * @param filepath File path
     * @return Base filename
     */
    static std::string get_basename(const std::string& filepath);

    /**
     * @brief Get directory path
     * @param filepath File path
     * @return Directory path
     */
    static std::string get_directory(const std::string& filepath);

    /**
     * @brief Create directory (recursive)
     * @param path Directory path
     * @return True if successful
     */
    static bool create_directory(const std::string& path);

    /**
     * @brief List files in directory
     * @param directory Directory path
     * @param pattern Regex pattern filter
     * @param recursive Whether to search recursively
     * @return List of file paths
     */
    static std::vector<std::string> list_files(const std::string& directory,
                                              const std::string& pattern = ".*",
                                              bool recursive = false);

    /**
     * @brief Copy file
     * @param source Source path
     * @param destination Destination path
     * @return True if successful
     */
    static bool copy_file(const std::string& source, const std::string& destination);

    /**
     * @brief Move/rename file
     * @param source Source path
     * @param destination Destination path
     * @return True if successful
     */
    static bool move_file(const std::string& source, const std::string& destination);

    /**
     * @brief Delete file
     * @param filepath File path
     * @return True if successful
     */
    static bool delete_file(const std::string& filepath);

    /**
     * @brief Get temporary directory
     * @return Temporary directory path
     */
    static std::string get_temp_directory();

    /**
     * @brief Create temporary file
     * @param prefix Filename prefix
     * @param extension File extension
     * @return Temporary file path
     */
    static std::string create_temp_file(const std::string& prefix = "tmp",
                                       const std::string& extension = ".tmp");
};

/**
 * @brief System utility functions
 */
class SystemUtils {
public:
    /**
     * @brief Get environment variable
     * @param name Variable name
     * @return Variable value if exists
     */
    static std::optional<std::string> get_env(const std::string& name);

    /**
     * @brief Set environment variable
     * @param name Variable name
     * @param value Variable value
     * @return True if successful
     */
    static bool set_env(const std::string& name, const std::string& value);

    /**
     * @brief Get current working directory
     * @return Current directory path
     */
    static std::string get_current_directory();

    /**
     * @brief Get home directory
     * @return Home directory path
     */
    static std::string get_home_directory();

    /**
     * @brief Get CPU count
     * @return Number of CPU cores
     */
    static unsigned int get_cpu_count();

    /**
     * @brief Get available memory
     * @return Available memory in bytes
     */
    static size_t get_available_memory();

    /**
     * @brief Get process ID
     * @return Current process ID
     */
    static int get_process_id();

    /**
     * @brief Execute command
     * @param command Command to execute
     * @return Command output
     */
    static std::optional<std::string> execute_command(const std::string& command);

    /**
     * @brief Get hostname
     * @return System hostname
     */
    static std::string get_hostname();

    /**
     * @brief Get username
     * @return Current username
     */
    static std::string get_username();
};

/**
 * @brief Cryptographic utility functions
 */
class CryptoUtils {
public:
    /**
     * @brief Calculate MD5 hash
     * @param data Input data
     * @return MD5 hash (hex string)
     */
    static std::string md5(const std::string& data);

    /**
     * @brief Calculate SHA256 hash
     * @param data Input data
     * @return SHA256 hash (hex string)
     */
    static std::string sha256(const std::string& data);

    /**
     * @brief Base64 encode
     * @param data Binary data
     * @return Base64 encoded string
     */
    static std::string base64_encode(const std::vector<uint8_t>& data);

    /**
     * @brief Base64 decode
     * @param encoded Encoded string
     * @return Decoded binary data
     */
    static std::vector<uint8_t> base64_decode(const std::string& encoded);

    /**
     * @brief Generate UUID
     * @return UUID string
     */
    static std::string generate_uuid();

    /**
     * @brief Generate random bytes
     * @param length Number of bytes
     * @return Random bytes
     */
    static std::vector<uint8_t> random_bytes(size_t length);

private:
    static std::mutex random_mutex;
};

/**
 * @brief Validation utility functions
 */
class ValidationUtils {
public:
    /**
     * @brief Validate email address
     * @param email Email address
     * @return True if valid
     */
    static bool is_valid_email(const std::string& email);

    /**
     * @brief Validate URL
     * @param url URL string
     * @return True if valid
     */
    static bool is_valid_url(const std::string& url);

    /**
     * @brief Validate IP address (v4 or v6)
     * @param ip IP address
     * @return True if valid
     */
    static bool is_valid_ip(const std::string& ip);

    /**
     * @brief Validate phone number
     * @param phone Phone number
     * @param country_code Country code
     * @return True if valid
     */
    static bool is_valid_phone(const std::string& phone,
                              const std::string& country_code = "US");

    /**
     * @brief Validate postal code
     * @param postal_code Postal code
     * @param country_code Country code
     * @return True if valid
     */
    static bool is_valid_postal_code(const std::string& postal_code,
                                    const std::string& country_code = "US");

    /**
     * @brief Validate date format
     * @param date_str Date string
     * @param format Expected format
     * @return True if valid
     */
    static bool is_valid_date_format(const std::string& date_str,
                                    const std::string& format);

    /**
     * @brief Validate JSON string
     * @param json JSON string
     * @return True if valid JSON
     */
    static bool is_valid_json(const std::string& json);

    /**
     * @brief Validate SQL identifier
     * @param identifier SQL identifier
     * @return True if valid
     */
    static bool is_valid_sql_identifier(const std::string& identifier);

    /**
     * @brief Check if a string is a valid email
     * @param email Email string to validate
     * @return true if valid email, false otherwise
     */
    static bool isValidEmail(const std::string& email);

    /**
     * @brief Check if a string is a valid phone number
     * @param phone Phone number string to validate
     * @param country_code Country code for validation
     * @return true if valid phone number, false otherwise
     */
    static bool isValidPhoneNumber(const std::string& phone, const std::string& country_code = "US");

    /**
     * @brief Check if a string is a valid postal code
     * @param postal_code Postal code string to validate
     * @param country_code Country code for validation
     * @return true if valid postal code, false otherwise
     */
    static bool isValidPostalCode(const std::string& postal_code, const std::string& country_code = "US");

    /**
     * @brief Check if a string is a valid UUID
     * @param uuid UUID string to validate
     * @return true if valid UUID, false otherwise
     */
    static bool isValidUUID(const std::string& uuid);

    /**
     * @brief Check if a string is a valid URL
     * @param url URL string to validate
     * @return true if valid URL, false otherwise
     */
    static bool isValidURL(const std::string& url);

    /**
     * @brief Sanitize a string by removing potentially harmful content
     * @param input Input string to sanitize
     * @return Sanitized string
     */
    static std::string sanitizeString(const std::string& input);
};

/**
 * @brief Performance monitoring utilities
 */
class PerformanceUtils {
public:
    /**
     * @brief Simple timer class
     */
    class Timer {
    public:
        Timer() : start_(std::chrono::high_resolution_clock::now()) {}

        void reset() {
            start_ = std::chrono::high_resolution_clock::now();
        }

        double elapsed_seconds() const {
            auto end = std::chrono::high_resolution_clock::now();
            return std::chrono::duration<double>(end - start_).count();
        }

        double elapsed_milliseconds() const {
            return elapsed_seconds() * 1000.0;
        }

    private:
        std::chrono::high_resolution_clock::time_point start_;
    };

    /**
     * @brief Memory usage tracker
     */
    class MemoryTracker {
    public:
        MemoryTracker();

        size_t current_usage() const;
        size_t peak_usage() const;
        void reset();
        static size_t global_peak_usage() { return global_peak_usage_.load(); }

    private:
        size_t initial_usage_;
        mutable size_t peak_usage_;
        static std::atomic<size_t> global_peak_usage_;
    };

    /**
     * @brief Format bytes to human readable string
     * @param bytes Number of bytes
     * @return Formatted string (e.g., "1.5 GB")
     */
    static std::string format_bytes(size_t bytes);

    /**
     * @brief Format duration to human readable string
     * @param seconds Duration in seconds
     * @return Formatted string (e.g., "2h 15m 30s")
     */
    static std::string format_duration(double seconds);

    /**
     * @brief Calculate throughput
     * @param items Number of items processed
     * @param seconds Time taken
     * @return Items per second
     */
    static double calculate_throughput(size_t items, double seconds);
};

/**
 * @brief Processing-related utility functions
 */
class ProcessingUtils {
public:
    /**
     * @brief Convert processing stage enum to string
     * @param stage Processing stage enum value
     * @return String representation of the stage
     */
    static std::string stage_name(int stage);
};

} // namespace omop::common

File src/lib/common/configuration.h:

#pragma once

#include "exceptions.h"

#include <string>
#include <string_view>
#include <memory>
#include <unordered_map>
#include <vector>
#include <optional>
#include <variant>
#include <yaml-cpp/yaml.h>
#include <mutex>

namespace omop::common {

/**
 * @brief Represents a single transformation rule in the ETL pipeline
 *
 * This class encapsulates the mapping rules from source columns to target columns,
 * including the transformation type and any additional parameters needed for the
 * transformation process.
 */
class TransformationRule {
public:
    /**
     * @brief Enumeration of supported transformation types
     */
    enum class Type {
        Direct,              ///< Direct column mapping without transformation
        DateTransform,       ///< Date format transformation
        VocabularyMapping,   ///< Map using vocabulary lookup
        DateCalculation,     ///< Calculate date based on multiple fields
        NumericTransform,    ///< Numeric value transformation
        StringConcatenation, ///< Concatenate multiple string fields
        Conditional,         ///< Conditional transformation based on rules
        Custom              ///< Custom transformation logic
    };

    /**
     * @brief Default constructor
     */
    TransformationRule() = default;

    /**
     * @brief Construct from YAML node
     * @param node YAML node containing transformation configuration
     */
    explicit TransformationRule(const YAML::Node& node);

    /**
     * @brief Get source column name
     * @return std::string_view Source column name
     */
    [[nodiscard]] std::string_view source_column() const noexcept { return source_column_; }

    /**
     * @brief Get source columns for multi-column transformations
     * @return const std::vector<std::string>& Vector of source column names
     */
    [[nodiscard]] const std::vector<std::string>& source_columns() const noexcept {
        return source_columns_;
    }

    /**
     * @brief Get target column name
     * @return std::string_view Target column name
     */
    [[nodiscard]] std::string_view target_column() const noexcept { return target_column_; }

    /**
     * @brief Get transformation type
     * @return Type The transformation type
     */
    [[nodiscard]] Type type() const noexcept { return type_; }

    /**
     * @brief Get transformation parameters
     * @return const YAML::Node& Additional parameters for the transformation
     */
    [[nodiscard]] const YAML::Node& parameters() const noexcept { return parameters_; }

    /**
     * @brief Check if this is a multi-column transformation
     * @return bool True if multiple source columns are involved
     */
    [[nodiscard]] bool is_multi_column() const noexcept { return !source_columns_.empty(); }

private:
    std::string source_column_;              ///< Single source column name
    std::vector<std::string> source_columns_; ///< Multiple source columns
    std::string target_column_;              ///< Target column name
    Type type_{Type::Direct};                ///< Transformation type
    YAML::Node parameters_;                  ///< Additional parameters
};

/**
 * @brief Configuration for a single table mapping
 *
 * This class represents the complete mapping configuration for transforming
 * data from a source table to an OMOP CDM target table.
 */
class TableMapping {
public:
    /**
     * @brief Default constructor
     */
    TableMapping() = default;

    /**
     * @brief Construct from YAML node
     * @param node YAML node containing table mapping configuration
     */
    explicit TableMapping(const YAML::Node& node);

    /**
     * @brief Get source table name
     * @return std::string_view Source table name
     */
    [[nodiscard]] std::string_view source_table() const noexcept { return source_table_; }

    /**
     * @brief Get target table name
     * @return std::string_view Target table name
     */
    [[nodiscard]] std::string_view target_table() const noexcept { return target_table_; }

    /**
     * @brief Get transformation rules
     * @return const std::vector<TransformationRule>& Vector of transformation rules
     */
    [[nodiscard]] const std::vector<TransformationRule>& transformations() const noexcept {
        return transformations_;
    }

    /**
     * @brief Get pre-processing SQL query
     * @return std::optional<std::string> Optional pre-processing SQL
     */
    [[nodiscard]] const std::optional<std::string>& pre_process_sql() const noexcept {
        return pre_process_sql_;
    }

    /**
     * @brief Get post-processing SQL query
     * @return std::optional<std::string> Optional post-processing SQL
     */
    [[nodiscard]] const std::optional<std::string>& post_process_sql() const noexcept {
        return post_process_sql_;
    }

    /**
     * @brief Get filter conditions
     * @return const YAML::Node& Filter conditions as YAML node
     */
    [[nodiscard]] const YAML::Node& filters() const noexcept { return filters_; }

    /**
     * @brief Get validation rules
     * @return const YAML::Node& Validation rules as YAML node
     */
    [[nodiscard]] const YAML::Node& validations() const noexcept { return validations_; }

private:
    std::string source_table_;                    ///< Source table name
    std::string target_table_;                    ///< Target table name
    std::vector<TransformationRule> transformations_; ///< Transformation rules
    std::optional<std::string> pre_process_sql_;  ///< Pre-processing SQL
    std::optional<std::string> post_process_sql_; ///< Post-processing SQL
    YAML::Node filters_;                          ///< Filter conditions
    YAML::Node validations_;                      ///< Validation rules
};

/**
 * @brief Database connection configuration
 */
class DatabaseConfig {
public:
    /**
     * @brief Database types supported
     */
    enum class Type {
        PostgreSQL,
        MySQL,
        MSSQL,
        Oracle
    };

    /**
     * @brief Default constructor
     */
    DatabaseConfig() = default;

    /**
     * @brief Construct from YAML node
     * @param node YAML node containing database configuration
     */
    explicit DatabaseConfig(const YAML::Node& node);

    /**
     * @brief Get database type
     * @return Type Database type
     */
    [[nodiscard]] Type type() const noexcept { return type_; }

    /**
     * @brief Get connection string
     * @return std::string_view Connection string
     */
    [[nodiscard]] std::string_view connection_string() const noexcept {
        return connection_string_;
    }

    /**
     * @brief Get host name
     * @return std::string_view Host name
     */
    [[nodiscard]] std::string_view host() const noexcept { return host_; }

    /**
     * @brief Get port number
     * @return int Port number
     */
    [[nodiscard]] int port() const noexcept { return port_; }

    /**
     * @brief Get database name
     * @return std::string_view Database name
     */
    [[nodiscard]] std::string_view database() const noexcept { return database_; }

    /**
     * @brief Get username
     * @return std::string_view Username
     */
    [[nodiscard]] std::string_view username() const noexcept { return username_; }

    /**
     * @brief Get password
     * @return std::string_view Password
     */
    [[nodiscard]] std::string_view password() const noexcept { return password_; }

    /**
     * @brief Get additional connection parameters
     * @return const std::unordered_map<std::string, std::string>& Additional parameters
     */
    [[nodiscard]] const std::unordered_map<std::string, std::string>& parameters() const noexcept {
        return parameters_;
    }

private:
    Type type_{Type::PostgreSQL};
    std::string connection_string_;
    std::string host_;
    int port_{5432};
    std::string database_;
    std::string username_;
    std::string password_;
    std::unordered_map<std::string, std::string> parameters_;
};

/**
 * @brief Main configuration manager for the ETL pipeline
 *
 * This class manages all configuration aspects of the OMOP ETL pipeline,
 * including loading configuration files, managing table mappings, and
 * providing access to various configuration parameters.
 */
class ConfigurationManager {
public:
    /**
     * @brief Construct a new ConfigurationManager
     */
    ConfigurationManager() = default;

    /**
     * @brief Load configuration from a YAML file
     * @param filepath Path to the YAML configuration file
     * @throws ConfigurationException if the file cannot be loaded or parsed
     */
    void load_config(const std::string& filepath);

    /**
     * @brief Load configuration from a YAML string
     * @param yaml_content YAML content as string
     * @throws ConfigurationException if the content cannot be parsed
     */
    void load_config_from_string(const std::string& yaml_content);

    /**
     * @brief Get table mapping by name
     * @param table_name Name of the table
     * @return std::optional<TableMapping> Table mapping if found
     */
    [[nodiscard]] std::optional<TableMapping> get_table_mapping(
        const std::string& table_name) const;

    /**
     * @brief Get all table mappings
     * @return const std::unordered_map<std::string, TableMapping>& All table mappings
     */
    [[nodiscard]] const std::unordered_map<std::string, TableMapping>&
        get_all_mappings() const noexcept {
        return table_mappings_;
    }

    /**
     * @brief Get source database configuration
     * @return const DatabaseConfig& Source database configuration
     */
    [[nodiscard]] const DatabaseConfig& get_source_db() const noexcept {
        return source_db_;
    }

    /**
     * @brief Get target database configuration
     * @return const DatabaseConfig& Target database configuration
     */
    [[nodiscard]] const DatabaseConfig& get_target_db() const noexcept {
        return target_db_;
    }

    /**
     * @brief Get configuration value by key
     * @param key Configuration key in dot notation (e.g., "etl.batch_size")
     * @return std::optional<YAML::Node> Configuration value if found
     */
    [[nodiscard]] std::optional<YAML::Node> get_value(const std::string& key) const;

    /**
     * @brief Get configuration value with default
     * @tparam T Type of the value
     * @param key Configuration key
     * @param default_value Default value if key not found
     * @return T Configuration value or default
     */
    template<typename T>
    [[nodiscard]] T get_value_or(const std::string& key, const T& default_value) const {
        auto value = get_value(key);
        if (value && value->IsDefined()) {
            try {
                return value->as<T>();
            } catch (const YAML::Exception&) {
                return default_value;
            }
        }
        return default_value;
    }

    /**
     * @brief Validate configuration
     * @throws ConfigurationException if configuration is invalid
     */
    void validate_config() const;

    /**
     * @brief Check if configuration is loaded
     * @return bool True if configuration is loaded
     */
    [[nodiscard]] bool is_loaded() const noexcept { return config_loaded_; }

    /**
     * @brief Get vocabulary mappings
     * @return const YAML::Node& Vocabulary mappings configuration
     */
    [[nodiscard]] const YAML::Node& get_vocabulary_mappings() const noexcept {
        return vocabulary_mappings_;
    }

    /**
     * @brief Get ETL settings
     * @return const YAML::Node& ETL settings configuration
     */
    [[nodiscard]] const YAML::Node& get_etl_settings() const noexcept {
        return etl_settings_;
    }
    
    /**
     * @brief Clear all configuration data
     */
    void clear();
    
    /**
     * @brief Get the path of the loaded configuration file
     * @return std::string Path to configuration file (empty if loaded from string)
     */
    [[nodiscard]] std::string get_config_file_path() const noexcept;
    
    /**
     * @brief Get the time when configuration was loaded
     * @return std::chrono::system_clock::time_point Load timestamp
     */
    [[nodiscard]] std::chrono::system_clock::time_point get_load_time() const noexcept;
    
    /**
     * @brief Reload configuration from file
     * @throws ConfigurationException if no file was previously loaded or reload fails
     */
    void reload();

private:
    /**
     * @brief Parse table mappings from configuration
     * @param mappings_node YAML node containing table mappings
     */
    void parse_table_mappings(const YAML::Node& mappings_node);

    /**
     * @brief Parse database configuration
     * @param db_node YAML node containing database configuration
     * @return DatabaseConfig Parsed database configuration
     */
    DatabaseConfig parse_database_config(const YAML::Node& db_node);

    mutable std::mutex config_mutex_;
    bool config_loaded_{false};
    YAML::Node root_config_;
    std::unordered_map<std::string, TableMapping> table_mappings_;
    DatabaseConfig source_db_;
    DatabaseConfig target_db_;
    YAML::Node vocabulary_mappings_;
    YAML::Node etl_settings_;
    std::string config_file_path_;
    std::chrono::system_clock::time_point load_time_;
};

/**
 * @brief Singleton accessor for global configuration
 *
 * Provides global access to the configuration manager instance.
 * This follows the singleton pattern to ensure consistent configuration
 * across the entire application.
 */
class Config {
public:
    /**
     * @brief Get the singleton instance
     * @return ConfigurationManager& Reference to the configuration manager
     */
    [[nodiscard]] static ConfigurationManager& instance() {
        std::call_once(init_flag_, []() {
            instance_.reset(new ConfigurationManager());
        });
        return *instance_;
    }

    Config() = delete;
    Config(const Config&) = delete;
    Config& operator=(const Config&) = delete;

private:
    static std::unique_ptr<ConfigurationManager> instance_;
    static std::once_flag init_flag_;
    static std::mutex instance_mutex_;
};

} // namespace omop::common

File src/lib/common/logging.h:

/**
 * @file logging.h
 * @brief Logging utilities for OMOP ETL pipeline
 * <AUTHOR> ETL Team
 * @date 2024
 */

#pragma once

#include <string>
#include <memory>
#include <chrono>
#include <unordered_map>
#include <vector>
#include <functional>
#include <mutex>
#include <optional>
#include <any>
#include <spdlog/spdlog.h>
#include <spdlog/sinks/sink.h>
#include <spdlog/fmt/ostr.h>

namespace omop::common {

/**
 * @brief Log level enumeration
 */
enum class LogLevel {
    Trace,
    Debug,
    Info,
    Warning,
    Error,
    Critical
};

/**
 * @brief Structured log entry
 *
 * Represents a single log entry with structured data for better analysis.
 */
struct LogEntry {
    std::chrono::system_clock::time_point timestamp;
    LogLevel level;
    std::string logger_name;
    std::string message;
    std::string thread_id;
    std::string job_id;
    std::string component;
    std::string operation;
    std::unordered_map<std::string, std::any> context;
    std::optional<std::string> error_code;
    std::optional<std::string> stack_trace;
};

/**
 * @brief Log formatter interface
 */
class ILogFormatter {
public:
    virtual ~ILogFormatter() = default;

    /**
     * @brief Format log entry
     * @param entry Log entry
     * @return std::string Formatted log message
     */
    virtual std::string format(const LogEntry& entry) = 0;
};

/**
 * @brief JSON log formatter
 *
 * Formats log entries as JSON for structured logging.
 */
class JsonLogFormatter : public ILogFormatter {
public:
    /**
     * @brief Format log entry as JSON
     * @param entry Log entry
     * @return std::string JSON formatted log
     */
    std::string format(const LogEntry& entry) override;

    /**
     * @brief Set whether to pretty print JSON
     * @param pretty Whether to pretty print
     */
    void set_pretty_print(bool pretty) { pretty_print_ = pretty; }

private:
    bool pretty_print_{false};
};

/**
 * @brief Plain text log formatter
 *
 * Formats log entries as human-readable text.
 */
class TextLogFormatter : public ILogFormatter {
public:
    /**
     * @brief Constructor
     * @param pattern Log pattern (default: "[%Y-%m-%d %H:%M:%S.%e] [%l] [%n] %v")
     */
    explicit TextLogFormatter(const std::string& pattern =
        "[%Y-%m-%d %H:%M:%S.%e] [%l] [%n] %v");

    /**
     * @brief Format log entry as text
     * @param entry Log entry
     * @return std::string Text formatted log
     */
    std::string format(const LogEntry& entry) override;

private:
    std::string pattern_;
};

/**
 * @brief Log sink interface
 */
class ILogSink {
public:
    virtual ~ILogSink() = default;

    /**
     * @brief Write log entry
     * @param entry Log entry
     */
    virtual void write(const LogEntry& entry) = 0;

    /**
     * @brief Flush buffered logs
     */
    virtual void flush() = 0;

    /**
     * @brief Set formatter
     * @param formatter Log formatter
     */
    void set_formatter(std::unique_ptr<ILogFormatter> formatter) {
        formatter_ = std::move(formatter);
    }

protected:
    std::unique_ptr<ILogFormatter> formatter_;
};

/**
 * @brief Abstract database log sink interface
 *
 * Interface for writing log entries to a database. Concrete implementations
 * should be provided by modules that have database access.
 */
class IDatabaseLogSink : public ILogSink {
public:
    virtual ~IDatabaseLogSink() = default;

    /**
     * @brief Create log table if not exists
     */
    virtual void create_table_if_not_exists() = 0;

protected:
    // Concrete implementations will handle database-specific details
    std::string table_name_;
    std::vector<LogEntry> buffer_;
    std::mutex buffer_mutex_;
};

/**
 * @brief ETL-specific logger
 *
 * Provides logging functionality tailored for ETL operations with
 * support for job tracking, component identification, and metrics.
 */
class Logger {
    friend class LoggingConfig;
public:
    /**
     * @brief Get logger instance
     * @param name Logger name
     * @return std::shared_ptr<Logger> Logger instance
     */
    static std::shared_ptr<Logger> get(const std::string& name);

    /**
     * @brief Constructor
     * @param name Logger name
     */
    explicit Logger(const std::string& name);

    /**
     * @brief Set log level
     * @param level Log level
     */
    void set_level(LogLevel level);

    /**
     * @brief Add log sink
     * @param sink Log sink
     */
    void add_sink(std::shared_ptr<ILogSink> sink);

    /**
     * @brief Clear all sinks
     */
    void clear_sinks() { 
        std::lock_guard<std::mutex> lock(sinks_mutex_);
        sinks_.clear(); 
    }

    /**
     * @brief Set job context
     * @param job_id Job identifier
     */
    void set_job_id(const std::string& job_id) { 
        std::lock_guard<std::mutex> lock(context_mutex_);
        job_id_ = job_id; 
    }

    /**
     * @brief Set component context
     * @param component Component name
     */
    void set_component(const std::string& component) { 
        std::lock_guard<std::mutex> lock(context_mutex_);
        component_ = component; 
    }

    // Logging methods
    template<typename... Args>
    void trace(const std::string& msg, Args&&... args) {
        log(LogLevel::Trace, msg, std::forward<Args>(args)...);
    }

    template<typename... Args>
    void debug(const std::string& msg, Args&&... args) {
        log(LogLevel::Debug, msg, std::forward<Args>(args)...);
    }

    template<typename... Args>
    void info(const std::string& msg, Args&&... args) {
        log(LogLevel::Info, msg, std::forward<Args>(args)...);
    }

    template<typename... Args>
    void warn(const std::string& msg, Args&&... args) {
        log(LogLevel::Warning, msg, std::forward<Args>(args)...);
    }

    template<typename... Args>
    void error(const std::string& msg, Args&&... args) {
        log(LogLevel::Error, msg, std::forward<Args>(args)...);
    }

    template<typename... Args>
    void critical(const std::string& msg, Args&&... args) {
        log(LogLevel::Critical, msg, std::forward<Args>(args)...);
    }

    /**
     * @brief Log with structured context
     * @param level Log level
     * @param msg Message
     * @param context Additional context
     */
    void log_structured(LogLevel level, const std::string& msg,
                       const std::unordered_map<std::string, std::any>& context);

    /**
     * @brief Log ETL operation
     * @param operation Operation name
     * @param status Operation status
     * @param details Operation details
     */
    void log_operation(const std::string& operation,
                      const std::string& status,
                      const std::unordered_map<std::string, std::any>& details = {});

    /**
     * @brief Log ETL metrics
     * @param metrics Metrics map
     */
    void log_metrics(const std::unordered_map<std::string, double>& metrics);

    /**
     * @brief Log exception
     * @param e Exception
     * @param context Additional context
     */
    void log_exception(const std::exception& e,
                      const std::unordered_map<std::string, std::any>& context = {});

private:
    template<typename... Args>
    void log(LogLevel level, const std::string& format, Args&&... args) {
        if (level < min_level_) return;

        auto formatted = fmt::format(fmt::runtime(format), std::forward<Args>(args)...);
        LogEntry entry{
            .timestamp = std::chrono::system_clock::now(),
            .level = level,
            .logger_name = name_,
            .message = formatted,
            .thread_id = get_thread_id(),
            .job_id = job_id_,
            .component = component_,
            .operation = "",
            .context = {},
            .error_code = std::nullopt,
            .stack_trace = std::nullopt
        };

        write_entry(entry);
    }

    void write_entry(const LogEntry& entry);
    std::string get_thread_id() const;
    void initialize_logger();

    std::string name_;
    mutable std::mutex context_mutex_;
    std::string job_id_;               // Protected by context_mutex_
    std::string component_;             // Protected by context_mutex_
    std::atomic<LogLevel> min_level_{LogLevel::Info};
    
    mutable std::mutex sinks_mutex_;
    std::vector<std::shared_ptr<ILogSink>> sinks_;
    std::shared_ptr<spdlog::logger> spdlog_logger_;

    static std::unordered_map<std::string, std::shared_ptr<Logger>> loggers_;
    static std::mutex loggers_mutex_;
};

/**
 * @brief Performance logger
 *
 * Specialized logger for tracking performance metrics and timings.
 */
class PerformanceLogger {
public:
    /**
     * @brief Constructor
     * @param logger Base logger
     */
    explicit PerformanceLogger(std::shared_ptr<Logger> logger)
        : logger_(logger) {}

    /**
     * @brief Start timing an operation
     * @param operation_id Operation identifier
     */
    void start_timing(const std::string& operation_id);

    /**
     * @brief End timing an operation
     * @param operation_id Operation identifier
     * @param record_count Records processed (optional)
     */
    void end_timing(const std::string& operation_id,
                   std::optional<size_t> record_count = std::nullopt);

    /**
     * @brief Log throughput
     * @param operation Operation name
     * @param records_per_second Records per second
     */
    void log_throughput(const std::string& operation, double records_per_second);

    /**
     * @brief Log resource usage
     * @param cpu_percent CPU usage percentage
     * @param memory_mb Memory usage in MB
     * @param disk_io_mb Disk I/O in MB
     */
    void log_resource_usage(double cpu_percent, double memory_mb, double disk_io_mb);

    /**
     * @brief RAII timer for automatic timing
     */
    class ScopedTimer {
    public:
        ScopedTimer(PerformanceLogger& logger, const std::string& operation_id)
            : logger_(logger), operation_id_(operation_id) {
            logger_.start_timing(operation_id_);
        }

        ~ScopedTimer() {
            if (record_count_.has_value()) {
                logger_.end_timing(operation_id_, record_count_);
            } else {
                logger_.end_timing(operation_id_);
            }
        }

        void set_record_count(size_t count) { record_count_ = count; }

    private:
        PerformanceLogger& logger_;
        std::string operation_id_;
        std::optional<size_t> record_count_;
    };

    /**
     * @brief Create scoped timer
     * @param operation_id Operation identifier
     * @return ScopedTimer Timer object
     */
    [[nodiscard]] ScopedTimer scoped_timer(const std::string& operation_id) {
        return ScopedTimer(*this, operation_id);
    }

private:
    std::shared_ptr<Logger> logger_;
    std::unordered_map<std::string, std::chrono::steady_clock::time_point> timings_;
    std::mutex timings_mutex_;
};

/**
 * @brief Audit logger
 *
 * Specialized logger for audit trail and compliance logging.
 */
class AuditLogger {
public:
    /**
     * @brief Constructor
     * @param logger Base logger
     */
    explicit AuditLogger(std::shared_ptr<Logger> logger)
        : logger_(logger) {}

    /**
     * @brief Log data access
     * @param table_name Table accessed
     * @param operation Operation type (read/write)
     * @param record_count Number of records
     * @param user User identifier
     */
    void log_data_access(const std::string& table_name,
                        const std::string& operation,
                        size_t record_count,
                        const std::string& user);

    /**
     * @brief Log configuration change
     * @param config_item Configuration item
     * @param old_value Old value
     * @param new_value New value
     * @param user User making the change
     */
    void log_config_change(const std::string& config_item,
                          const std::string& old_value,
                          const std::string& new_value,
                          const std::string& user);

    /**
     * @brief Log security event
     * @param event_type Event type
     * @param details Event details
     */
    void log_security_event(const std::string& event_type,
                           const std::unordered_map<std::string, std::string>& details);

private:
    std::shared_ptr<Logger> logger_;
};

/**
 * @brief Global logging configuration
 */
class LoggingConfig {
public:
    /**
     * @brief Initialize logging system
     * @param config_file Configuration file path
     */
    static void initialize(const std::string& config_file);

    /**
     * @brief Initialize with default configuration
     */
    static void initialize_default();

    /**
     * @brief Set global log level
     * @param level Log level
     */
    static void set_global_level(LogLevel level);

    /**
     * @brief Add global sink
     * @param sink Log sink
     */
    static void add_global_sink(std::shared_ptr<ILogSink> sink);

    /**
     * @brief Flush all loggers
     */
    static void flush_all();

    /**
     * @brief Shutdown logging system
     */
    static void shutdown();
};

// Convenience macros
#define LOG_TRACE(logger, ...) (logger)->trace(__VA_ARGS__)
#define LOG_DEBUG(logger, ...) (logger)->debug(__VA_ARGS__)
#define LOG_INFO(logger, ...) (logger)->info(__VA_ARGS__)
#define LOG_WARN(logger, ...) (logger)->warn(__VA_ARGS__)
#define LOG_ERROR(logger, ...) (logger)->error(__VA_ARGS__)
#define LOG_CRITICAL(logger, ...) (logger)->critical(__VA_ARGS__)

} // namespace omop::common

File src/lib/common/utilities.cpp:

/**
 * @file utilities.cpp
 * @brief Implementation of utility functions for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "utilities.h"
#include "core/interfaces.h"
#include <any>

#include <fstream>
#include <random>
#include <cctype>
#include <ctime>
#include <iomanip>
#include <mutex>
#include <openssl/md5.h>
#include <openssl/sha.h>
#include <openssl/evp.h>
#include <openssl/rand.h>
#include <openssl/bio.h>
#include <openssl/buffer.h>
#include <uuid/uuid.h>
#include <nlohmann/json.hpp>
#include <cstdlib>
#include <unistd.h>
#include <sys/types.h>
#include <pwd.h>
#include <thread>
#include <array>
#include <memory>

#ifdef _WIN32
#include <windows.h>
#include <psapi.h>
#elif defined(__linux__)
#include <sys/resource.h>
#include <sys/sysinfo.h>
#elif defined(__APPLE__)
#include <sys/resource.h>
#include <sys/types.h>
#include <sys/sysctl.h>
#include <mach/mach.h>
#endif

namespace omop::common {

// Thread-local random generator for better performance and thread safety
thread_local std::mt19937 tl_random_gen{std::random_device{}()};

std::string any_to_string(const std::any& value) {
    if (!value.has_value()) {
        return "";
    }

    const std::type_info& type = value.type();

    try {
        if (type == typeid(std::string)) {
            return std::any_cast<std::string>(value);
        } else if (type == typeid(int)) {
            return std::to_string(std::any_cast<int>(value));
        } else if (type == typeid(long)) {
            return std::to_string(std::any_cast<long>(value));
        } else if (type == typeid(long long)) {
            return std::to_string(std::any_cast<long long>(value));
        } else if (type == typeid(unsigned int)) {
            return std::to_string(std::any_cast<unsigned int>(value));
        } else if (type == typeid(unsigned long)) {
            return std::to_string(std::any_cast<unsigned long>(value));
        } else if (type == typeid(unsigned long long)) {
            return std::to_string(std::any_cast<unsigned long long>(value));
        } else if (type == typeid(float)) {
            return std::to_string(std::any_cast<float>(value));
        } else if (type == typeid(double)) {
            return std::to_string(std::any_cast<double>(value));
        } else if (type == typeid(bool)) {
            return std::any_cast<bool>(value) ? "true" : "false";
        } else if (type == typeid(char)) {
            return std::string(1, std::any_cast<char>(value));
        } else {
            // For unknown types, return type name
            return std::string("<") + type.name() + ">";
        }
    } catch (const std::bad_any_cast& e) {
        return std::string("<bad_cast: ") + type.name() + ">";
    }
}

// StringUtils implementation
std::string StringUtils::trim(const std::string& str) {
    size_t first = str.find_first_not_of(" \t\n\r\f\v");
    if (first == std::string::npos) {
        return "";
    }
    size_t last = str.find_last_not_of(" \t\n\r\f\v");
    return str.substr(first, (last - first + 1));
}

std::string StringUtils::to_lower(const std::string& str) {
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(),
                   [](unsigned char c) { return std::tolower(c); });
    return result;
}

std::string StringUtils::to_upper(const std::string& str) {
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(),
                   [](unsigned char c) { return std::toupper(c); });
    return result;
}

std::vector<std::string> StringUtils::split(const std::string& str, char delimiter) {
    std::vector<std::string> tokens;
    std::stringstream ss(str);
    std::string token;

    while (std::getline(ss, token, delimiter)) {
        if (!token.empty()) {
            tokens.push_back(token);
        }
    }

    return tokens;
}

std::vector<std::string> StringUtils::split(const std::string& str, const std::string& delimiter) {
    std::vector<std::string> tokens;
    size_t start = 0;
    size_t end = str.find(delimiter);

    while (end != std::string::npos) {
        tokens.push_back(str.substr(start, end - start));
        start = end + delimiter.length();
        end = str.find(delimiter, start);
    }

    tokens.push_back(str.substr(start));
    return tokens;
}

std::string StringUtils::join(const std::vector<std::string>& strings, const std::string& delimiter) {
    if (strings.empty()) {
        return "";
    }

    std::stringstream ss;
    for (size_t i = 0; i < strings.size(); ++i) {
        if (i > 0) {
            ss << delimiter;
        }
        ss << strings[i];
    }

    return ss.str();
}

std::string StringUtils::replace_all(const std::string& str,
                                    const std::string& from,
                                    const std::string& to) {
    if (from.empty()) {
        return str;
    }

    std::string result = str;
    size_t start_pos = 0;

    while ((start_pos = result.find(from, start_pos)) != std::string::npos) {
        result.replace(start_pos, from.length(), to);
        start_pos += to.length();
    }

    return result;
}

bool StringUtils::starts_with(const std::string& str, const std::string& prefix) {
    return str.size() >= prefix.size() &&
           str.compare(0, prefix.size(), prefix) == 0;
}

bool StringUtils::ends_with(const std::string& str, const std::string& suffix) {
    return str.size() >= suffix.size() &&
           str.compare(str.size() - suffix.size(), suffix.size(), suffix) == 0;
}

bool StringUtils::contains(const std::string& str, const std::string& substr) {
    return str.find(substr) != std::string::npos;
}

std::string StringUtils::to_snake_case(const std::string& str) {
    std::string result;
    for (size_t i = 0; i < str.length(); ++i) {
        if (i > 0 && std::isupper(str[i]) && std::islower(str[i-1])) {
            result += '_';
        }
        result += std::tolower(str[i]);
    }
    return result;
}

std::string StringUtils::to_camel_case(const std::string& str) {
    std::string result;
    bool capitalize_next = false;

    for (char c : str) {
        if (c == '_' || c == ' ' || c == '-') {
            capitalize_next = true;
        } else {
            if (capitalize_next) {
                result += std::toupper(c);
                capitalize_next = false;
            } else {
                result += std::tolower(c);
            }
        }
    }

    return result;
}

std::string StringUtils::escape_sql(const std::string& str) {
    std::string result;
    result.reserve(str.length() * 2);

    for (char c : str) {
        switch (c) {
            case '\'': result += "''"; break;
            case '"': result += "\\\""; break;
            case '\\': result += "\\\\"; break;
            case '\n': result += "\\n"; break;
            case '\r': result += "\\r"; break;
            case '\t': result += "\\t"; break;
            case '\0': result += "\\0"; break;
            default: result += c; break;
        }
    }

    return result;
}

std::string StringUtils::random_string(size_t length) {
    static const char alphanum[] =
        "0123456789"
        "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
        "abcdefghijklmnopqrstuvwxyz";

    std::uniform_int_distribution<> dis(0, sizeof(alphanum) - 2);

    std::string result;
    result.reserve(length);

    for (size_t i = 0; i < length; ++i) {
        result += alphanum[dis(tl_random_gen)];
    }

    return result;
}

// DateTimeUtils implementation
// Thread-safe time conversion using mutex
static std::mutex time_conversion_mutex;

std::optional<DateTimeUtils::time_point> DateTimeUtils::parse_date(
    const std::string& date_str, const std::string& format) {

    std::tm tm = {};
    std::istringstream ss(date_str);
    
    // Lock for thread-safe time parsing
    {
        std::lock_guard<std::mutex> lock(time_conversion_mutex);
        ss >> std::get_time(&tm, format.c_str());
    }

    if (ss.fail()) {
        return std::nullopt;
    }

    // Use mutex for mktime which is not thread-safe
    time_t time_value;
    {
        std::lock_guard<std::mutex> lock(time_conversion_mutex);
        time_value = std::mktime(&tm);
    }
    
    if (time_value == -1) {
        return std::nullopt;
    }
    
    return std::chrono::system_clock::from_time_t(time_value);
}

std::string DateTimeUtils::format_date(const time_point& tp, const std::string& format) {
    auto time_t = std::chrono::system_clock::to_time_t(tp);
    std::stringstream ss;
    
    // Thread-safe localtime
    std::tm tm_result;
    {
        std::lock_guard<std::mutex> lock(time_conversion_mutex);
#ifdef _WIN32
        localtime_s(&tm_result, &time_t);
#else
        localtime_r(&time_t, &tm_result);
#endif
    }
    
    ss << std::put_time(&tm_result, format.c_str());
    return ss.str();
}

std::string DateTimeUtils::current_timestamp(const std::string& format) {
    return format_date(std::chrono::system_clock::now(), format);
}

DateTimeUtils::time_point DateTimeUtils::convert_timezone(
    const time_point& tp, const std::string& from_tz, const std::string& to_tz) {
    // Simplified implementation - in production, use a proper timezone library
    // This is a placeholder that doesn't actually convert timezones
    return tp;
}

DateTimeUtils::time_point DateTimeUtils::add_duration(
    const time_point& tp, int days, int hours, int minutes) {

    auto duration = std::chrono::days(days) +
                   std::chrono::hours(hours) +
                   std::chrono::minutes(minutes);
    return tp + duration;
}

int DateTimeUtils::calculate_age(const time_point& birth_date, const time_point& as_of_date) {
    auto birth_time_t = std::chrono::system_clock::to_time_t(birth_date);
    auto as_of_time_t = std::chrono::system_clock::to_time_t(as_of_date);

    std::tm birth_tm = *std::localtime(&birth_time_t);
    std::tm as_of_tm = *std::localtime(&as_of_time_t);

    int age = as_of_tm.tm_year - birth_tm.tm_year;

    if (as_of_tm.tm_mon < birth_tm.tm_mon ||
        (as_of_tm.tm_mon == birth_tm.tm_mon && as_of_tm.tm_mday < birth_tm.tm_mday)) {
        age--;
    }

    return age;
}

bool DateTimeUtils::is_valid_date(int year, int month, int day) {
    if (month < 1 || month > 12) return false;
    if (day < 1) return false;

    static const int days_in_month[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};

    int max_day = days_in_month[month - 1];

    // Check for leap year
    if (month == 2 && ((year % 4 == 0 && year % 100 != 0) || year % 400 == 0)) {
        max_day = 29;
    }

    return day <= max_day;
}

int DateTimeUtils::get_iso_week(const time_point& tp) {
    auto time_t = std::chrono::system_clock::to_time_t(tp);
    std::tm tm = *std::localtime(&time_t);

    // Simplified ISO week calculation
    // In production, use a proper date library
    std::tm jan1 = tm;
    jan1.tm_mon = 0;
    jan1.tm_mday = 1;
    std::mktime(&jan1);

    int days_since_jan1 = tm.tm_yday;
    int jan1_weekday = jan1.tm_wday;

    // Adjust for ISO week (Monday = 1, Sunday = 7)
    if (jan1_weekday == 0) jan1_weekday = 7;

    int iso_week = (days_since_jan1 + jan1_weekday - 1) / 7 + 1;

    return iso_week;
}

// FileUtils implementation
std::optional<std::string> FileUtils::read_file(const std::string& filepath) {
    std::ifstream file(filepath, std::ios::binary);
    if (!file.is_open()) {
        return std::nullopt;
    }

    std::string content((std::istreambuf_iterator<char>(file)),
                       std::istreambuf_iterator<char>());
    return content;
}

bool FileUtils::write_file(const std::string& filepath,
                          const std::string& content,
                          bool append) {
    std::ios_base::openmode mode = std::ios::out;
    if (append) {
        mode |= std::ios::app;
    }

    std::ofstream file(filepath, mode);
    if (!file.is_open()) {
        return false;
    }

    file << content;
    return file.good();
}

bool FileUtils::file_exists(const std::string& filepath) {
    return std::filesystem::exists(filepath) &&
           std::filesystem::is_regular_file(filepath);
}

std::optional<size_t> FileUtils::file_size(const std::string& filepath) {
    try {
        return std::filesystem::file_size(filepath);
    } catch (...) {
        return std::nullopt;
    }
}

std::string FileUtils::get_extension(const std::string& filepath) {
    return std::filesystem::path(filepath).extension().string();
}

std::string FileUtils::get_basename(const std::string& filepath) {
    return std::filesystem::path(filepath).stem().string();
}

std::string FileUtils::get_directory(const std::string& filepath) {
    return std::filesystem::path(filepath).parent_path().string();
}

bool FileUtils::create_directory(const std::string& path) {
    try {
        return std::filesystem::create_directories(path);
    } catch (...) {
        return false;
    }
}

std::vector<std::string> FileUtils::list_files(const std::string& directory,
                                              const std::string& pattern,
                                              bool recursive) {
    std::vector<std::string> files;
    std::regex pattern_regex(pattern);

    try {
        if (recursive) {
            for (const auto& entry : std::filesystem::recursive_directory_iterator(directory)) {
                if (entry.is_regular_file() &&
                    std::regex_match(entry.path().filename().string(), pattern_regex)) {
                    files.push_back(entry.path().string());
                }
            }
        } else {
            for (const auto& entry : std::filesystem::directory_iterator(directory)) {
                if (entry.is_regular_file() &&
                    std::regex_match(entry.path().filename().string(), pattern_regex)) {
                    files.push_back(entry.path().string());
                }
            }
        }
    } catch (...) {
        // Return empty vector on error
    }

    return files;
}

bool FileUtils::copy_file(const std::string& source, const std::string& destination) {
    try {
        std::filesystem::copy_file(source, destination,
                                  std::filesystem::copy_options::overwrite_existing);
        return true;
    } catch (...) {
        return false;
    }
}

bool FileUtils::move_file(const std::string& source, const std::string& destination) {
    try {
        std::filesystem::rename(source, destination);
        return true;
    } catch (...) {
        return false;
    }
}

bool FileUtils::delete_file(const std::string& filepath) {
    try {
        return std::filesystem::remove(filepath);
    } catch (...) {
        return false;
    }
}

std::string FileUtils::get_temp_directory() {
    return std::filesystem::temp_directory_path().string();
}

std::string FileUtils::create_temp_file(const std::string& prefix, const std::string& extension) {
    std::string temp_dir = get_temp_directory();
    std::string filename = prefix + "_" + StringUtils::random_string(8) + extension;
    return std::filesystem::path(temp_dir) / filename;
}

// SystemUtils implementation
std::optional<std::string> SystemUtils::get_env(const std::string& name) {
    const char* value = std::getenv(name.c_str());
    if (value) {
        return std::string(value);
    }
    return std::nullopt;
}

bool SystemUtils::set_env(const std::string& name, const std::string& value) {
#ifdef _WIN32
    return _putenv_s(name.c_str(), value.c_str()) == 0;
#else
    return setenv(name.c_str(), value.c_str(), 1) == 0;
#endif
}

std::string SystemUtils::get_current_directory() {
    return std::filesystem::current_path().string();
}

std::string SystemUtils::get_home_directory() {
#ifdef _WIN32
    const char* home = std::getenv("USERPROFILE");
    if (home) {
        return std::string(home);
    }
#else
    const char* home = std::getenv("HOME");
    if (home) {
        return std::string(home);
    }

    struct passwd* pw = getpwuid(getuid());
    if (pw) {
        return std::string(pw->pw_dir);
    }
#endif
    return "";
}

unsigned int SystemUtils::get_cpu_count() {
    return std::thread::hardware_concurrency();
}

size_t SystemUtils::get_available_memory() {
#ifdef _WIN32
    MEMORYSTATUSEX status;
    status.dwLength = sizeof(status);
    if (GlobalMemoryStatusEx(&status)) {
        return status.ullAvailPhys;
    }
#elif defined(__linux__)
    struct sysinfo info;
    if (sysinfo(&info) == 0) {
        return info.freeram * info.mem_unit;
    }
#elif defined(__APPLE__)
    vm_statistics64_data_t vm_stat;
    mach_msg_type_number_t count = HOST_VM_INFO64_COUNT;
    if (host_statistics64(mach_host_self(), HOST_VM_INFO64, (host_info64_t)&vm_stat, &count) == KERN_SUCCESS) {
        return vm_stat.free_count * vm_page_size;
    }
#endif
    return 0;
}

int SystemUtils::get_process_id() {
#ifdef _WIN32
    return GetCurrentProcessId();
#else
    return getpid();
#endif
}

std::optional<std::string> SystemUtils::execute_command(const std::string& command) {
    // Custom deleter for FILE*
    auto file_deleter = [](FILE* f) {
        if (f) {
            int result = pclose(f);
            if (result == -1) {
                // Log error but don't throw in destructor
            }
        }
    };
    
    std::unique_ptr<FILE, decltype(file_deleter)> pipe(
        popen(command.c_str(), "r"), 
        file_deleter
    );
    
    if (!pipe) {
        return std::nullopt;
    }

    std::array<char, 128> buffer;
    std::string result;
    
    while (fgets(buffer.data(), buffer.size(), pipe.get()) != nullptr) {
        result += buffer.data();
    }

    return result;
}

std::string SystemUtils::get_hostname() {
    char hostname[256];
    if (gethostname(hostname, sizeof(hostname)) == 0) {
        return std::string(hostname);
    }
    return "unknown";
}

std::string SystemUtils::get_username() {
#ifdef _WIN32
    char username[256];
    DWORD size = sizeof(username);
    if (GetUserNameA(username, &size)) {
        return std::string(username);
    }
#else
    const char* user = std::getenv("USER");
    if (user) {
        return std::string(user);
    }

    struct passwd* pw = getpwuid(getuid());
    if (pw) {
        return std::string(pw->pw_name);
    }
#endif
    return "unknown";
}

// CryptoUtils implementation
std::string CryptoUtils::md5(const std::string& data) {
    unsigned char digest[MD5_DIGEST_LENGTH];
    
    if (!MD5(reinterpret_cast<const unsigned char*>(data.c_str()), 
             data.length(), digest)) {
        throw std::runtime_error("MD5 computation failed");
    }

    std::stringstream ss;
    ss << std::hex << std::setfill('0');
    for (int i = 0; i < MD5_DIGEST_LENGTH; ++i) {
        ss << std::setw(2) << static_cast<unsigned int>(digest[i]);
    }

    return ss.str();
}

std::string CryptoUtils::sha256(const std::string& data) {
    unsigned char digest[SHA256_DIGEST_LENGTH];
    
    EVP_MD_CTX* mdctx = EVP_MD_CTX_new();
    if (!mdctx) {
        throw std::runtime_error("Failed to create EVP_MD_CTX");
    }
    
    if (EVP_DigestInit_ex(mdctx, EVP_sha256(), nullptr) != 1 ||
        EVP_DigestUpdate(mdctx, data.c_str(), data.length()) != 1 ||
        EVP_DigestFinal_ex(mdctx, digest, nullptr) != 1) {
        EVP_MD_CTX_free(mdctx);
        throw std::runtime_error("SHA256 computation failed");
    }
    
    EVP_MD_CTX_free(mdctx);

    std::stringstream ss;
    ss << std::hex << std::setfill('0');
    for (int i = 0; i < SHA256_DIGEST_LENGTH; ++i) {
        ss << std::setw(2) << static_cast<unsigned int>(digest[i]);
    }

    return ss.str();
}

std::string CryptoUtils::base64_encode(const std::vector<uint8_t>& data) {
    BIO* b64 = BIO_new(BIO_f_base64());
    BIO* bio = BIO_new(BIO_s_mem());
    bio = BIO_push(b64, bio);

    BIO_set_flags(bio, BIO_FLAGS_BASE64_NO_NL);
    BIO_write(bio, data.data(), data.size());
    BIO_flush(bio);

    BUF_MEM* buffer_ptr;
    BIO_get_mem_ptr(bio, &buffer_ptr);

    std::string result(buffer_ptr->data, buffer_ptr->length);
    BIO_free_all(bio);

    return result;
}

std::vector<uint8_t> CryptoUtils::base64_decode(const std::string& encoded) {
    BIO* b64 = BIO_new(BIO_f_base64());
    BIO* bio = BIO_new_mem_buf(encoded.c_str(), encoded.length());
    bio = BIO_push(b64, bio);

    BIO_set_flags(bio, BIO_FLAGS_BASE64_NO_NL);

    std::vector<uint8_t> result(encoded.length());
    int decoded_length = BIO_read(bio, result.data(), encoded.length());
    BIO_free_all(bio);

    result.resize(decoded_length);
    return result;
}

std::string CryptoUtils::generate_uuid() {
    uuid_t uuid;
    uuid_generate(uuid);

    char uuid_str[37];
    uuid_unparse_lower(uuid, uuid_str);

    return std::string(uuid_str);
}

std::vector<uint8_t> CryptoUtils::random_bytes(size_t length) {
    std::vector<uint8_t> result(length);
    
    if (RAND_bytes(result.data(), length) != 1) {
        // Fall back to C++ random if OpenSSL fails
        std::uniform_int_distribution<> dis(0, 255);
        
        // Use thread-local generator
        std::lock_guard<std::mutex> lock(random_mutex);
        
        // Log warning about fallback
        std::cerr << "Warning: OpenSSL RAND_bytes failed, using C++ random fallback\n";

        for (size_t i = 0; i < length; ++i) {
            result[i] = static_cast<uint8_t>(dis(tl_random_gen));
        }
    }

    return result;
}

// Static mutex for thread safety
std::mutex CryptoUtils::random_mutex;

// PerformanceUtils::MemoryTracker implementation
std::atomic<size_t> PerformanceUtils::MemoryTracker::global_peak_usage_{0};

// ValidationUtils implementation
bool ValidationUtils::is_valid_email(const std::string& email) {
    const std::regex pattern(R"(^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$)");
    return std::regex_match(email, pattern);
}

bool ValidationUtils::is_valid_url(const std::string& url) {
    const std::regex pattern(
        R"(^(https?://)?([a-zA-Z0-9.-]+)\.([a-zA-Z]{2,})(:[0-9]+)?(/.*)?$)");
    return std::regex_match(url, pattern);
}

bool ValidationUtils::is_valid_ip(const std::string& ip) {
    // IPv4 pattern
    const std::regex ipv4_pattern(
        R"(^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$)");

    // Simplified IPv6 pattern
    const std::regex ipv6_pattern(
        R"(^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::1|::)$)");

    return std::regex_match(ip, ipv4_pattern) || std::regex_match(ip, ipv6_pattern);
}

bool ValidationUtils::is_valid_phone(const std::string& phone, const std::string& country_code) {
    if (country_code == "US") {
        const std::regex pattern(R"(^\+?1?\s*\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}$)");
        return std::regex_match(phone, pattern);
    }
    // Add more country-specific patterns as needed
    return false;
}

bool ValidationUtils::is_valid_postal_code(const std::string& postal_code,
                                          const std::string& country_code) {
    if (country_code == "US") {
        const std::regex pattern(R"(^\d{5}(-\d{4})?$)");
        return std::regex_match(postal_code, pattern);
    } else if (country_code == "CA") {
        const std::regex pattern(R"(^[A-Za-z]\d[A-Za-z]\s?\d[A-Za-z]\d$)");
        return std::regex_match(postal_code, pattern);
    }
    // Add more country-specific patterns as needed
    return false;
}

bool ValidationUtils::is_valid_date_format(const std::string& date_str,
                                          const std::string& format) {
    std::tm tm = {};
    std::istringstream ss(date_str);
    ss >> std::get_time(&tm, format.c_str());
    return !ss.fail();
}

bool ValidationUtils::is_valid_json(const std::string& json) {
    try {
        auto parsed = nlohmann::json::parse(json);
        return true;
    } catch (...) {
        return false;
    }
}

bool ValidationUtils::is_valid_sql_identifier(const std::string& identifier) {
    if (identifier.empty()) {
        return false;
    }

    // Check if starts with letter or underscore
    if (!std::isalpha(identifier[0]) && identifier[0] != '_') {
        return false;
    }

    // Check each character
    for (char c : identifier) {
        if (!std::isalnum(c) && c != '_') {
            return false;
        }
    }

    // Check for reserved words (basic check)
    std::string upper_identifier = StringUtils::to_upper(identifier);
    static const std::vector<std::string> reserved_words = {
        "SELECT", "FROM", "WHERE", "INSERT", "UPDATE", "DELETE", "CREATE", "DROP", "TABLE", "INDEX"
    };

    for (const auto& word : reserved_words) {
        if (upper_identifier == word) {
            return false;
        }
    }

    return true;
}

bool ValidationUtils::isValidEmail(const std::string& email) {
    // Basic email validation regex
    static const std::regex email_regex(R"([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})");
    return std::regex_match(email, email_regex);
}

bool ValidationUtils::isValidPhoneNumber(const std::string& phone, const std::string& country_code) {
    if (country_code == "US") {
        // US phone number validation
        std::string cleaned = StringUtils::replace_all(phone, " ", "");
        cleaned = StringUtils::replace_all(cleaned, "-", "");
        cleaned = StringUtils::replace_all(cleaned, "(", "");
        cleaned = StringUtils::replace_all(cleaned, ")", "");
        cleaned = StringUtils::replace_all(cleaned, "+1", "");

        // Check if it's a 10-digit number
        if (cleaned.length() == 10 && std::all_of(cleaned.begin(), cleaned.end(), ::isdigit)) {
            return true;
        }

        // Check if it's a 11-digit number starting with 1
        if (cleaned.length() == 11 && cleaned[0] == '1' &&
            std::all_of(cleaned.begin() + 1, cleaned.end(), ::isdigit)) {
            return true;
        }
    }

    return false;
}

bool ValidationUtils::isValidPostalCode(const std::string& postal_code, const std::string& country_code) {
    if (country_code == "US") {
        // US ZIP code validation (5 digits or 5+4 format)
        static const std::regex zip_regex(R"(^\d{5}(-\d{4})?$)");
        return std::regex_match(postal_code, zip_regex);
    }

    return false;
}

bool ValidationUtils::isValidUUID(const std::string& uuid) {
    // UUID validation regex (8-4-4-4-12 format)
    static const std::regex uuid_regex(R"([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})");
    return std::regex_match(uuid, uuid_regex);
}

bool ValidationUtils::isValidURL(const std::string& url) {
    // Basic URL validation regex
    static const std::regex url_regex(R"((https?|ftp)://[^\s/$.?#].[^\s]*)");
    return std::regex_match(url, url_regex);
}

std::string ValidationUtils::sanitizeString(const std::string& input) {
    std::string result = input;

    // Remove or escape potentially dangerous characters
    result = StringUtils::replace_all(result, "<", "&lt;");
    result = StringUtils::replace_all(result, ">", "&gt;");
    result = StringUtils::replace_all(result, "&", "&amp;");
    result = StringUtils::replace_all(result, "\"", "&quot;");
    result = StringUtils::replace_all(result, "'", "&#39;");

    // Remove control characters
    result.erase(std::remove_if(result.begin(), result.end(),
                               [](char c) { return std::iscntrl(c) && c != '\n' && c != '\t'; }),
                result.end());

    return result;
}

// PerformanceUtils implementation
PerformanceUtils::MemoryTracker::MemoryTracker() {
#ifdef _WIN32
    PROCESS_MEMORY_COUNTERS pmc;
    if (GetProcessMemoryInfo(GetCurrentProcess(), &pmc, sizeof(pmc))) {
        initial_usage_ = pmc.WorkingSetSize;
        peak_usage_ = initial_usage_;
    }
#else
    struct rusage usage;
    if (getrusage(RUSAGE_SELF, &usage) == 0) {
        initial_usage_ = usage.ru_maxrss * 1024; // Convert KB to bytes
        peak_usage_ = initial_usage_;
    }
#endif
}

size_t PerformanceUtils::MemoryTracker::current_usage() const {
#ifdef _WIN32
    PROCESS_MEMORY_COUNTERS pmc;
    if (GetProcessMemoryInfo(GetCurrentProcess(), &pmc, sizeof(pmc))) {
        size_t current = pmc.WorkingSetSize;
        if (current > peak_usage_) {
            peak_usage_ = current;
        }
        // Update global peak if necessary
        size_t expected = global_peak_usage_.load();
        while (expected < current && !global_peak_usage_.compare_exchange_weak(expected, current)) {}
        
        return current - initial_usage_;
    }
#else
    struct rusage usage;
    if (getrusage(RUSAGE_SELF, &usage) == 0) {
        size_t current = usage.ru_maxrss * 1024;
        if (current > peak_usage_) {
            peak_usage_ = current;
        }
        // Update global peak if necessary
        size_t expected = global_peak_usage_.load();
        while (expected < current && !global_peak_usage_.compare_exchange_weak(expected, current)) {}
        
        return current - initial_usage_;
    }
#endif
    return 0;
}

size_t PerformanceUtils::MemoryTracker::peak_usage() const {
    return peak_usage_ - initial_usage_;
}

void PerformanceUtils::MemoryTracker::reset() {
    initial_usage_ = 0;
    peak_usage_ = 0;

#ifdef _WIN32
    PROCESS_MEMORY_COUNTERS pmc;
    if (GetProcessMemoryInfo(GetCurrentProcess(), &pmc, sizeof(pmc))) {
        initial_usage_ = pmc.WorkingSetSize;
        peak_usage_ = initial_usage_;
    }
#else
    struct rusage usage;
    if (getrusage(RUSAGE_SELF, &usage) == 0) {
        initial_usage_ = usage.ru_maxrss * 1024;
        peak_usage_ = initial_usage_;
    }
#endif
}

std::string PerformanceUtils::format_bytes(size_t bytes) {
    const char* units[] = {"B", "KB", "MB", "GB", "TB"};
    int unit_index = 0;
    double size = static_cast<double>(bytes);

    while (size >= 1024.0 && unit_index < 4) {
        size /= 1024.0;
        unit_index++;
    }

    std::stringstream ss;
    ss << std::fixed << std::setprecision(2) << size << " " << units[unit_index];
    return ss.str();
}

std::string PerformanceUtils::format_duration(double seconds) {
    if (seconds < 1.0) {
        return std::to_string(static_cast<int>(seconds * 1000)) + "ms";
    }

    int hours = static_cast<int>(seconds / 3600);
    int minutes = static_cast<int>((seconds - hours * 3600) / 60);
    int secs = static_cast<int>(seconds - hours * 3600 - minutes * 60);

    std::stringstream ss;
    if (hours > 0) {
        ss << hours << "h ";
    }
    if (minutes > 0 || hours > 0) {
        ss << minutes << "m ";
    }
    ss << secs << "s";

    return ss.str();
}

double PerformanceUtils::calculate_throughput(size_t items, double seconds) {
    if (seconds <= 0) {
        return 0.0;
    }
    return static_cast<double>(items) / seconds;
}

std::string ProcessingUtils::stage_name(int stage) {
    switch (stage) {
        case 0: // Extract
            return "Extract";
        case 1: // Transform
            return "Transform";
        case 2: // Load
            return "Load";
        default:
            return "Unknown";
    }
}

} // namespace omop::common

File src/lib/common/configuration.cpp:

#include "configuration.h"

#include <fstream>
#include <sstream>
#include <algorithm>
#include <filesystem>
#include <ranges>

namespace omop::common {

// TransformationRule implementation
TransformationRule::TransformationRule(const YAML::Node& node) {
    if (node["source_column"]) {
        source_column_ = node["source_column"].as<std::string>();
    } else if (node["source_columns"]) {
        source_columns_ = node["source_columns"].as<std::vector<std::string>>();
    } else {
        throw ConfigurationException("Transformation rule must have either 'source_column' or 'source_columns'");
    }

    // Validate source columns are not empty
    if (!source_column_.empty() && source_column_.find_first_not_of(" \t\n\r") == std::string::npos) {
        throw ConfigurationException("Source column cannot be empty or whitespace");
    }
    
    for (const auto& col : source_columns_) {
        if (col.find_first_not_of(" \t\n\r") == std::string::npos) {
            throw ConfigurationException("Source column cannot be empty or whitespace");
        }
    }

    if (!node["target_column"]) {
        throw ConfigurationException("Transformation rule must have 'target_column'");
    }
    target_column_ = node["target_column"].as<std::string>();

    if (node["type"]) {
        static const std::unordered_map<std::string, Type> type_map = {
            {"direct", Type::Direct},
            {"date_transform", Type::DateTransform},
            {"datetransform", Type::DateTransform},
            {"vocabulary_mapping", Type::VocabularyMapping},
            {"vocabularymapping", Type::VocabularyMapping},
            {"date_calculation", Type::DateCalculation},
            {"datecalculation", Type::DateCalculation},
            {"numeric_transform", Type::NumericTransform},
            {"numerictransform", Type::NumericTransform},
            {"string_concatenation", Type::StringConcatenation},
            {"stringconcatenation", Type::StringConcatenation},
            {"conditional", Type::Conditional},
            {"custom", Type::Custom}
        };

        std::string type_str = node["type"].as<std::string>();
        std::transform(type_str.begin(), type_str.end(), type_str.begin(), ::tolower);
        std::replace(type_str.begin(), type_str.end(), '-', '_');
        std::replace(type_str.begin(), type_str.end(), ' ', '_');
        auto it = type_map.find(type_str);
        if (it != type_map.end()) {
            type_ = it->second;
        } else {
            throw ConfigurationException(
                std::format("Unknown transformation type: '{}'", type_str), "type");
        }
    }

    // Store all other parameters
    for (const auto& item : node) {
        const std::string& key = item.first.as<std::string>();
        if (key != "source_column" && key != "source_columns" &&
            key != "target_column" && key != "type") {
            parameters_[key] = item.second;
        }
    }
}

// TableMapping implementation
TableMapping::TableMapping(const YAML::Node& node) {
    if (!node["source_table"]) {
        throw ConfigurationException("Table mapping must have 'source_table'");
    }
    source_table_ = node["source_table"].as<std::string>();

    if (!node["target_table"]) {
        throw ConfigurationException("Table mapping must have 'target_table'");
    }
    target_table_ = node["target_table"].as<std::string>();

    if (node["transformations"]) {
        for (const auto& trans_node : node["transformations"]) {
            transformations_.emplace_back(trans_node);
        }
    }

    if (node["pre_process_sql"]) {
        pre_process_sql_ = node["pre_process_sql"].as<std::string>();
    }

    if (node["post_process_sql"]) {
        post_process_sql_ = node["post_process_sql"].as<std::string>();
    }

    if (node["filters"]) {
        filters_ = node["filters"];
    }

    if (node["validations"]) {
        validations_ = node["validations"];
    }
}

// DatabaseConfig implementation
DatabaseConfig::DatabaseConfig(const YAML::Node& node) {
    if (node["type"]) {
        static const std::unordered_map<std::string, Type> type_map = {
            {"postgresql", Type::PostgreSQL},
            {"mysql", Type::MySQL},
            {"mssql", Type::MSSQL},
            {"oracle", Type::Oracle}
        };

        std::string type_str = node["type"].as<std::string>();
        std::transform(type_str.begin(), type_str.end(), type_str.begin(), ::tolower);

        auto it = type_map.find(type_str);
        if (it != type_map.end()) {
            type_ = it->second;
        } else {
            throw ConfigurationException(
                std::format("Unknown database type: '{}'", type_str), "type");
        }
    }

    if (node["connection_string"]) {
        connection_string_ = node["connection_string"].as<std::string>();
    } else {
        // Build connection from individual parameters
        if (!node["host"]) {
            throw ConfigurationException("Database configuration must have 'host' or 'connection_string'");
        }
        host_ = node["host"].as<std::string>();

        if (node["port"]) {
            port_ = node["port"].as<int>();
        } else {
            // Set default ports based on database type
            switch (type_) {
                case Type::PostgreSQL: port_ = 5432; break;
                case Type::MySQL: port_ = 3306; break;
                case Type::MSSQL: port_ = 1433; break;
                case Type::Oracle: port_ = 1521; break;
            }
        }

        if (!node["database"]) {
            throw ConfigurationException("Database configuration must have 'database'");
        }
        database_ = node["database"].as<std::string>();

        if (!node["username"]) {
            throw ConfigurationException("Database configuration must have 'username'");
        }
        username_ = node["username"].as<std::string>();

        if (node["password"]) {
            password_ = node["password"].as<std::string>();
        }
    }

    // Parse additional parameters
    if (node["parameters"]) {
        const auto& params_node = node["parameters"];
        for (const auto& param : params_node) {
            parameters_[param.first.as<std::string>()] = param.second.as<std::string>();
        }
    }
}

// ConfigurationManager implementation
void ConfigurationManager::load_config(const std::string& filepath) {
    std::ifstream file(filepath);
    if (!file.is_open()) {
        throw ConfigurationException(
            std::format("Failed to open configuration file: '{}'", filepath));
    }

    std::stringstream buffer;
    buffer << file.rdbuf();
    load_config_from_string(buffer.str());
}

void ConfigurationManager::load_config_from_string(const std::string& yaml_content) {
    try {
        root_config_ = YAML::Load(yaml_content);
    } catch (const YAML::Exception& e) {
        throw ConfigurationException(
            std::format("Failed to parse YAML configuration: {}", e.what()));
    }

    // Parse source database configuration
    if (!root_config_["source_database"]) {
        throw ConfigurationException("Configuration must have 'source_database' section");
    }
    source_db_ = parse_database_config(root_config_["source_database"]);

    // Parse target database configuration
    if (!root_config_["target_database"]) {
        throw ConfigurationException("Configuration must have 'target_database' section");
    }
    target_db_ = parse_database_config(root_config_["target_database"]);

    // Parse table mappings
    if (root_config_["table_mappings"]) {
        parse_table_mappings(root_config_["table_mappings"]);
    }

    // Parse vocabulary mappings
    if (root_config_["vocabulary_mappings"]) {
        vocabulary_mappings_ = root_config_["vocabulary_mappings"];
    }

    // Parse ETL settings
    if (root_config_["etl_settings"]) {
        etl_settings_ = root_config_["etl_settings"];
    } else {
        // Set default ETL settings
        etl_settings_["batch_size"] = 1000;
        etl_settings_["parallel_workers"] = 4;
        etl_settings_["validation_mode"] = "strict";
        etl_settings_["error_threshold"] = 0.01;
    }

    config_loaded_ = true;
    validate_config();
}

std::optional<TableMapping> ConfigurationManager::get_table_mapping(
    const std::string& table_name) const {
    auto it = table_mappings_.find(table_name);
    if (it != table_mappings_.end()) {
        return it->second;
    }
    return std::nullopt;
}

std::optional<YAML::Node> ConfigurationManager::get_value(const std::string& key) const {
    if (!config_loaded_) {
        return std::nullopt;
    }

    // Split key by dots to navigate nested structure
    std::vector<std::string> parts;
    std::stringstream ss(key);
    std::string part;
    while (std::getline(ss, part, '.')) {
        parts.push_back(part);
    }

    YAML::Node current = root_config_;
    for (const auto& p : parts) {
        if (!current[p]) {
            return std::nullopt;
        }
        current = current[p];
    }

    return current;
}

void ConfigurationManager::validate_config() const {
    if (!config_loaded_) {
        throw ConfigurationException("Configuration not loaded");
    }

    // Validate that we have at least one table mapping
    if (table_mappings_.empty()) {
        throw ConfigurationException("No table mappings defined in configuration");
    }

    // Validate each table mapping
    for (const auto& [name, mapping] : table_mappings_) {
        if (mapping.transformations().empty()) {
            throw ConfigurationException(
                std::format("Table mapping '{}' has no transformations defined", name));
        }

        // Validate that all transformation rules are properly configured
        for (const auto& rule : mapping.transformations()) {
            if (rule.target_column().empty()) {
                throw ConfigurationException(
                    std::format("Empty target column in table mapping '{}'", name));
            }

            if (!rule.is_multi_column() && rule.source_column().empty()) {
                throw ConfigurationException(
                    std::format("Empty source column in table mapping '{}'", name));
            }

            // Validate vocabulary mappings reference existing vocabularies
            if (rule.type() == TransformationRule::Type::VocabularyMapping) {
                auto vocab_param = rule.parameters()["vocabulary"];
                if (!vocab_param || !vocab_param.IsDefined()) {
                    throw ConfigurationException(
                        std::format("Vocabulary mapping in table '{}' missing vocabulary parameter", name));
                }

                std::string vocab_name = vocab_param.as<std::string>();
                if (!vocabulary_mappings_[vocab_name]) {
                    throw ConfigurationException(
                        std::format("Referenced vocabulary '{}' not found in vocabulary_mappings", vocab_name),
                        vocab_name);
                }
            }
        }
    }

    // Validate ETL settings
    int batch_size = get_value_or<int>("etl_settings.batch_size", 1000);
    if (batch_size <= 0) {
        throw ConfigurationException("Invalid batch_size: must be greater than 0", "batch_size");
    }

    int workers = get_value_or<int>("etl_settings.parallel_workers", 1);
    if (workers <= 0) {
        throw ConfigurationException("Invalid parallel_workers: must be greater than 0", "parallel_workers");
    }

    double error_threshold = get_value_or<double>("etl_settings.error_threshold", 0.01);
    if (error_threshold < 0.0 || error_threshold > 1.0) {
        throw ConfigurationException("Invalid error_threshold: must be between 0.0 and 1.0", "error_threshold");
    }
}

void ConfigurationManager::parse_table_mappings(const YAML::Node& mappings_node) {
    for (const auto& item : mappings_node) {
        std::string table_name = item.first.as<std::string>();
        try {
            table_mappings_[table_name] = TableMapping(item.second);
        } catch (const ConfigurationException& e) {
            throw ConfigurationException(
                std::format("Error parsing table mapping '{}': {}", table_name, e.message()));
        }
    }
}

DatabaseConfig ConfigurationManager::parse_database_config(const YAML::Node& db_node) {
    try {
        return DatabaseConfig(db_node);
    } catch (const ConfigurationException& e) {
        throw ConfigurationException(
            std::format("Error parsing database configuration: {}", e.message()));
    }
}

// Static member definitions
std::unique_ptr<ConfigurationManager> Config::instance_;
std::once_flag Config::init_flag_;
std::mutex Config::instance_mutex_;

} // namespace omop::common

File src/lib/common/validation.h:

/**
 * @file validation.h
 * @brief Data validation framework for OMOP ETL pipeline
 * <AUTHOR> ETL Team
 * @date 2024
 *
 * This file contains the validation framework for ensuring data quality
 * and integrity throughout the ETL process.
 */

#pragma once

#include "exceptions.h"
#include "logging.h"

#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <unordered_map>
#include <variant>
#include <optional>
#include <chrono>
#include <regex>
#include <any>

namespace omop::common {

/**
 * @brief Enumeration of validation rule types
 */
enum class ValidationType {
    NOT_NULL,           ///< Field must not be null
    NOT_EMPTY,          ///< Field must not be empty
    UNIQUE,             ///< Field must be unique
    IN_LIST,            ///< Field must be in specified list
    REGEX,              ///< Field must match regex pattern
    DATE_RANGE,         ///< Date must be within range
    NUMERIC_RANGE,      ///< Number must be within range
    GREATER_THAN,       ///< Number must be greater than value
    LESS_THAN,          ///< Number must be less than value
    BETWEEN,            ///< Value must be between two values
    BEFORE,             ///< Date must be before another date
    AFTER,              ///< Date must be after another date
    LENGTH,             ///< String length validation
    CUSTOM,             ///< Custom validation function
    NOT_ZERO,           ///< Number must not be zero
    NOT_FUTURE_DATE,    ///< Date must not be in the future
    FOREIGN_KEY,        ///< Foreign key constraint
    COMPOSITE_KEY,      ///< Composite key validation
    CONDITIONAL         ///< Conditional validation based on other fields
};

/**
 * @brief Structure to hold validation results
 */
struct ValidationResult {
    bool is_valid;                          ///< Overall validation status
    std::vector<std::string> errors;        ///< List of validation errors
    std::vector<std::string> warnings;      ///< List of validation warnings
    size_t records_validated;               ///< Number of records validated
    size_t records_failed;                  ///< Number of records that failed validation

    /**
     * @brief Check if validation passed
     * @return true if validation passed, false otherwise
     */
    bool passed() const { return is_valid && errors.empty(); }

    /**
     * @brief Merge another validation result into this one
     * @param other The validation result to merge
     */
    void merge(const ValidationResult& other);
};

/**
 * @brief Base class for validation rules
 */
class ValidationRule {
public:
    /**
     * @brief Constructor
     * @param field_name Name of the field to validate
     * @param type Type of validation
     * @param error_message Custom error message
     */
    ValidationRule(const std::string& field_name,
                  ValidationType type,
                  const std::string& error_message = "");

    /**
     * @brief Virtual destructor
     */
    virtual ~ValidationRule() = default;

    /**
     * @brief Validate a value
     * @param value The value to validate
     * @param record The entire record (for context)
     * @return true if validation passes, false otherwise
     */
    virtual bool validate(const std::any& value,
                         const std::unordered_map<std::string, std::any>& record) const = 0;

    /**
     * @brief Get the error message for validation failure
     * @return Error message string
     */
    virtual std::string getErrorMessage() const;

    /**
     * @brief Get the field name
     * @return Field name string
     */
    const std::string& getFieldName() const { return field_name_; }

    /**
     * @brief Get the validation type
     * @return Validation type enum
     */
    ValidationType getType() const { return type_; }

protected:
    std::string field_name_;      ///< Name of the field to validate
    ValidationType type_;         ///< Type of validation
    std::string error_message_;   ///< Custom error message
};

/**
 * @brief Not null validation rule
 */
class NotNullRule : public ValidationRule {
public:
    /**
     * @brief Constructor
     * @param field_name Name of the field to validate
     */
    explicit NotNullRule(const std::string& field_name);

    bool validate(const std::any& value,
                 const std::unordered_map<std::string, std::any>& record) const override;
};

/**
 * @brief In list validation rule
 */
template<typename T>
class InListRule : public ValidationRule {
public:
    /**
     * @brief Constructor
     * @param field_name Name of the field to validate
     * @param allowed_values List of allowed values
     */
    InListRule(const std::string& field_name, const std::vector<T>& allowed_values);

    bool validate(const std::any& value,
                 const std::unordered_map<std::string, std::any>& record) const override;

private:
    std::vector<T> allowed_values_;  ///< List of allowed values
};

/**
 * @brief Date range validation rule
 */
class DateRangeRule : public ValidationRule {
public:
    /**
     * @brief Constructor
     * @param field_name Name of the field to validate
     * @param min_date Minimum allowed date (optional)
     * @param max_date Maximum allowed date (optional)
     */
    DateRangeRule(const std::string& field_name,
                  const std::optional<std::chrono::system_clock::time_point>& min_date,
                  const std::optional<std::chrono::system_clock::time_point>& max_date);

    bool validate(const std::any& value,
                 const std::unordered_map<std::string, std::any>& record) const override;

private:
    std::optional<std::chrono::system_clock::time_point> min_date_;  ///< Minimum date
    std::optional<std::chrono::system_clock::time_point> max_date_;  ///< Maximum date
};

/**
 * @brief Numeric range validation rule
 */
template<typename T>
class NumericRangeRule : public ValidationRule {
public:
    /**
     * @brief Constructor
     * @param field_name Name of the field to validate
     * @param min_value Minimum allowed value (optional)
     * @param max_value Maximum allowed value (optional)
     */
    NumericRangeRule(const std::string& field_name,
                     const std::optional<T>& min_value,
                     const std::optional<T>& max_value);

    bool validate(const std::any& value,
                 const std::unordered_map<std::string, std::any>& record) const override;

private:
    std::optional<T> min_value_;  ///< Minimum value
    std::optional<T> max_value_;  ///< Maximum value
};

/**
 * @brief Regular expression validation rule
 */
class RegexRule : public ValidationRule {
public:
    /**
     * @brief Constructor
     * @param field_name Name of the field to validate
     * @param pattern Regular expression pattern
     */
    RegexRule(const std::string& field_name, const std::string& pattern);

    bool validate(const std::any& value,
                 const std::unordered_map<std::string, std::any>& record) const override;

private:
    std::regex pattern_;  ///< Regular expression pattern
};

/**
 * @brief Custom validation rule with user-defined function
 */
class CustomRule : public ValidationRule {
public:
    using ValidationFunction = std::function<bool(const std::any&, const std::unordered_map<std::string, std::any>&)>;

    /**
     * @brief Constructor
     * @param field_name Name of the field to validate
     * @param validator Custom validation function
     * @param error_message Custom error message
     */
    CustomRule(const std::string& field_name,
               ValidationFunction validator,
               const std::string& error_message = "");

    bool validate(const std::any& value,
                 const std::unordered_map<std::string, std::any>& record) const override;

private:
    ValidationFunction validator_;  ///< Custom validation function
};

/**
 * @brief Validation engine for applying validation rules
 */
class ValidationEngine {
public:
    /**
     * @brief Constructor
     * @param logger Logger instance
     */
    explicit ValidationEngine(std::shared_ptr<Logger> logger = nullptr);

    /**
     * @brief Add a validation rule
     * @param rule Validation rule to add
     */
    void addRule(std::unique_ptr<ValidationRule> rule);

    /**
     * @brief Validate a single record
     * @param record Record to validate
     * @return Validation result
     */
    ValidationResult validateRecord(const std::unordered_map<std::string, std::any>& record);

    /**
     * @brief Validate a batch of records
     * @param records Records to validate
     * @param stop_on_error Stop validation on first error
     * @return Validation result
     */
    ValidationResult validateBatch(const std::vector<std::unordered_map<std::string, std::any>>& records,
                                  bool stop_on_error = false);

    /**
     * @brief Clear all validation rules
     */
    void clearRules();

    /**
     * @brief Get the number of rules
     * @return Number of validation rules
     */
    size_t getRuleCount() const { return rules_.size(); }

    /**
     * @brief Enable or disable specific validation types
     * @param type Validation type to enable/disable
     * @param enabled true to enable, false to disable
     */
    void setValidationTypeEnabled(ValidationType type, bool enabled);

private:
    std::vector<std::unique_ptr<ValidationRule>> rules_;  ///< List of validation rules
    std::unordered_map<ValidationType, bool> enabled_types_;  ///< Enabled validation types
    std::shared_ptr<Logger> logger_;  ///< Logger instance
};

/**
 * @brief Factory class for creating validation rules from configuration
 */
class ValidationRuleFactory {
public:
    /**
     * @brief Create a validation rule from configuration
     * @param config Configuration map
     * @return Unique pointer to validation rule
     */
    static std::unique_ptr<ValidationRule> createRule(const std::unordered_map<std::string, std::any>& config);

    /**
     * @brief Create validation engine from YAML configuration
     * @param yaml_config YAML configuration string
     * @return Unique pointer to validation engine
     */
    static std::unique_ptr<ValidationEngine> createEngineFromYaml(const std::string& yaml_config);
};

} // namespace omop::common
```

The C++ source code unit test files in the project directory tests/unit/common.

```
File tests/unit/common/CMakeLists.txt:

# Unit tests for OMOP Common library

# Test source files in the common directory
set(COMMON_TEST_SOURCES
    configuration_test.cpp
    exceptions_test.cpp
    logging_test.cpp
    utilities_test.cpp
    validation_test.cpp
)

# Use the new consolidated approach
add_component_unit_tests(common ${COMMON_TEST_SOURCES})

# Copy test data files if any exist
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/test_data)
    file(COPY ${CMAKE_CURRENT_SOURCE_DIR}/test_data
        DESTINATION ${CMAKE_CURRENT_BINARY_DIR}
    )
endif()

# Test configuration summary
message(STATUS "Common Unit Tests Configuration:")
message(STATUS "  Test files: ${COMMON_TEST_SOURCES}")
message(STATUS "  Output directory: ${CMAKE_BINARY_DIR}/tests/unit/common")
message(STATUS "  C++ Standard: 20")

# Remove or comment out the custom target to avoid duplicate errors
# add_custom_target(test_common_tests ...)

File tests/unit/common/logging_test.cpp:

// Test file: tests/unit/common/test_logging.cpp
/**
 * Unit tests for OMOP logging framework
 */

#include <gtest/gtest.h>
#include "common/logging.h"
#include <sstream>
#include <thread>
#include <chrono>
#include <mutex>

namespace omop::common::test {

// Mock log sink for testing
class MockLogSink : public ILogSink {
public:
    void write(const LogEntry& entry) override {
        std::lock_guard<std::mutex> lock(mutex_);
        entries.push_back(entry);
        if (formatter_) {
            formatted_messages.push_back(formatter_->format(entry));
        }
    }

    void flush() override {
        std::lock_guard<std::mutex> lock(mutex_);
        flush_called = true;
    }

    // Override the set_formatter method to make it public for testing
    void set_formatter(std::unique_ptr<ILogFormatter> formatter) {
        ILogSink::set_formatter(std::move(formatter));
    }

    // Thread-safe access to entries
    size_t get_entries_size() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return entries.size();
    }

    LogEntry get_entry(size_t index) const {
        std::lock_guard<std::mutex> lock(mutex_);
        return entries.at(index);
    }

    std::vector<LogEntry> get_all_entries() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return entries;
    }

    std::vector<LogEntry> entries;
    std::vector<std::string> formatted_messages;
    bool flush_called = false;

private:
    mutable std::mutex mutex_;
};

class LoggingTest : public ::testing::Test {
protected:
    void SetUp() override {
        logger = Logger::get("TestLogger");
        logger->clear_sinks(); // Clear any existing sinks from previous tests
        mock_sink = std::make_shared<MockLogSink>();
        logger->add_sink(mock_sink);
        logger->set_level(LogLevel::Debug); // Ensure all messages are logged
    }

    void TearDown() override {
        // Clear the logger's sinks to prevent interference between tests
        if (logger) {
            logger->clear_sinks();
        }
        mock_sink->entries.clear();
        mock_sink->formatted_messages.clear();
        mock_sink->flush_called = false;
    }

    std::shared_ptr<Logger> logger;
    std::shared_ptr<MockLogSink> mock_sink;
};

// Test basic logging functionality
TEST_F(LoggingTest, BasicLogging) {
    logger->set_level(LogLevel::Debug);

    logger->info("Test message");

    ASSERT_EQ(mock_sink->entries.size(), 1);
    EXPECT_EQ(mock_sink->entries[0].level, LogLevel::Info);
    EXPECT_EQ(mock_sink->entries[0].message, "Test message");
    EXPECT_EQ(mock_sink->entries[0].logger_name, "TestLogger");
}

// Test log level filtering
TEST_F(LoggingTest, LogLevelFiltering) {
    logger->set_level(LogLevel::Warning);

    logger->debug("Debug message");
    logger->info("Info message");
    logger->warn("Warning message");
    logger->error("Error message");

    ASSERT_EQ(mock_sink->entries.size(), 2);
    EXPECT_EQ(mock_sink->entries[0].level, LogLevel::Warning);
    EXPECT_EQ(mock_sink->entries[1].level, LogLevel::Error);
}

// Test formatted logging
TEST_F(LoggingTest, FormattedLogging) {
    logger->info("User {} has {} items", "John", 5);

    ASSERT_EQ(mock_sink->entries.size(), 1);
    EXPECT_EQ(mock_sink->entries[0].message, "User John has 5 items");
}

// Test structured logging
TEST_F(LoggingTest, StructuredLogging) {
    std::unordered_map<std::string, std::any> context;
    context["user_id"] = 12345;
    context["action"] = std::string("login");
    context["success"] = true;

    logger->log_structured(LogLevel::Info, "User login attempt", context);

    ASSERT_EQ(mock_sink->entries.size(), 1);
    const auto& entry = mock_sink->entries[0];

    EXPECT_EQ(entry.message, "User login attempt");
    EXPECT_EQ(entry.context.size(), 3);
    EXPECT_EQ(std::any_cast<int>(entry.context.at("user_id")), 12345);
    EXPECT_EQ(std::any_cast<std::string>(entry.context.at("action")), "login");
    EXPECT_EQ(std::any_cast<bool>(entry.context.at("success")), true);
}

// Test job and component context
TEST_F(LoggingTest, JobComponentContext) {
    logger->set_job_id("job_123");
    logger->set_component("DataExtractor");

    logger->info("Processing records");

    ASSERT_EQ(mock_sink->entries.size(), 1);
    const auto& entry = mock_sink->entries[0];

    EXPECT_EQ(entry.job_id, "job_123");
    EXPECT_EQ(entry.component, "DataExtractor");
}

// Test operation logging
TEST_F(LoggingTest, OperationLogging) {
    std::unordered_map<std::string, std::any> details;
    details["records_processed"] = static_cast<size_t>(1000);
    details["duration_ms"] = 2500.0;

    logger->log_operation("extract_patients", "completed", details);

    ASSERT_EQ(mock_sink->entries.size(), 1);
    const auto& entry = mock_sink->entries[0];

    EXPECT_EQ(entry.operation, "extract_patients");
    EXPECT_TRUE(entry.message.find("extract_patients") != std::string::npos);
    EXPECT_TRUE(entry.message.find("completed") != std::string::npos);
    EXPECT_EQ(entry.context.size(), 3); // details + operation_status
}

// Test metrics logging
TEST_F(LoggingTest, MetricsLogging) {
    std::unordered_map<std::string, double> metrics;
    metrics["cpu_usage"] = 75.5;
    metrics["memory_usage"] = 1024.0;
    metrics["throughput"] = 100.5;

    logger->log_metrics(metrics);

    ASSERT_EQ(mock_sink->entries.size(), 1);
    const auto& entry = mock_sink->entries[0];

    EXPECT_EQ(entry.operation, "metrics");
    EXPECT_EQ(entry.context.size(), 3);
    EXPECT_DOUBLE_EQ(std::any_cast<double>(entry.context.at("cpu_usage")), 75.5);
}

// Test exception logging
TEST_F(LoggingTest, ExceptionLogging) {
    std::runtime_error ex("Test exception");
    std::unordered_map<std::string, std::any> context;
    context["operation"] = std::string("data_load");

    logger->log_exception(ex, context);

    ASSERT_EQ(mock_sink->entries.size(), 1);
    const auto& entry = mock_sink->entries[0];

    EXPECT_EQ(entry.level, LogLevel::Error);
    EXPECT_TRUE(entry.message.find("Test exception") != std::string::npos);
    EXPECT_TRUE(entry.stack_trace.has_value());
    EXPECT_EQ(std::any_cast<std::string>(entry.context.at("operation")), "data_load");
}

// Test JsonLogFormatter
TEST_F(LoggingTest, JsonLogFormatter) {
    auto formatter = std::make_unique<JsonLogFormatter>();
    mock_sink->set_formatter(std::move(formatter));

    std::unordered_map<std::string, std::any> context;
    context["test_field"] = std::string("test_value");

    logger->log_structured(LogLevel::Info, "Test message", context);

    ASSERT_EQ(mock_sink->formatted_messages.size(), 1);
    const auto& json_msg = mock_sink->formatted_messages[0];

    EXPECT_TRUE(json_msg.find("\"level\":\"INFO\"") != std::string::npos);
    EXPECT_TRUE(json_msg.find("\"message\":\"Test message\"") != std::string::npos);
    EXPECT_TRUE(json_msg.find("\"test_field\":\"test_value\"") != std::string::npos);
}

// Test TextLogFormatter
TEST_F(LoggingTest, TextLogFormatter) {
    auto formatter = std::make_unique<TextLogFormatter>("[%l] %n: %v");
    mock_sink->set_formatter(std::move(formatter));

    logger->info("Test message");

    ASSERT_EQ(mock_sink->formatted_messages.size(), 1);
    const auto& text_msg = mock_sink->formatted_messages[0];

    EXPECT_TRUE(text_msg.find("[INFO] TestLogger: Test message") != std::string::npos);
}

// Test PerformanceLogger timing
TEST_F(LoggingTest, PerformanceLoggerTiming) {
    PerformanceLogger perf_logger(logger);

    perf_logger.start_timing("test_operation");
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    perf_logger.end_timing("test_operation", 100);

    ASSERT_GE(mock_sink->entries.size(), 1);
    const auto& entry = mock_sink->entries.back();

    EXPECT_TRUE(entry.context.find("duration_ms") != entry.context.end());
    EXPECT_TRUE(entry.context.find("record_count") != entry.context.end());
    EXPECT_TRUE(entry.context.find("records_per_second") != entry.context.end());

    auto duration = std::any_cast<double>(entry.context.at("duration_ms"));
    EXPECT_GE(duration, 10.0);
}

// Test PerformanceLogger scoped timer
TEST_F(LoggingTest, PerformanceLoggerScopedTimer) {
    PerformanceLogger perf_logger(logger);

    {
        auto timer = perf_logger.scoped_timer("scoped_operation");
        timer.set_record_count(50);
        std::this_thread::sleep_for(std::chrono::milliseconds(5));
    } // Timer should end here automatically

    ASSERT_GE(mock_sink->entries.size(), 1);
    const auto& entry = mock_sink->entries.back();

    EXPECT_TRUE(entry.context.find("duration_ms") != entry.context.end());
    EXPECT_EQ(std::any_cast<size_t>(entry.context.at("record_count")), 50);
}

// Test PerformanceLogger throughput
TEST_F(LoggingTest, PerformanceLoggerThroughput) {
    PerformanceLogger perf_logger(logger);

    perf_logger.log_throughput("data_processing", 1500.5);

    ASSERT_GE(mock_sink->entries.size(), 1);

    // Find the metrics log entry
    auto it = std::find_if(mock_sink->entries.begin(), mock_sink->entries.end(),
        [](const LogEntry& entry) {
            return entry.operation == "metrics" ||
                   entry.message.find("throughput") != std::string::npos;
        });

    ASSERT_NE(it, mock_sink->entries.end());
}

// Test PerformanceLogger resource usage
TEST_F(LoggingTest, PerformanceLoggerResourceUsage) {
    PerformanceLogger perf_logger(logger);

    perf_logger.log_resource_usage(85.5, 2048.0, 150.5);

    ASSERT_GE(mock_sink->entries.size(), 1);
    const auto& entry = mock_sink->entries.back();

    EXPECT_DOUBLE_EQ(std::any_cast<double>(entry.context.at("cpu_percent")), 85.5);
    EXPECT_DOUBLE_EQ(std::any_cast<double>(entry.context.at("memory_mb")), 2048.0);
    EXPECT_DOUBLE_EQ(std::any_cast<double>(entry.context.at("disk_io_mb")), 150.5);
}

// Test AuditLogger data access
TEST_F(LoggingTest, AuditLoggerDataAccess) {
    AuditLogger audit_logger(logger);

    audit_logger.log_data_access("person", "read", 1000, "user123");

    ASSERT_EQ(mock_sink->entries.size(), 1);
    const auto& entry = mock_sink->entries[0];

    EXPECT_EQ(entry.level, LogLevel::Info);
    EXPECT_TRUE(entry.message.find("Data access") != std::string::npos);
    EXPECT_TRUE(entry.message.find("person") != std::string::npos);
    EXPECT_EQ(std::any_cast<std::string>(entry.context.at("table_name")), "person");
    EXPECT_EQ(std::any_cast<std::string>(entry.context.at("operation")), "read");
    EXPECT_EQ(std::any_cast<size_t>(entry.context.at("record_count")), 1000);
    EXPECT_EQ(std::any_cast<std::string>(entry.context.at("user")), "user123");
    EXPECT_EQ(std::any_cast<std::string>(entry.context.at("audit_type")), "data_access");
}

// Test AuditLogger config change
TEST_F(LoggingTest, AuditLoggerConfigChange) {
    AuditLogger audit_logger(logger);

    audit_logger.log_config_change("database.timeout", "30", "60", "admin");

    ASSERT_EQ(mock_sink->entries.size(), 1);
    const auto& entry = mock_sink->entries[0];

    EXPECT_EQ(entry.level, LogLevel::Info);
    EXPECT_TRUE(entry.message.find("Configuration change") != std::string::npos);
    EXPECT_EQ(std::any_cast<std::string>(entry.context.at("config_item")), "database.timeout");
    EXPECT_EQ(std::any_cast<std::string>(entry.context.at("old_value")), "30");
    EXPECT_EQ(std::any_cast<std::string>(entry.context.at("new_value")), "60");
    EXPECT_EQ(std::any_cast<std::string>(entry.context.at("user")), "admin");
    EXPECT_EQ(std::any_cast<std::string>(entry.context.at("audit_type")), "config_change");
}

// Test AuditLogger security event
TEST_F(LoggingTest, AuditLoggerSecurityEvent) {
    AuditLogger audit_logger(logger);

    std::unordered_map<std::string, std::string> details;
    details["ip_address"] = "*************";
    details["user_agent"] = "Mozilla/5.0";
    details["attempted_user"] = "admin";

    audit_logger.log_security_event("failed_login", details);

    ASSERT_EQ(mock_sink->entries.size(), 1);
    const auto& entry = mock_sink->entries[0];

    EXPECT_EQ(entry.level, LogLevel::Warning);
    EXPECT_TRUE(entry.message.find("Security event") != std::string::npos);
    EXPECT_TRUE(entry.message.find("failed_login") != std::string::npos);
    EXPECT_EQ(std::any_cast<std::string>(entry.context.at("ip_address")), "*************");
    EXPECT_EQ(std::any_cast<std::string>(entry.context.at("audit_type")), "security");
    EXPECT_EQ(std::any_cast<std::string>(entry.context.at("event_type")), "failed_login");
}

// Test thread safety
TEST_F(LoggingTest, ThreadSafety) {
    const int num_threads = 10;
    const int messages_per_thread = 100;

    std::vector<std::thread> threads;

    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back([this, i, messages_per_thread]() {
            for (int j = 0; j < messages_per_thread; ++j) {
                logger->info("Thread {} message {}", i, j);
            }
        });
    }

    for (auto& thread : threads) {
        thread.join();
    }

    EXPECT_EQ(mock_sink->get_entries_size(), num_threads * messages_per_thread);
}

// Test LoggingConfig default initialization
TEST_F(LoggingTest, LoggingConfigDefaultInit) {
    EXPECT_NO_THROW(LoggingConfig::initialize_default());
    EXPECT_NO_THROW(LoggingConfig::set_global_level(LogLevel::Debug));
    EXPECT_NO_THROW(LoggingConfig::flush_all());
}

} // namespace omop::common::test


File tests/unit/common/exceptions_test.cpp:

// Test file: tests/unit/common/test_exceptions.cpp
/**
 * Unit tests for OMOP common exception classes
 */

#include <gtest/gtest.h>
#include "common/exceptions.h"
#include <string_view>

namespace omop::common::test {

class ExceptionsTest : public ::testing::Test {
protected:
    void SetUp() override {}
    void TearDown() override {}
};

// Test base OmopException functionality
TEST_F(ExceptionsTest, OmopExceptionBasicFunctionality) {
    const std::string test_message = "Test exception message";

    OmopException ex(test_message);

    EXPECT_EQ(ex.message(), test_message);
    EXPECT_NE(ex.what(), nullptr);
    EXPECT_TRUE(std::string_view(ex.what()).find(test_message) != std::string_view::npos);

    const auto& location = ex.location();
    EXPECT_FALSE(std::string_view(location.file_name()).empty());
    EXPECT_GT(location.line(), 0);
}

// Test exception with source location
TEST_F(ExceptionsTest, OmopExceptionSourceLocation) {
    const std::string test_message = "Location test";
    auto source_loc = std::source_location::current();

    OmopException ex(test_message, source_loc);

    EXPECT_EQ(ex.location().line(), source_loc.line());
    EXPECT_EQ(ex.location().function_name(), source_loc.function_name());
}

// Test ConfigurationException functionality
TEST_F(ExceptionsTest, ConfigurationExceptionFunctionality) {
    const std::string message = "Configuration error";
    const std::string config_key = "database.host";

    ConfigurationException ex(message, config_key);

    EXPECT_EQ(ex.config_key(), config_key);
    EXPECT_TRUE(std::string_view(ex.what()).find(config_key) != std::string_view::npos);
    EXPECT_TRUE(std::string_view(ex.what()).find(message) != std::string_view::npos);
}

// Test ExtractionException functionality
TEST_F(ExceptionsTest, ExtractionExceptionFunctionality) {
    const std::string message = "Extraction failed";
    const std::string source_name = "patient_data.csv";

    ExtractionException ex(message, source_name);

    EXPECT_EQ(ex.source_name(), source_name);
    EXPECT_TRUE(std::string_view(ex.what()).find(source_name) != std::string_view::npos);
}

// Test TransformationException functionality
TEST_F(ExceptionsTest, TransformationExceptionFunctionality) {
    const std::string message = "Transform failed";
    const std::string field_name = "birth_date";
    const std::string transformation_type = "DateTransform";

    TransformationException ex(message, field_name, transformation_type);

    EXPECT_EQ(ex.field_name(), field_name);
    EXPECT_EQ(ex.transformation_type(), transformation_type);
    EXPECT_TRUE(std::string_view(ex.what()).find(field_name) != std::string_view::npos);
    EXPECT_TRUE(std::string_view(ex.what()).find(transformation_type) != std::string_view::npos);
}

// Test LoadException functionality
TEST_F(ExceptionsTest, LoadExceptionFunctionality) {
    const std::string message = "Load operation failed";
    const std::string target_table = "person";

    LoadException ex(message, target_table);

    EXPECT_EQ(ex.target_table(), target_table);
    EXPECT_TRUE(std::string_view(ex.what()).find(target_table) != std::string_view::npos);
}

// Test DatabaseException functionality
TEST_F(ExceptionsTest, DatabaseExceptionFunctionality) {
    const std::string message = "Connection failed";
    const std::string database_type = "PostgreSQL";
    const int error_code = 1234;

    DatabaseException ex(message, database_type, error_code);

    EXPECT_EQ(ex.database_type(), database_type);
    EXPECT_EQ(ex.error_code(), error_code);
    EXPECT_TRUE(std::string_view(ex.what()).find(database_type) != std::string_view::npos);
    EXPECT_TRUE(std::string_view(ex.what()).find(std::to_string(error_code)) != std::string_view::npos);
}

// Test ValidationException functionality
TEST_F(ExceptionsTest, ValidationExceptionFunctionality) {
    const std::string message = "Validation failed";
    const std::string rule_name = "not_null";
    const std::string field_value = "";

    ValidationException ex(message, rule_name, field_value);

    EXPECT_EQ(ex.rule_name(), rule_name);
    EXPECT_EQ(ex.field_value(), field_value);
    EXPECT_TRUE(std::string_view(ex.what()).find(rule_name) != std::string_view::npos);
}

// Test VocabularyException functionality
TEST_F(ExceptionsTest, VocabularyExceptionFunctionality) {
    const std::string message = "Vocabulary mapping failed";
    const std::string vocabulary_name = "ICD10CM";
    const std::string source_value = "Z00.00";

    VocabularyException ex(message, vocabulary_name, source_value);

    EXPECT_EQ(ex.vocabulary_name(), vocabulary_name);
    EXPECT_EQ(ex.source_value(), source_value);
    EXPECT_TRUE(std::string_view(ex.what()).find(vocabulary_name) != std::string_view::npos);
    EXPECT_TRUE(std::string_view(ex.what()).find(source_value) != std::string_view::npos);
}

// Test ApiException functionality
TEST_F(ExceptionsTest, ApiExceptionFunctionality) {
    const std::string message = "API call failed";
    const int http_status = 404;
    const std::string endpoint = "/api/v1/patients";

    ApiException ex(message, http_status, endpoint);

    EXPECT_EQ(ex.http_status(), http_status);
    EXPECT_EQ(ex.endpoint(), endpoint);
    EXPECT_TRUE(std::string_view(ex.what()).find(endpoint) != std::string_view::npos);
    EXPECT_TRUE(std::string_view(ex.what()).find(std::to_string(http_status)) != std::string_view::npos);
}

// Test exception inheritance
TEST_F(ExceptionsTest, ExceptionInheritance) {
    ConfigurationException config_ex("Config error", "key");

    EXPECT_NO_THROW(static_cast<OmopException&>(config_ex));
    EXPECT_NO_THROW(static_cast<std::exception&>(config_ex));

    // Test polymorphic behavior
    std::unique_ptr<OmopException> base_ptr =
        std::make_unique<ConfigurationException>("Test", "test_key");

    EXPECT_EQ(base_ptr->message(), "Test: key 'test_key'");
}

} // namespace omop::common::test


File tests/unit/common/validation_test.cpp:

// Test file: tests/unit/common/test_validation.cpp
/**
 * Unit tests for OMOP validation framework
 */

#include <gtest/gtest.h>
#include "common/validation.h"
#include "common/utilities.h"
#include "common/exceptions.h"
#include <chrono>
#include <unordered_map>
#include <any>
#include <iostream>

namespace omop::common::test {

class ValidationTest : public ::testing::Test {
protected:
    void SetUp() override {
        engine = std::make_unique<ValidationEngine>();
        logger = Logger::get("ValidationTest");
    }

    void TearDown() override {
        engine.reset();
    }

    std::unique_ptr<ValidationEngine> engine;
    std::shared_ptr<Logger> logger;

    std::unordered_map<std::string, std::any> createTestRecord() {
        std::unordered_map<std::string, std::any> record;
        record["name"] = std::string("John Doe");
        record["age"] = 30;
        record["email"] = std::string("<EMAIL>");
        record["birth_date"] = std::chrono::system_clock::now();
        return record;
    }
};

// Test ValidationResult merge functionality
TEST_F(ValidationTest, ValidationResultMerge) {
    ValidationResult result1;
    result1.is_valid = true;
    result1.records_validated = 10;
    result1.records_failed = 0;

    ValidationResult result2;
    result2.is_valid = false;
    result2.errors.push_back("Error 1");
    result2.warnings.push_back("Warning 1");
    result2.records_validated = 5;
    result2.records_failed = 2;

    result1.merge(result2);

    EXPECT_FALSE(result1.is_valid);
    EXPECT_EQ(result1.errors.size(), 1);
    EXPECT_EQ(result1.warnings.size(), 1);
    EXPECT_EQ(result1.records_validated, 15);
    EXPECT_EQ(result1.records_failed, 2);
}

// Test NotNullRule functionality
TEST_F(ValidationTest, NotNullRuleValidation) {
    auto rule = std::make_unique<NotNullRule>("name");

    std::unordered_map<std::string, std::any> record;

    // Test with null value
    EXPECT_FALSE(rule->validate(std::any{}, record));

    // Test with valid value
    record["name"] = std::string("John");
    EXPECT_TRUE(rule->validate(record["name"], record));

    // Test error message generation
    EXPECT_FALSE(rule->getErrorMessage().empty());
    EXPECT_EQ(rule->getType(), ValidationType::NOT_NULL);
    EXPECT_EQ(rule->getFieldName(), "name");
}

// Test InListRule functionality
TEST_F(ValidationTest, InListRuleValidation) {
    std::vector<std::string> allowed_values = {"male", "female", "other"};
    auto rule = std::make_unique<InListRule<std::string>>("gender", allowed_values);

    std::unordered_map<std::string, std::any> record;

    // Test with valid value
    record["gender"] = std::string("male");
    EXPECT_TRUE(rule->validate(record["gender"], record));

    // Test with invalid value
    record["gender"] = std::string("invalid");
    EXPECT_FALSE(rule->validate(record["gender"], record));

    // Test with wrong type
    record["gender"] = 123;
    EXPECT_FALSE(rule->validate(record["gender"], record));

    EXPECT_EQ(rule->getType(), ValidationType::IN_LIST);
}

// Test numeric InListRule
TEST_F(ValidationTest, InListRuleNumericValidation) {
    std::vector<int> allowed_values = {1, 2, 3, 4, 5};
    auto rule = std::make_unique<InListRule<int>>("rating", allowed_values);

    std::unordered_map<std::string, std::any> record;

    record["rating"] = 3;
    EXPECT_TRUE(rule->validate(record["rating"], record));

    record["rating"] = 10;
    EXPECT_FALSE(rule->validate(record["rating"], record));
}

// Test DateRangeRule functionality
TEST_F(ValidationTest, DateRangeRuleValidation) {
    auto min_date = std::chrono::system_clock::now() - std::chrono::days(365);
    auto max_date = std::chrono::system_clock::now() + std::chrono::days(365);

    auto rule = std::make_unique<DateRangeRule>("event_date", min_date, max_date);

    std::unordered_map<std::string, std::any> record;

    // Test with valid date
    auto valid_date = std::chrono::system_clock::now();
    record["event_date"] = valid_date;
    EXPECT_TRUE(rule->validate(record["event_date"], record));

    // Test with date too early
    auto early_date = min_date - std::chrono::days(1);
    record["event_date"] = early_date;
    EXPECT_FALSE(rule->validate(record["event_date"], record));

    // Test with date too late
    auto late_date = max_date + std::chrono::days(1);
    record["event_date"] = late_date;
    EXPECT_FALSE(rule->validate(record["event_date"], record));

    EXPECT_EQ(rule->getType(), ValidationType::DATE_RANGE);
}

// Test NumericRangeRule functionality
TEST_F(ValidationTest, NumericRangeRuleValidation) {
    auto rule = std::make_unique<NumericRangeRule<int>>("age", 0, 150);

    std::unordered_map<std::string, std::any> record;

    // Test with valid value
    record["age"] = 25;
    EXPECT_TRUE(rule->validate(record["age"], record));

    // Test with value too low
    record["age"] = -5;
    EXPECT_FALSE(rule->validate(record["age"], record));

    // Test with value too high
    record["age"] = 200;
    EXPECT_FALSE(rule->validate(record["age"], record));

    // Test boundary values
    record["age"] = 0;
    EXPECT_TRUE(rule->validate(record["age"], record));

    record["age"] = 150;
    EXPECT_TRUE(rule->validate(record["age"], record));
}

// Test RegexRule functionality
TEST_F(ValidationTest, RegexRuleValidation) {
    auto rule = std::make_unique<RegexRule>("phone", R"(^\d{3}-\d{3}-\d{4}$)");

    std::unordered_map<std::string, std::any> record;

    // Test with valid phone number
    record["phone"] = std::string("************");
    EXPECT_TRUE(rule->validate(record["phone"], record));

    // Test with invalid phone number
    record["phone"] = std::string("123-45-6789");
    EXPECT_FALSE(rule->validate(record["phone"], record));

    record["phone"] = std::string("not-a-phone");
    EXPECT_FALSE(rule->validate(record["phone"], record));

    EXPECT_EQ(rule->getType(), ValidationType::REGEX);
}

// Test CustomRule functionality
TEST_F(ValidationTest, CustomRuleValidation) {
    auto custom_validator = [](const std::any& value,
                              const std::unordered_map<std::string, std::any>& record) -> bool {
        if (!value.has_value()) return false;
        try {
            int val = std::any_cast<int>(value);
            return val % 2 == 0; // Only even numbers
        } catch (const std::bad_any_cast&) {
            return false;
        }
    };

    auto rule = std::make_unique<CustomRule>("even_number", custom_validator, "Must be even");

    std::unordered_map<std::string, std::any> record;

    record["even_number"] = 4;
    EXPECT_TRUE(rule->validate(record["even_number"], record));

    record["even_number"] = 3;
    EXPECT_FALSE(rule->validate(record["even_number"], record));

    EXPECT_EQ(rule->getType(), ValidationType::CUSTOM);
    EXPECT_EQ(rule->getErrorMessage(), "Must be even");
}

// Test ValidationEngine functionality
TEST_F(ValidationTest, ValidationEngineBasicValidation) {
    engine->addRule(std::make_unique<NotNullRule>("name"));
    engine->addRule(std::make_unique<InListRule<std::string>>("status",
        std::vector<std::string>{"active", "inactive"}));

    std::unordered_map<std::string, std::any> record;
    record["name"] = std::string("John");
    record["status"] = std::string("active");

    auto result = engine->validateRecord(record);

    EXPECT_TRUE(result.is_valid);
    EXPECT_EQ(result.errors.size(), 0);
    EXPECT_EQ(result.records_validated, 1);
    EXPECT_EQ(result.records_failed, 0);
}

// Test ValidationEngine with validation failures
TEST_F(ValidationTest, ValidationEngineWithFailures) {
    engine->addRule(std::make_unique<NotNullRule>("required_field"));
    engine->addRule(std::make_unique<NumericRangeRule<int>>("age", 0, 120));

    std::unordered_map<std::string, std::any> record;
    record["age"] = 150; // Invalid age
    // required_field is missing

    auto result = engine->validateRecord(record);

    EXPECT_FALSE(result.is_valid);
    EXPECT_EQ(result.errors.size(), 2);
    EXPECT_EQ(result.records_validated, 1);
    EXPECT_EQ(result.records_failed, 1);
}

// Test batch validation
TEST_F(ValidationTest, ValidationEngineBatchValidation) {
    engine->addRule(std::make_unique<NotNullRule>("name"));

    std::vector<std::unordered_map<std::string, std::any>> records;

    // Valid record
    std::unordered_map<std::string, std::any> record1;
    record1["name"] = std::string("John");
    records.push_back(record1);

    // Invalid record
    std::unordered_map<std::string, std::any> record2;
    // name is missing
    records.push_back(record2);

    auto result = engine->validateBatch(records, false);

    EXPECT_FALSE(result.is_valid);
    EXPECT_EQ(result.records_validated, 2);
    EXPECT_EQ(result.records_failed, 1);
    EXPECT_GE(result.errors.size(), 1);
    EXPECT_TRUE(result.errors[0].find("Record 1:") != std::string::npos);
}

// Test batch validation with stop on error
TEST_F(ValidationTest, ValidationEngineBatchValidationStopOnError) {
    engine->addRule(std::make_unique<NotNullRule>("name"));

    std::vector<std::unordered_map<std::string, std::any>> records;

    // Invalid record (should stop here)
    std::unordered_map<std::string, std::any> record1;
    records.push_back(record1);

    // This record shouldn't be processed
    std::unordered_map<std::string, std::any> record2;
    record2["name"] = std::string("John");
    records.push_back(record2);

    auto result = engine->validateBatch(records, true);

    EXPECT_FALSE(result.is_valid);
    EXPECT_EQ(result.records_validated, 1);
    EXPECT_EQ(result.records_failed, 1);
}

// Test validation type enabling/disabling
TEST_F(ValidationTest, ValidationEngineTypeManagement) {
    engine->addRule(std::make_unique<NotNullRule>("name"));
    engine->addRule(std::make_unique<RegexRule>("email", R"(^[^@]+@[^@]+\.[^@]+$)"));

    // Disable NOT_NULL validation
    engine->setValidationTypeEnabled(ValidationType::NOT_NULL, false);

    std::unordered_map<std::string, std::any> record;
    record["email"] = std::string("invalid-email");
    // name is missing but NOT_NULL is disabled

    auto result = engine->validateRecord(record);

    EXPECT_FALSE(result.is_valid);
    EXPECT_EQ(result.errors.size(), 1); // Only email validation should fail
}

// Test ValidationRuleFactory
TEST_F(ValidationTest, ValidationRuleFactoryNotNull) {
    std::unordered_map<std::string, std::any> config;
    config["field"] = std::string("test_field");
    config["type"] = std::string("not_null");

    auto rule = ValidationRuleFactory::createRule(config);

    EXPECT_NE(rule, nullptr);
    EXPECT_EQ(rule->getType(), ValidationType::NOT_NULL);
    EXPECT_EQ(rule->getFieldName(), "test_field");
}

// Test ValidationRuleFactory for in_list
TEST_F(ValidationTest, ValidationRuleFactoryInList) {
    std::unordered_map<std::string, std::any> config;
    config["field"] = std::string("status");
    config["type"] = std::string("in_list");
    config["values"] = std::vector<std::string>{"active", "inactive"};

    auto rule = ValidationRuleFactory::createRule(config);

    EXPECT_NE(rule, nullptr);
    EXPECT_EQ(rule->getType(), ValidationType::IN_LIST);
}

// Test ValidationRuleFactory for regex
TEST_F(ValidationTest, ValidationRuleFactoryRegex) {
    std::unordered_map<std::string, std::any> config;
    config["field"] = std::string("email");
    config["type"] = std::string("regex");
    config["pattern"] = std::string(R"(^[^@]+@[^@]+\.[^@]+$)");

    auto rule = ValidationRuleFactory::createRule(config);

    EXPECT_NE(rule, nullptr);
    EXPECT_EQ(rule->getType(), ValidationType::REGEX);
}

// Test ValidationRuleFactory error handling
TEST_F(ValidationTest, ValidationRuleFactoryErrorHandling) {
    std::unordered_map<std::string, std::any> config;
    config["field"] = std::string("test");
    // Missing type

    EXPECT_THROW(ValidationRuleFactory::createRule(config), ConfigurationException);

    config["type"] = std::string("unknown_type");
    EXPECT_THROW(ValidationRuleFactory::createRule(config), ConfigurationException);
}

// Test YAML configuration parsing
TEST_F(ValidationTest, ValidationRuleFactoryYamlConfig) {
    std::string yaml_config = R"(
validation_rules:
  - field: name
    type: not_null
  - field: age
    type: numeric_range
    min: 0
    max: 120
  - field: email
    type: regex
    pattern: "^[^@]+@[^@]+\\.[^@]+$"
)";

    auto engine = ValidationRuleFactory::createEngineFromYaml(yaml_config);

    EXPECT_NE(engine, nullptr);

    std::unordered_map<std::string, std::any> record;
    record["name"] = std::string("John");
    record["age"] = static_cast<int>(25);
    record["email"] = std::string("<EMAIL>");

    auto result = engine->validateRecord(record);
    EXPECT_TRUE(result.is_valid);

    // Test with invalid data
    std::unordered_map<std::string, std::any> invalid_record;
    invalid_record["age"] = static_cast<int>(150); // Too old
    invalid_record["email"] = std::string("invalid-email"); // Invalid format
    // name is missing (not_null violation)

    auto invalid_result = engine->validateRecord(invalid_record);
    EXPECT_FALSE(invalid_result.is_valid);
    EXPECT_GE(invalid_result.errors.size(), 2); // At least 2 validation errors
}

// Test ValidationUtils functions
TEST_F(ValidationTest, ValidationUtilsEmailValidation) {
    EXPECT_TRUE(ValidationUtils::isValidEmail("<EMAIL>"));
    EXPECT_TRUE(ValidationUtils::isValidEmail("<EMAIL>"));
    EXPECT_FALSE(ValidationUtils::isValidEmail("invalid-email"));
    EXPECT_FALSE(ValidationUtils::isValidEmail("@domain.com"));
    EXPECT_FALSE(ValidationUtils::isValidEmail("user@"));
}

// Test phone number validation
TEST_F(ValidationTest, ValidationUtilsPhoneValidation) {
    EXPECT_TRUE(ValidationUtils::isValidPhoneNumber("(*************", "US"));
    EXPECT_TRUE(ValidationUtils::isValidPhoneNumber("************", "US"));
    EXPECT_TRUE(ValidationUtils::isValidPhoneNumber("5551234567", "US"));
    EXPECT_FALSE(ValidationUtils::isValidPhoneNumber("123-456", "US"));

    EXPECT_TRUE(ValidationUtils::isValidPhoneNumber("07123456789", "UK"));
    EXPECT_FALSE(ValidationUtils::isValidPhoneNumber("123456", "UK"));
}

// Test postal code validation
TEST_F(ValidationTest, ValidationUtilsPostalCodeValidation) {
    // US ZIP codes
    EXPECT_TRUE(ValidationUtils::isValidPostalCode("12345", "US"));
    EXPECT_TRUE(ValidationUtils::isValidPostalCode("12345-6789", "US"));
    EXPECT_FALSE(ValidationUtils::isValidPostalCode("1234", "US"));

    // Canadian postal codes
    EXPECT_TRUE(ValidationUtils::isValidPostalCode("K1A 0A6", "CA"));
    EXPECT_TRUE(ValidationUtils::isValidPostalCode("K1A0A6", "CA"));
    EXPECT_FALSE(ValidationUtils::isValidPostalCode("123456", "CA"));

    // UK postcodes
    EXPECT_TRUE(ValidationUtils::isValidPostalCode("SW1A 1AA", "UK"));
    EXPECT_TRUE(ValidationUtils::isValidPostalCode("M1 1AA", "UK"));
    EXPECT_FALSE(ValidationUtils::isValidPostalCode("123456", "UK"));
}

// Test UUID validation
TEST_F(ValidationTest, ValidationUtilsUUIDValidation) {
    EXPECT_TRUE(ValidationUtils::isValidUUID("550e8400-e29b-41d4-a716-************"));
    EXPECT_TRUE(ValidationUtils::isValidUUID("6ba7b810-9dad-11d1-80b4-00c04fd430c8"));
    EXPECT_FALSE(ValidationUtils::isValidUUID("not-a-uuid"));
    EXPECT_FALSE(ValidationUtils::isValidUUID("550e8400-e29b-41d4-a716"));
}

// Test URL validation
TEST_F(ValidationTest, ValidationUtilsURLValidation) {
    EXPECT_TRUE(ValidationUtils::isValidURL("https://www.example.com"));
    EXPECT_TRUE(ValidationUtils::isValidURL("http://example.com/path"));
    // Note: FTP may not be supported by the validation implementation
    // EXPECT_TRUE(ValidationUtils::isValidURL("ftp://files.example.com"));
    EXPECT_FALSE(ValidationUtils::isValidURL("not-a-url"));
    EXPECT_FALSE(ValidationUtils::isValidURL("http://"));
}

// Test string sanitization
TEST_F(ValidationTest, ValidationUtilsSanitization) {
    std::string input = "  \t\nHello\x01World\x7F  \r\n  ";
    std::string sanitized = ValidationUtils::sanitizeString(input);

    // The sanitization may remove control characters and trim whitespace
    EXPECT_TRUE(sanitized.find("Hello") != std::string::npos);
    EXPECT_TRUE(sanitized.find("World") != std::string::npos);
    // Allow for different sanitization implementations

    std::string empty_input = "   \t\n\r   ";
    EXPECT_TRUE(ValidationUtils::sanitizeString(empty_input).empty());
}

} // namespace omop::common::test


File tests/unit/common/utilities_test.cpp:

// Test file: tests/unit/common/test_utilities.cpp
/**
 * Unit tests for OMOP utility functions
 */

#include <gtest/gtest.h>
#include "common/utilities.h"
#include <fstream>
#include <filesystem>
#include <chrono>
#include <thread>

namespace omop::common::test {

class UtilitiesTest : public ::testing::Test {
protected:
    void SetUp() override {
        temp_dir = std::filesystem::temp_directory_path() / "omop_test";
        std::filesystem::create_directories(temp_dir);
    }

    void TearDown() override {
        if (std::filesystem::exists(temp_dir)) {
            std::filesystem::remove_all(temp_dir);
        }
    }

    std::filesystem::path temp_dir;
};

// StringUtils Tests
TEST_F(UtilitiesTest, StringUtilsTrim) {
    EXPECT_EQ(StringUtils::trim("  hello  "), "hello");
    EXPECT_EQ(StringUtils::trim("\t\ntest\r\n"), "test");
    EXPECT_EQ(StringUtils::trim(""), "");
    EXPECT_EQ(StringUtils::trim("   "), "");
    EXPECT_EQ(StringUtils::trim("no-trim"), "no-trim");
}

// Test string case conversion
TEST_F(UtilitiesTest, StringUtilsCaseConversion) {
    EXPECT_EQ(StringUtils::to_lower("HELLO World"), "hello world");
    EXPECT_EQ(StringUtils::to_upper("hello World"), "HELLO WORLD");
    EXPECT_EQ(StringUtils::to_lower(""), "");
    EXPECT_EQ(StringUtils::to_upper(""), "");
    EXPECT_EQ(StringUtils::to_lower("123!@#"), "123!@#");
}

// Test string splitting
TEST_F(UtilitiesTest, StringUtilsSplit) {
    auto result = StringUtils::split("a,b,c", ',');
    EXPECT_EQ(result.size(), 3);
    EXPECT_EQ(result[0], "a");
    EXPECT_EQ(result[1], "b");
    EXPECT_EQ(result[2], "c");

    auto result2 = StringUtils::split("hello::world::test", "::");
    EXPECT_EQ(result2.size(), 3);
    EXPECT_EQ(result2[0], "hello");
    EXPECT_EQ(result2[1], "world");
    EXPECT_EQ(result2[2], "test");

    auto empty_result = StringUtils::split("", ',');
    EXPECT_TRUE(empty_result.empty());
}

// Test string joining
TEST_F(UtilitiesTest, StringUtilsJoin) {
    std::vector<std::string> parts = {"a", "b", "c"};
    EXPECT_EQ(StringUtils::join(parts, ","), "a,b,c");
    EXPECT_EQ(StringUtils::join(parts, " - "), "a - b - c");

    std::vector<std::string> empty_parts;
    EXPECT_EQ(StringUtils::join(empty_parts, ","), "");

    std::vector<std::string> single_part = {"alone"};
    EXPECT_EQ(StringUtils::join(single_part, ","), "alone");
}

// Test string replacement
TEST_F(UtilitiesTest, StringUtilsReplaceAll) {
    EXPECT_EQ(StringUtils::replace_all("hello world hello", "hello", "hi"), "hi world hi");
    EXPECT_EQ(StringUtils::replace_all("test", "missing", "new"), "test");
    EXPECT_EQ(StringUtils::replace_all("", "any", "thing"), "");
    EXPECT_EQ(StringUtils::replace_all("test", "", "new"), "test");
}

// Test string prefix/suffix checking
TEST_F(UtilitiesTest, StringUtilsPrefixSuffix) {
    EXPECT_TRUE(StringUtils::starts_with("hello world", "hello"));
    EXPECT_FALSE(StringUtils::starts_with("hello world", "world"));
    EXPECT_TRUE(StringUtils::starts_with("test", "test"));
    EXPECT_FALSE(StringUtils::starts_with("test", "testing"));

    EXPECT_TRUE(StringUtils::ends_with("hello world", "world"));
    EXPECT_FALSE(StringUtils::ends_with("hello world", "hello"));
    EXPECT_TRUE(StringUtils::ends_with("test", "test"));
    EXPECT_FALSE(StringUtils::ends_with("test", "testing"));

    EXPECT_TRUE(StringUtils::contains("hello world", "lo wo"));
    EXPECT_FALSE(StringUtils::contains("hello world", "xyz"));
}

// Test case conversion functions
TEST_F(UtilitiesTest, StringUtilsCaseStyles) {
    EXPECT_EQ(StringUtils::to_snake_case("CamelCase"), "camel_case");
    EXPECT_EQ(StringUtils::to_snake_case("XMLHttpRequest"), "xmlhttp_request");
    EXPECT_EQ(StringUtils::to_snake_case("already_snake"), "already_snake");

    EXPECT_EQ(StringUtils::to_camel_case("snake_case"), "snakeCase");
    EXPECT_EQ(StringUtils::to_camel_case("multiple_words_here"), "multipleWordsHere");
    EXPECT_EQ(StringUtils::to_camel_case("single"), "single");
}

// Test SQL escaping
TEST_F(UtilitiesTest, StringUtilsSqlEscape) {
    EXPECT_EQ(StringUtils::escape_sql("O'Reilly"), "O''Reilly");
    EXPECT_EQ(StringUtils::escape_sql("\"quoted\""), "\\\"quoted\\\"");
    EXPECT_EQ(StringUtils::escape_sql("line\nbreak"), "line\\nbreak");
    EXPECT_EQ(StringUtils::escape_sql("tab\there"), "tab\\there");
    EXPECT_EQ(StringUtils::escape_sql("normal text"), "normal text");
}

// Test random string generation
TEST_F(UtilitiesTest, StringUtilsRandomString) {
    auto str1 = StringUtils::random_string(10);
    auto str2 = StringUtils::random_string(10);

    EXPECT_EQ(str1.length(), 10);
    EXPECT_EQ(str2.length(), 10);
    EXPECT_NE(str1, str2); // Should be different (probabilistically)

    auto empty_str = StringUtils::random_string(0);
    EXPECT_TRUE(empty_str.empty());
}

// DateTimeUtils Tests
TEST_F(UtilitiesTest, DateTimeUtilsParseDate) {
    auto parsed = DateTimeUtils::parse_date("2023-12-25", "%Y-%m-%d");
    EXPECT_TRUE(parsed.has_value());

    auto invalid = DateTimeUtils::parse_date("invalid-date", "%Y-%m-%d");
    EXPECT_FALSE(invalid.has_value());

    auto custom_format = DateTimeUtils::parse_date("25/12/2023", "%d/%m/%Y");
    EXPECT_TRUE(custom_format.has_value());
}

// Test date formatting
TEST_F(UtilitiesTest, DateTimeUtilsFormatDate) {
    auto now = std::chrono::system_clock::now();
    auto formatted = DateTimeUtils::format_date(now, "%Y-%m-%d");
    EXPECT_FALSE(formatted.empty());
    EXPECT_EQ(formatted.length(), 10); // YYYY-MM-DD format

    auto with_time = DateTimeUtils::format_date(now, "%Y-%m-%d %H:%M:%S");
    EXPECT_EQ(with_time.length(), 19); // YYYY-MM-DD HH:MM:SS format
}

// Test current timestamp
TEST_F(UtilitiesTest, DateTimeUtilsCurrentTimestamp) {
    auto timestamp1 = DateTimeUtils::current_timestamp();
    std::this_thread::sleep_for(std::chrono::milliseconds(1100)); // Sleep longer to ensure different seconds
    auto timestamp2 = DateTimeUtils::current_timestamp();

    EXPECT_NE(timestamp1, timestamp2);
    EXPECT_EQ(timestamp1.length(), 19); // Default format length
}

// Test age calculation
TEST_F(UtilitiesTest, DateTimeUtilsCalculateAge) {
    auto now = std::chrono::system_clock::now();
    auto birth_date = DateTimeUtils::add_duration(now, -365 * 25, 0, 0); // 25 years ago

    int age = DateTimeUtils::calculate_age(birth_date, now);
    EXPECT_GE(age, 24); // Allow for 24 or 25 due to leap years and date precision
    EXPECT_LE(age, 25);

    // Test edge case - birthday not yet reached this year
    auto almost_birthday = DateTimeUtils::add_duration(now, -365 * 25 + 10, 0, 0);
    int age_not_reached = DateTimeUtils::calculate_age(almost_birthday, now);
    EXPECT_GE(age_not_reached, 23); // Allow for some variance
    EXPECT_LE(age_not_reached, 25);
}

// Test date validation
TEST_F(UtilitiesTest, DateTimeUtilsDateValidation) {
    EXPECT_TRUE(DateTimeUtils::is_valid_date(2023, 12, 25));
    EXPECT_TRUE(DateTimeUtils::is_valid_date(2024, 2, 29)); // Leap year
    EXPECT_FALSE(DateTimeUtils::is_valid_date(2023, 2, 29)); // Not leap year
    EXPECT_FALSE(DateTimeUtils::is_valid_date(2023, 13, 1)); // Invalid month
    EXPECT_FALSE(DateTimeUtils::is_valid_date(2023, 4, 31)); // Invalid day for April
    EXPECT_FALSE(DateTimeUtils::is_valid_date(2023, 1, 0)); // Invalid day
}

// FileUtils Tests
TEST_F(UtilitiesTest, FileUtilsFileOperations) {
    auto test_file = temp_dir / "test.txt";
    std::string content = "Hello, World!";

    // Test writing
    EXPECT_TRUE(FileUtils::write_file(test_file.string(), content));
    EXPECT_TRUE(FileUtils::file_exists(test_file.string()));

    // Test reading
    auto read_content = FileUtils::read_file(test_file.string());
    EXPECT_TRUE(read_content.has_value());
    EXPECT_EQ(read_content.value(), content);

    // Test file size
    auto size = FileUtils::file_size(test_file.string());
    EXPECT_TRUE(size.has_value());
    EXPECT_EQ(size.value(), content.length());

    // Test append
    EXPECT_TRUE(FileUtils::write_file(test_file.string(), "\nAppended", true));
    auto appended_content = FileUtils::read_file(test_file.string());
    EXPECT_EQ(appended_content.value(), content + "\nAppended");
}

// Test file path operations
TEST_F(UtilitiesTest, FileUtilsPathOperations) {
    std::string filepath = "/path/to/file.txt";

    EXPECT_EQ(FileUtils::get_extension(filepath), ".txt");
    EXPECT_EQ(FileUtils::get_basename(filepath), "file");
    EXPECT_EQ(FileUtils::get_directory(filepath), "/path/to");

    std::string no_ext = "/path/to/file";
    EXPECT_TRUE(FileUtils::get_extension(no_ext).empty());
}

// Test directory operations
TEST_F(UtilitiesTest, FileUtilsDirectoryOperations) {
    auto new_dir = temp_dir / "new_directory";

    EXPECT_TRUE(FileUtils::create_directory(new_dir.string()));
    EXPECT_TRUE(std::filesystem::exists(new_dir));

    // Create some test files
    FileUtils::write_file((new_dir / "file1.txt").string(), "content1");
    FileUtils::write_file((new_dir / "file2.log").string(), "content2");
    FileUtils::write_file((new_dir / "file3.txt").string(), "content3");

    auto txt_files = FileUtils::list_files(new_dir.string(), ".*\\.txt");
    EXPECT_EQ(txt_files.size(), 2);

    auto all_files = FileUtils::list_files(new_dir.string());
    EXPECT_EQ(all_files.size(), 3);
}

// Test file copy and move operations
TEST_F(UtilitiesTest, FileUtilsCopyMoveOperations) {
    auto source = temp_dir / "source.txt";
    auto destination = temp_dir / "destination.txt";
    auto move_dest = temp_dir / "moved.txt";

    std::string content = "Test content";
    FileUtils::write_file(source.string(), content);

    // Test copy
    EXPECT_TRUE(FileUtils::copy_file(source.string(), destination.string()));
    EXPECT_TRUE(FileUtils::file_exists(source.string()));
    EXPECT_TRUE(FileUtils::file_exists(destination.string()));

    auto copied_content = FileUtils::read_file(destination.string());
    EXPECT_EQ(copied_content.value(), content);

    // Test move
    EXPECT_TRUE(FileUtils::move_file(destination.string(), move_dest.string()));
    EXPECT_FALSE(FileUtils::file_exists(destination.string()));
    EXPECT_TRUE(FileUtils::file_exists(move_dest.string()));

    // Test delete
    EXPECT_TRUE(FileUtils::delete_file(move_dest.string()));
    EXPECT_FALSE(FileUtils::file_exists(move_dest.string()));
}

// Test temporary file operations
TEST_F(UtilitiesTest, FileUtilsTemporaryOperations) {
    auto temp_dir_path = FileUtils::get_temp_directory();
    EXPECT_FALSE(temp_dir_path.empty());

    auto temp_file = FileUtils::create_temp_file("test_prefix", ".tmp");
    EXPECT_TRUE(temp_file.find("test_prefix") != std::string::npos);
    EXPECT_TRUE(temp_file.find(".tmp") != std::string::npos);
}

// SystemUtils Tests
TEST_F(UtilitiesTest, SystemUtilsEnvironmentVariables) {
    std::string test_var = "OMOP_TEST_VAR";
    std::string test_value = "test_value_123";

    // Test setting environment variable
    EXPECT_TRUE(SystemUtils::set_env(test_var, test_value));

    // Test getting environment variable
    auto retrieved = SystemUtils::get_env(test_var);
    EXPECT_TRUE(retrieved.has_value());
    EXPECT_EQ(retrieved.value(), test_value);

    // Test non-existent variable
    auto non_existent = SystemUtils::get_env("NON_EXISTENT_VAR_12345");
    EXPECT_FALSE(non_existent.has_value());
}

// Test system information
TEST_F(UtilitiesTest, SystemUtilsSystemInfo) {
    EXPECT_FALSE(SystemUtils::get_current_directory().empty());
    EXPECT_FALSE(SystemUtils::get_home_directory().empty());
    EXPECT_GT(SystemUtils::get_cpu_count(), 0);
    EXPECT_GE(SystemUtils::get_available_memory(), 0);
    EXPECT_GT(SystemUtils::get_process_id(), 0);
    EXPECT_FALSE(SystemUtils::get_hostname().empty());
    EXPECT_FALSE(SystemUtils::get_username().empty());
}

// Test command execution
TEST_F(UtilitiesTest, SystemUtilsCommandExecution) {
    auto result = SystemUtils::execute_command("echo 'test output'");
    EXPECT_TRUE(result.has_value());
    EXPECT_TRUE(result.value().find("test output") != std::string::npos);

    // Test invalid command
    auto invalid = SystemUtils::execute_command("invalid_command_xyz_123");
    // Behavior may vary by system, but should not crash
}

// CryptoUtils Tests
TEST_F(UtilitiesTest, CryptoUtilsHashing) {
    std::string input = "Hello, World!";

    auto md5_hash = CryptoUtils::md5(input);
    EXPECT_EQ(md5_hash.length(), 32); // MD5 produces 32 hex characters
    EXPECT_NE(md5_hash, input);

    auto sha256_hash = CryptoUtils::sha256(input);
    EXPECT_EQ(sha256_hash.length(), 64); // SHA256 produces 64 hex characters
    EXPECT_NE(sha256_hash, input);

    // Same input should produce same hash
    EXPECT_EQ(CryptoUtils::md5(input), CryptoUtils::md5(input));
    EXPECT_EQ(CryptoUtils::sha256(input), CryptoUtils::sha256(input));
}

// Test Base64 encoding/decoding
TEST_F(UtilitiesTest, CryptoUtilsBase64) {
    std::string input = "Hello, World!";
    std::vector<uint8_t> data(input.begin(), input.end());

    auto encoded = CryptoUtils::base64_encode(data);
    EXPECT_FALSE(encoded.empty());
    EXPECT_NE(encoded, input);

    auto decoded = CryptoUtils::base64_decode(encoded);
    std::string decoded_str(decoded.begin(), decoded.end());
    EXPECT_EQ(decoded_str, input);

    // Test empty input
    std::vector<uint8_t> empty_data;
    auto empty_encoded = CryptoUtils::base64_encode(empty_data);
    auto empty_decoded = CryptoUtils::base64_decode(empty_encoded);
    EXPECT_TRUE(empty_decoded.empty());
}

// Test UUID generation
TEST_F(UtilitiesTest, CryptoUtilsUuidGeneration) {
    auto uuid1 = CryptoUtils::generate_uuid();
    auto uuid2 = CryptoUtils::generate_uuid();

    EXPECT_EQ(uuid1.length(), 36); // Standard UUID length
    EXPECT_EQ(uuid2.length(), 36);
    EXPECT_NE(uuid1, uuid2); // Should be unique

    // Check UUID format (8-4-4-4-12)
    EXPECT_EQ(uuid1[8], '-');
    EXPECT_EQ(uuid1[13], '-');
    EXPECT_EQ(uuid1[18], '-');
    EXPECT_EQ(uuid1[23], '-');
}

// Test random bytes generation
TEST_F(UtilitiesTest, CryptoUtilsRandomBytes) {
    auto bytes1 = CryptoUtils::random_bytes(16);
    auto bytes2 = CryptoUtils::random_bytes(16);

    EXPECT_EQ(bytes1.size(), 16);
    EXPECT_EQ(bytes2.size(), 16);
    EXPECT_NE(bytes1, bytes2); // Should be different

    auto empty_bytes = CryptoUtils::random_bytes(0);
    EXPECT_TRUE(empty_bytes.empty());
}

// ValidationUtils Tests
TEST_F(UtilitiesTest, ValidationUtilsEmailValidation) {
    EXPECT_TRUE(ValidationUtils::is_valid_email("<EMAIL>"));
    EXPECT_TRUE(ValidationUtils::is_valid_email("<EMAIL>"));
    EXPECT_TRUE(ValidationUtils::is_valid_email("<EMAIL>"));

    EXPECT_FALSE(ValidationUtils::is_valid_email("invalid-email"));
    EXPECT_FALSE(ValidationUtils::is_valid_email("@domain.com"));
    EXPECT_FALSE(ValidationUtils::is_valid_email("user@"));
    EXPECT_FALSE(ValidationUtils::is_valid_email("user@@domain.com"));
    EXPECT_FALSE(ValidationUtils::is_valid_email(""));
}

// Test URL validation
TEST_F(UtilitiesTest, ValidationUtilsUrlValidation) {
    EXPECT_TRUE(ValidationUtils::is_valid_url("https://www.example.com"));
    EXPECT_TRUE(ValidationUtils::is_valid_url("http://example.com/path"));
    EXPECT_TRUE(ValidationUtils::is_valid_url("https://subdomain.example.org:8080/path"));

    EXPECT_FALSE(ValidationUtils::is_valid_url("not-a-url"));
    EXPECT_FALSE(ValidationUtils::is_valid_url("http://"));
    EXPECT_FALSE(ValidationUtils::is_valid_url(""));
}

// Test IP address validation
TEST_F(UtilitiesTest, ValidationUtilsIpValidation) {
    // IPv4 addresses
    EXPECT_TRUE(ValidationUtils::is_valid_ip("***********"));
    EXPECT_TRUE(ValidationUtils::is_valid_ip("********"));
    EXPECT_TRUE(ValidationUtils::is_valid_ip("***************"));
    EXPECT_TRUE(ValidationUtils::is_valid_ip("0.0.0.0"));

    EXPECT_FALSE(ValidationUtils::is_valid_ip("256.1.1.1"));
    EXPECT_FALSE(ValidationUtils::is_valid_ip("192.168.1"));
    EXPECT_FALSE(ValidationUtils::is_valid_ip("***********.1"));

    // IPv6 addresses (simplified test)
    EXPECT_TRUE(ValidationUtils::is_valid_ip("::1"));
    EXPECT_TRUE(ValidationUtils::is_valid_ip("2001:0db8:85a3:0000:0000:8a2e:0370:7334"));
}

// Test date format validation
TEST_F(UtilitiesTest, ValidationUtilsDateFormatValidation) {
    EXPECT_TRUE(ValidationUtils::is_valid_date_format("2023-12-25", "%Y-%m-%d"));
    EXPECT_TRUE(ValidationUtils::is_valid_date_format("25/12/2023", "%d/%m/%Y"));
    EXPECT_TRUE(ValidationUtils::is_valid_date_format("Dec 25, 2023", "%b %d, %Y"));

    EXPECT_FALSE(ValidationUtils::is_valid_date_format("invalid-date", "%Y-%m-%d"));
    EXPECT_FALSE(ValidationUtils::is_valid_date_format("2023-13-25", "%Y-%m-%d"));
    EXPECT_FALSE(ValidationUtils::is_valid_date_format("", "%Y-%m-%d"));
}

// Test JSON validation
TEST_F(UtilitiesTest, ValidationUtilsJsonValidation) {
    EXPECT_TRUE(ValidationUtils::is_valid_json("{}"));
    EXPECT_TRUE(ValidationUtils::is_valid_json("{\"key\": \"value\"}"));
    EXPECT_TRUE(ValidationUtils::is_valid_json("[1, 2, 3]"));
    EXPECT_TRUE(ValidationUtils::is_valid_json("\"string\""));
    EXPECT_TRUE(ValidationUtils::is_valid_json("123"));
    EXPECT_TRUE(ValidationUtils::is_valid_json("true"));

    EXPECT_FALSE(ValidationUtils::is_valid_json("{invalid json}"));
    EXPECT_FALSE(ValidationUtils::is_valid_json("{\"key\": }"));
    EXPECT_FALSE(ValidationUtils::is_valid_json(""));
}

// Test SQL identifier validation
TEST_F(UtilitiesTest, ValidationUtilsSqlIdentifierValidation) {
    EXPECT_TRUE(ValidationUtils::is_valid_sql_identifier("valid_name"));
    EXPECT_TRUE(ValidationUtils::is_valid_sql_identifier("_underscore_start"));
    EXPECT_TRUE(ValidationUtils::is_valid_sql_identifier("name123"));
    EXPECT_TRUE(ValidationUtils::is_valid_sql_identifier("CamelCase"));

    EXPECT_FALSE(ValidationUtils::is_valid_sql_identifier("123invalid"));
    EXPECT_FALSE(ValidationUtils::is_valid_sql_identifier("invalid-hyphen"));
    EXPECT_FALSE(ValidationUtils::is_valid_sql_identifier("invalid space"));
    EXPECT_FALSE(ValidationUtils::is_valid_sql_identifier("SELECT")); // Reserved word
    EXPECT_FALSE(ValidationUtils::is_valid_sql_identifier(""));
}

// PerformanceUtils Tests
TEST_F(UtilitiesTest, PerformanceUtilsTimer) {
    PerformanceUtils::Timer timer;

    std::this_thread::sleep_for(std::chrono::milliseconds(10));

    auto elapsed_ms = timer.elapsed_milliseconds();
    auto elapsed_s = timer.elapsed_seconds();

    EXPECT_GE(elapsed_ms, 10.0);
    EXPECT_GE(elapsed_s, 0.01);

    timer.reset();
    auto reset_elapsed = timer.elapsed_milliseconds();
    EXPECT_LT(reset_elapsed, elapsed_ms);
}

// Test memory tracker
TEST_F(UtilitiesTest, PerformanceUtilsMemoryTracker) {
    PerformanceUtils::MemoryTracker tracker;

    // Allocate some memory
    std::vector<int> large_vector(10000, 42);

    auto current_usage = tracker.current_usage();
    auto peak_usage = tracker.peak_usage();

    // Memory usage should be measurable
    EXPECT_GE(peak_usage, current_usage);

    tracker.reset();
    auto reset_usage = tracker.current_usage();
    // After reset, usage should be minimal
    EXPECT_LE(reset_usage, current_usage);
}

// Test formatting functions
TEST_F(UtilitiesTest, PerformanceUtilsFormatting) {
    EXPECT_EQ(PerformanceUtils::format_bytes(1024), "1.00 KB");
    EXPECT_EQ(PerformanceUtils::format_bytes(1024 * 1024), "1.00 MB");
    EXPECT_EQ(PerformanceUtils::format_bytes(1024 * 1024 * 1024), "1.00 GB");
    EXPECT_EQ(PerformanceUtils::format_bytes(500), "500.00 B");

    EXPECT_EQ(PerformanceUtils::format_duration(0.5), "500ms");
    EXPECT_EQ(PerformanceUtils::format_duration(65), "1m 5s");
    EXPECT_EQ(PerformanceUtils::format_duration(3665), "1h 1m 5s");

    EXPECT_DOUBLE_EQ(PerformanceUtils::calculate_throughput(1000, 10.0), 100.0);
    EXPECT_DOUBLE_EQ(PerformanceUtils::calculate_throughput(100, 0), 0.0);
}

// ProcessingUtils Tests
TEST_F(UtilitiesTest, ProcessingUtilsStageNames) {
    EXPECT_EQ(ProcessingUtils::stage_name(0), "Extract");
    EXPECT_EQ(ProcessingUtils::stage_name(1), "Transform");
    EXPECT_EQ(ProcessingUtils::stage_name(2), "Load");
    EXPECT_EQ(ProcessingUtils::stage_name(999), "Unknown");
}

} // namespace omop::common::test

File tests/unit/common/configuration_test.cpp:

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "common/configuration.h"
#include <yaml-cpp/yaml.h>
#include <fstream>
#include <filesystem>
#include <format>

using namespace omop::common;
using namespace testing;

namespace omop::common::test {

// Test fixture for TransformationRule tests
class TransformationRuleTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create basic YAML nodes for testing
        basic_rule_yaml_ = YAML::Load(R"(
            source_column: "patient_id"
            target_column: "person_id"
            type: "direct"
        )");

        multi_column_rule_yaml_ = YAML::Load(R"(
            source_columns: ["first_name", "last_name"]
            target_column: "full_name"
            type: "string_concatenation"
            separator: " "
        )");

        vocab_mapping_yaml_ = YAML::Load(R"(
            source_column: "gender_code"
            target_column: "gender_concept_id"
            type: "vocabulary_mapping"
            vocabulary: "gender_vocab"
            default_value: 0
        )");
    }

    YAML::Node basic_rule_yaml_;
    YAML::Node multi_column_rule_yaml_;
    YAML::Node vocab_mapping_yaml_;
};

// Tests for TransformationRule constructor and basic functionality
// Tests basic transformation rule construction with direct mapping
TEST_F(TransformationRuleTest, ConstructorWithBasicRule) {
    TransformationRule rule(basic_rule_yaml_);

    EXPECT_EQ(rule.source_column(), "patient_id");
    EXPECT_EQ(rule.target_column(), "person_id");
    EXPECT_EQ(rule.type(), TransformationRule::Type::Direct);
    EXPECT_FALSE(rule.is_multi_column());
    EXPECT_TRUE(rule.source_columns().empty());
}

// Tests transformation rule construction with multiple source columns
TEST_F(TransformationRuleTest, ConstructorWithMultiColumnRule) {
    TransformationRule rule(multi_column_rule_yaml_);

    EXPECT_TRUE(rule.is_multi_column());
    EXPECT_EQ(rule.source_columns().size(), 2);
    EXPECT_EQ(rule.source_columns()[0], "first_name");
    EXPECT_EQ(rule.source_columns()[1], "last_name");
    EXPECT_EQ(rule.target_column(), "full_name");
    EXPECT_EQ(rule.type(), TransformationRule::Type::StringConcatenation);
}

// Tests transformation rule construction with vocabulary mapping parameters
TEST_F(TransformationRuleTest, ConstructorWithVocabularyMapping) {
    TransformationRule rule(vocab_mapping_yaml_);

    EXPECT_EQ(rule.source_column(), "gender_code");
    EXPECT_EQ(rule.target_column(), "gender_concept_id");
    EXPECT_EQ(rule.type(), TransformationRule::Type::VocabularyMapping);

    // Check parameters
    const auto& params = rule.parameters();
    EXPECT_TRUE(params["vocabulary"]);
    EXPECT_EQ(params["vocabulary"].as<std::string>(), "gender_vocab");
    EXPECT_TRUE(params["default_value"]);
    EXPECT_EQ(params["default_value"].as<int>(), 0);
}

// Tests that constructor throws exception when source column is missing
TEST_F(TransformationRuleTest, ConstructorThrowsOnMissingSourceColumn) {
    YAML::Node invalid_yaml = YAML::Load(R"(
        target_column: "person_id"
        type: "direct"
    )");

    EXPECT_THROW(TransformationRule rule(invalid_yaml), ConfigurationException);
}

// Tests that constructor throws exception when target column is missing
TEST_F(TransformationRuleTest, ConstructorThrowsOnMissingTargetColumn) {
    YAML::Node invalid_yaml = YAML::Load(R"(
        source_column: "patient_id"
        type: "direct"
    )");

    EXPECT_THROW(TransformationRule rule(invalid_yaml), ConfigurationException);
}

// Tests that constructor throws exception when transformation type is invalid
TEST_F(TransformationRuleTest, ConstructorThrowsOnInvalidType) {
    YAML::Node invalid_yaml = YAML::Load(R"(
        source_column: "patient_id"
        target_column: "person_id"
        type: "invalid_type"
    )");

    EXPECT_THROW(TransformationRule rule(invalid_yaml), ConfigurationException);
}

// Tests all supported transformation types are correctly parsed
TEST_F(TransformationRuleTest, AllTransformationTypes) {
    std::vector<std::pair<std::string, TransformationRule::Type>> type_mappings = {
        {"direct", TransformationRule::Type::Direct},
        {"date_transform", TransformationRule::Type::DateTransform},
        {"vocabulary_mapping", TransformationRule::Type::VocabularyMapping},
        {"date_calculation", TransformationRule::Type::DateCalculation},
        {"numeric_transform", TransformationRule::Type::NumericTransform},
        {"string_concatenation", TransformationRule::Type::StringConcatenation},
        {"conditional", TransformationRule::Type::Conditional},
        {"custom", TransformationRule::Type::Custom}
    };

    for (const auto& [type_str, expected_type] : type_mappings) {
        YAML::Node yaml = YAML::Load(std::format(R"(
            source_column: "test_col"
            target_column: "target_col"
            type: "{}"
        )", type_str));

        TransformationRule rule(yaml);
        EXPECT_EQ(rule.type(), expected_type) << "Failed for type: " << type_str;
    }
}

// Test fixture for TableMapping tests
class TableMappingTest : public ::testing::Test {
protected:
    void SetUp() override {
        basic_mapping_yaml_ = YAML::Load(R"(
            source_table: "patients"
            target_table: "person"
            transformations:
              - source_column: "patient_id"
                target_column: "person_id"
                type: "direct"
              - source_column: "birth_date"
                target_column: "birth_datetime"
                type: "date_transform"
                format: "YYYY-MM-DD"
        )");
    }

    YAML::Node basic_mapping_yaml_;
};

// Tests basic table mapping construction with transformations
TEST_F(TableMappingTest, ConstructorWithBasicMapping) {
    TableMapping mapping(basic_mapping_yaml_);

    EXPECT_EQ(mapping.source_table(), "patients");
    EXPECT_EQ(mapping.target_table(), "person");
    EXPECT_EQ(mapping.transformations().size(), 2);
    EXPECT_FALSE(mapping.pre_process_sql().has_value());
    EXPECT_FALSE(mapping.post_process_sql().has_value());
}

// Tests that constructor throws exception when source table is missing
TEST_F(TableMappingTest, ConstructorThrowsOnMissingSourceTable) {
    YAML::Node invalid_yaml = YAML::Load(R"(
        target_table: "person"
        transformations: []
    )");

    EXPECT_THROW(TableMapping mapping(invalid_yaml), ConfigurationException);
}

// Tests that constructor throws exception when target table is missing
TEST_F(TableMappingTest, ConstructorThrowsOnMissingTargetTable) {
    YAML::Node invalid_yaml = YAML::Load(R"(
        source_table: "patients"
        transformations: []
    )");

    EXPECT_THROW(TableMapping mapping(invalid_yaml), ConfigurationException);
}

// Test fixture for DatabaseConfig tests
class DatabaseConfigTest : public ::testing::Test {
protected:
    void SetUp() override {
        postgresql_config_yaml_ = YAML::Load(R"(
            type: "postgresql"
            host: "localhost"
            port: 5432
            database: "omop_cdm"
            username: "postgres"
            password: "password123"
            parameters:
              sslmode: "require"
              connect_timeout: "30"
        )");

        connection_string_config_yaml_ = YAML::Load(R"(
            type: "mysql"
            connection_string: "mysql://user:pass@localhost:3306/omop_db"
        )");
    }

    YAML::Node postgresql_config_yaml_;
    YAML::Node connection_string_config_yaml_;
};

// Tests database config construction with individual connection parameters
TEST_F(DatabaseConfigTest, ConstructorWithIndividualParameters) {
    DatabaseConfig config(postgresql_config_yaml_);

    EXPECT_EQ(config.type(), DatabaseConfig::Type::PostgreSQL);
    EXPECT_EQ(config.host(), "localhost");
    EXPECT_EQ(config.port(), 5432);
    EXPECT_EQ(config.database(), "omop_cdm");
    EXPECT_EQ(config.username(), "postgres");
    EXPECT_EQ(config.password(), "password123");

    const auto& params = config.parameters();
    EXPECT_EQ(params.at("sslmode"), "require");
    EXPECT_EQ(params.at("connect_timeout"), "30");
}

// Tests database config construction with connection string
TEST_F(DatabaseConfigTest, ConstructorWithConnectionString) {
    DatabaseConfig config(connection_string_config_yaml_);

    EXPECT_EQ(config.type(), DatabaseConfig::Type::MySQL);
    EXPECT_EQ(config.connection_string(), "mysql://user:pass@localhost:3306/omop_db");
}

// Tests default port assignment for different database types
TEST_F(DatabaseConfigTest, DefaultPortsForDifferentDatabaseTypes) {
    std::vector<std::pair<std::string, std::pair<DatabaseConfig::Type, int>>> db_types = {
        {"postgresql", {DatabaseConfig::Type::PostgreSQL, 5432}},
        {"mysql", {DatabaseConfig::Type::MySQL, 3306}},
        {"mssql", {DatabaseConfig::Type::MSSQL, 1433}},
        {"oracle", {DatabaseConfig::Type::Oracle, 1521}}
    };

    for (const auto& [type_str, type_info] : db_types) {
        YAML::Node yaml = YAML::Load(std::format(R"(
            type: "{}"
            host: "localhost"
            database: "test_db"
            username: "user"
        )", type_str));

        DatabaseConfig config(yaml);
        EXPECT_EQ(config.type(), type_info.first) << "Failed for type: " << type_str;
        EXPECT_EQ(config.port(), type_info.second) << "Failed for type: " << type_str;
    }
}

// Tests that constructor throws exception when database type is invalid
TEST_F(DatabaseConfigTest, ConstructorThrowsOnInvalidType) {
    YAML::Node invalid_yaml = YAML::Load(R"(
        type: "invalid_db_type"
        host: "localhost"
        database: "test_db"
        username: "user"
    )");

    EXPECT_THROW(DatabaseConfig config(invalid_yaml), ConfigurationException);
}

// Test fixture for ConfigurationManager tests
class ConfigurationManagerTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create temporary directory for file tests
        temp_dir_ = std::filesystem::temp_directory_path() / "omop_config_test";
        std::filesystem::create_directories(temp_dir_);
        
        // Create a temporary YAML file for testing
        test_config_path = "test_config.yaml";
        std::ofstream file(test_config_path);
        file << R"(
source_database:
  type: postgresql
  host: localhost
  port: 5432
  database: source_db
  username: user
  password: pass

target_database:
  type: mysql
  host: localhost
  port: 3306
  database: target_db
  username: user
  password: pass

table_mappings:
  person:
    source_table: patients
    target_table: person
    transformations:
      - source_column: patient_id
        target_column: person_id
        type: direct
      - source_column: birth_date
        target_column: year_of_birth
        type: date_transform
        date_format: "%Y"

etl_settings:
  batch_size: 5000
  parallel_workers: 8
  validation_mode: strict
  error_threshold: 0.05

vocabulary_mappings:
  gender:
    M: 8507
    F: 8532
)";
        file.close();
    }

    void TearDown() override {
        // Clean up temporary files
        std::filesystem::remove_all(temp_dir_);
        std::filesystem::remove(test_config_path);
    }

    std::filesystem::path temp_dir_;
    std::string test_config_path;

    std::string createSimpleConfig() {
        return "source_database:\n"
               "  type: postgresql\n"
               "  host: localhost\n"
               "  database: source_db\n"
               "  username: user\n"
               "target_database:\n"
               "  type: postgresql\n"
               "  host: localhost\n"
               "  database: omop_cdm\n"
               "  username: omop_user\n"
               "table_mappings:\n"
               "  patients:\n"
               "    source_table: patients\n"
               "    target_table: person\n"
               "    transformations:\n"
               "      - source_column: patient_id\n"
               "        target_column: person_id\n"
               "        type: direct\n";
    }

    void writeConfigFile(const std::string& content, const std::string& filename = "config.yaml") {
        auto filepath = temp_dir_ / filename;
        std::ofstream file(filepath);
        file << content;
        file.close();
    }
};

// Tests loading configuration from YAML string
TEST_F(ConfigurationManagerTest, LoadConfigFromString) {
    ConfigurationManager manager;
    std::string config_content = createSimpleConfig();

    EXPECT_FALSE(manager.is_loaded());
    EXPECT_NO_THROW(manager.load_config_from_string(config_content));
    EXPECT_TRUE(manager.is_loaded());
}

// Tests loading configuration from YAML file
TEST_F(ConfigurationManagerTest, LoadConfigFromFile) {
    ConfigurationManager manager;
    std::string config_content = createSimpleConfig();
    writeConfigFile(config_content);

    auto config_file = temp_dir_ / "config.yaml";
    EXPECT_NO_THROW(manager.load_config(config_file.string()));
    EXPECT_TRUE(manager.is_loaded());
}

// Tests that loading throws exception when file doesn't exist
TEST_F(ConfigurationManagerTest, LoadConfigThrowsOnInvalidFile) {
    ConfigurationManager manager;
    EXPECT_THROW(manager.load_config("/nonexistent/file.yaml"), ConfigurationException);
}

// Tests that loading throws exception when YAML is malformed
TEST_F(ConfigurationManagerTest, LoadConfigThrowsOnInvalidYaml) {
    ConfigurationManager manager;
    std::string invalid_yaml = "invalid: yaml: content: [unclosed";
    EXPECT_THROW(manager.load_config_from_string(invalid_yaml), ConfigurationException);
}

// Tests that source and target database configurations are parsed correctly
TEST_F(ConfigurationManagerTest, DatabaseConfigurationsParsedCorrectly) {
    ConfigurationManager manager;
    std::string config_content = createSimpleConfig();
    manager.load_config_from_string(config_content);

    const auto& source_db = manager.get_source_db();
    EXPECT_EQ(source_db.type(), DatabaseConfig::Type::PostgreSQL);
    EXPECT_EQ(source_db.host(), "localhost");
    EXPECT_EQ(source_db.database(), "source_db");
    EXPECT_EQ(source_db.username(), "user");

    const auto& target_db = manager.get_target_db();
    EXPECT_EQ(target_db.type(), DatabaseConfig::Type::PostgreSQL);
    EXPECT_EQ(target_db.database(), "omop_cdm");
    EXPECT_EQ(target_db.username(), "omop_user");
}

// Tests that table mappings and transformations are parsed correctly
TEST_F(ConfigurationManagerTest, TableMappingsParsedCorrectly) {
    ConfigurationManager manager;
    std::string config_content = createSimpleConfig();
    manager.load_config_from_string(config_content);

    auto patients_mapping = manager.get_table_mapping("patients");
    ASSERT_TRUE(patients_mapping.has_value());

    EXPECT_EQ(patients_mapping->source_table(), "patients");
    EXPECT_EQ(patients_mapping->target_table(), "person");
    EXPECT_EQ(patients_mapping->transformations().size(), 1);

    const auto& transformations = patients_mapping->transformations();
    EXPECT_EQ(transformations[0].source_column(), "patient_id");
    EXPECT_EQ(transformations[0].target_column(), "person_id");
    EXPECT_EQ(transformations[0].type(), TransformationRule::Type::Direct);
}

// Tests that get_table_mapping returns nullopt for non-existent table
TEST_F(ConfigurationManagerTest, GetTableMappingReturnsNulloptForNonexistent) {
    ConfigurationManager manager;
    std::string config_content = createSimpleConfig();
    manager.load_config_from_string(config_content);

    auto mapping = manager.get_table_mapping("nonexistent_table");
    EXPECT_FALSE(mapping.has_value());
}

// Tests that get_all_mappings returns all configured table mappings
TEST_F(ConfigurationManagerTest, GetAllMappingsReturnsAllTables) {
    ConfigurationManager manager;
    std::string config_content = createSimpleConfig();
    manager.load_config_from_string(config_content);

    const auto& all_mappings = manager.get_all_mappings();
    EXPECT_EQ(all_mappings.size(), 1);
    EXPECT_TRUE(all_mappings.find("patients") != all_mappings.end());
}

// Test Config singleton functionality
// Tests that Config singleton returns the same instance
TEST_F(ConfigurationManagerTest, ConfigSingleton) {
    auto& instance1 = Config::instance();
    auto& instance2 = Config::instance();

    // Should be the same instance
    EXPECT_EQ(&instance1, &instance2);

    // Test that it works as expected
    std::string config_content = createSimpleConfig();
    instance1.load_config_from_string(config_content);

    EXPECT_TRUE(instance2.is_loaded());

    auto mapping = instance2.get_table_mapping("patients");
    EXPECT_TRUE(mapping.has_value());
}

// Tests loading configuration from the test config file created in SetUp
TEST_F(ConfigurationManagerTest, LoadConfigFromFileWithTestConfig) {
    ConfigurationManager config;
    EXPECT_NO_THROW(config.load_config(test_config_path));
    EXPECT_TRUE(config.is_loaded());
}

// Tests loading configuration from complex YAML string with multiple sections
TEST_F(ConfigurationManagerTest, LoadConfigFromStringWithComplexConfig) {
    ConfigurationManager config;
    std::string yaml_content = R"(
source_database:
  type: postgresql
  host: localhost
  database: test
  username: user

target_database:
  type: mysql
  host: localhost
  database: test
  username: user

table_mappings:
  test_table:
    source_table: src
    target_table: tgt
    transformations:
      - source_column: id
        target_column: id
)";
    
    EXPECT_NO_THROW(config.load_config_from_string(yaml_content));
    EXPECT_TRUE(config.is_loaded());
}

// Tests retrieving source database configuration details
TEST_F(ConfigurationManagerTest, GetSourceDatabase) {
    ConfigurationManager config;
    config.load_config(test_config_path);
    
    const auto& source_db = config.get_source_db();
    EXPECT_EQ(source_db.type(), DatabaseConfig::Type::PostgreSQL);
    EXPECT_EQ(source_db.host(), "localhost");
    EXPECT_EQ(source_db.port(), 5432);
    EXPECT_EQ(source_db.database(), "source_db");
}

// Tests retrieving target database configuration details
TEST_F(ConfigurationManagerTest, GetTargetDatabase) {
    ConfigurationManager config;
    config.load_config(test_config_path);
    
    const auto& target_db = config.get_target_db();
    EXPECT_EQ(target_db.type(), DatabaseConfig::Type::MySQL);
    EXPECT_EQ(target_db.host(), "localhost");
    EXPECT_EQ(target_db.port(), 3306);
}

// Tests retrieving table mapping configuration with transformations
TEST_F(ConfigurationManagerTest, GetTableMapping) {
    ConfigurationManager config;
    config.load_config(test_config_path);
    
    auto mapping = config.get_table_mapping("person");
    ASSERT_TRUE(mapping.has_value());
    EXPECT_EQ(mapping->source_table(), "patients");
    EXPECT_EQ(mapping->target_table(), "person");
    EXPECT_EQ(mapping->transformations().size(), 2);
}

// Tests that get_table_mapping returns nullopt for non-existent table mapping
TEST_F(ConfigurationManagerTest, GetNonExistentTableMapping) {
    ConfigurationManager config;
    config.load_config(test_config_path);
    
    auto mapping = config.get_table_mapping("nonexistent");
    EXPECT_FALSE(mapping.has_value());
}

// Tests retrieving configuration values using dot notation path
TEST_F(ConfigurationManagerTest, GetValueWithDotNotation) {
    ConfigurationManager config;
    config.load_config(test_config_path);
    
    auto value = config.get_value("etl_settings.batch_size");
    ASSERT_TRUE(value.has_value());
    EXPECT_EQ(value->as<int>(), 5000);
}

// Tests retrieving configuration values with default fallback
TEST_F(ConfigurationManagerTest, GetValueOrWithDefault) {
    ConfigurationManager config;
    config.load_config(test_config_path);
    
    int batch_size = config.get_value_or<int>("etl_settings.batch_size", 1000);
    EXPECT_EQ(batch_size, 5000);
    
    int missing_value = config.get_value_or<int>("etl_settings.missing", 999);
    EXPECT_EQ(missing_value, 999);
}

// Tests that transformation rule types are correctly parsed from configuration
TEST_F(ConfigurationManagerTest, TransformationRuleTypes) {
    ConfigurationManager config;
    config.load_config(test_config_path);
    
    auto mapping = config.get_table_mapping("person");
    ASSERT_TRUE(mapping.has_value());
    
    const auto& transformations = mapping->transformations();
    EXPECT_EQ(transformations[0].type(), TransformationRule::Type::Direct);
    EXPECT_EQ(transformations[1].type(), TransformationRule::Type::DateTransform);
}

// Tests that Config singleton pattern returns same instance
TEST_F(ConfigurationManagerTest, SingletonPattern) {
    auto& instance1 = Config::instance();
    auto& instance2 = Config::instance();
    
    EXPECT_EQ(&instance1, &instance2);
}

// Tests that invalid configuration throws ConfigurationException
TEST_F(ConfigurationManagerTest, InvalidConfigurationThrows) {
    ConfigurationManager config;
    
    // Missing required sections
    std::string invalid_yaml = R"(
source_database:
  type: postgresql
  host: localhost
)";
    
    EXPECT_THROW(config.load_config_from_string(invalid_yaml), ConfigurationException);
}

// Tests database configuration with connection string format
TEST_F(ConfigurationManagerTest, DatabaseConfigWithConnectionString) {
    std::string yaml = R"(
source_database:
  type: postgresql
  connection_string: postgresql://user:pass@localhost:5432/dbname

target_database:
  type: mysql
  host: localhost
  database: test
  username: user

table_mappings:
  test:
    source_table: src
    target_table: tgt
    transformations:
      - source_column: id
        target_column: id
)";
    
    ConfigurationManager config;
    config.load_config_from_string(yaml);
    
    const auto& source_db = config.get_source_db();
    EXPECT_EQ(source_db.connection_string(), "postgresql://user:pass@localhost:5432/dbname");
}

} // namespace omop::common::test
```